import json
import unittest

import yaml

from bin.docGen import generate_openapi_html_documentation
from bin.genOpenapi import gen_file
from getDocApi import build_openapi_response
from utils.mocking import mock_environnement


class TestGetDocAPi(unittest.TestCase):
    @mock_environnement
    def test_genFile_format_and_version(self):
        env_name = "test_env"
        data_genfile = gen_file(env_name, None, "")
        self.assertIsInstance(data_genfile, dict, "Generated data is not a valid dict")
        self.assertEqual(
            data_genfile.get("info", {}).get("version"),
            env_name,
            f"Version does not match {env_name}",
        )

    def test_genfile_with_only(self):
        only_params = ["/utilisateurs"]
        only_data_genfile = gen_file("test_env", only_params, "")
        paths = only_data_genfile["paths"]
        for path in paths:
            self.assertTrue(
                path.startswith(only_params[0]),
                f"Path {path} does not start with {only_params[0]}",
            )

    def test_build_openapi_response(self):
        _generate_test_file()
        for headers_accept in ["application/json", "*/*", "text/html", "text/yaml"]:
            response_buildOpenApi = build_openapi_response(None, headers_accept)
            self.assertEqual(response_buildOpenApi["statusCode"], 200)
            if headers_accept in ["application/json", "*/*"]:
                try:
                    decoded_body = json.loads(response_buildOpenApi["body"])
                    self.assertIsInstance(decoded_body, dict, "The decoded body is not a dictionary")
                except json.JSONDecodeError:
                    self.fail("Failed to decode the response body as JSON")
            elif headers_accept == "text/html":
                self.assertIsInstance(
                    response_buildOpenApi["body"],
                    str,
                    "The response body is not a string",
                )
                striped_body_html = response_buildOpenApi["body"].strip()
                self.assertTrue(
                    striped_body_html.startswith("<html>"),
                    "The response body is not HTML",
                )
            elif headers_accept == "text/yaml":
                self.assertIsInstance(
                    response_buildOpenApi["body"],
                    str,
                    "The response body is not a string",
                )
                striped_body_yaml = response_buildOpenApi["body"].strip()
                openapi_version = "openapi: 3.1.0"
                self.assertTrue(
                    striped_body_yaml.startswith(openapi_version),
                    "The response body is not YAML or the openapi version as changed",
                )


def _generate_test_file():
    openapi = gen_file("test", None, "")
    with open("resources/myresaapi_openapi_doc.json", "w+", encoding="utf8") as f:
        f.write(json.dumps(openapi, indent=2, separators=(",", ": "), ensure_ascii=False))
    with open("resources/myresaapi_openapi_doc.yaml", "w+", encoding="utf8") as f:
        f.write(yaml.safe_dump(openapi, sort_keys=False))  # noqa: F821
    with open("resources/documentation.html", "w+", encoding="utf8") as f:
        f.write(generate_openapi_html_documentation(json.dumps(openapi)))
