import json
import os

import yaml

from bin.docGen import generate_openapi_html_documentation
from bin.genOpenapi import gen_file
from utils.aws_handler_decorator import aws_lambda_handler


@aws_lambda_handler
def handler(event, context):
    query_string_params = event.get("queryStringParameters", {})
    query_string_params = {} if not query_string_params else query_string_params
    sub_path = query_string_params.get("SubPath")

    headers_accept = event.get("headers", {}).get("Accept", "application/json")
    if "," in headers_accept:
        headers_accept = headers_accept.split(",")[0]
    return build_openapi_response(sub_path, headers_accept)


def build_openapi_response(sub_path, headers_accept):
    if sub_path:
        openapi = gen_file(os.environ["API_VERSION"], [sub_path], os.environ["STAGE"])
        json_openapi = json.dumps(openapi, indent=2, separators=(", ", ": "), ensure_ascii=False)
        data_return = {
            "application/json": json_openapi,
            "*/*": json_openapi,
            "text/html": generate_openapi_html_documentation(json_openapi),
            "text/yaml": (yaml.safe_dump(openapi, sort_keys=False)),
        }.get(headers_accept, json_openapi)
    else:
        file = {
            "application/json": "myresaapi_openapi_doc.json",
            "*/*": "myresaapi_openapi_doc.json",
            "text/html": "documentation.html",
            "text/yaml": "myresaapi_openapi_doc.yaml",
        }.get(headers_accept, "myresaapi_openapi_doc.json")

        file_path = os.path.join("./resources", file)
        data_return = read_file_content(file_path)

    return {
        "statusCode": 200,
        "headers": {"Content-Type": headers_accept},
        "body": data_return,
    }


def read_file_content(file_path):
    with open(file_path, "r", encoding="utf-8") as file:
        return file.read()
