"""
This module is responsible for updating user information in a DynamoDB table using boto3 and SAP backend.

It utilizes helper functions from the `utils` package to fetch, log, and update user details. The main functionalities include:
- Retrieving an existing user from the DynamoDB table based on a unique identifier (uid).
- Updating user information and sending them to SAP then inserting it back into the DynamoDB table.
- Optionally handling specific attributes like 'Commune' and assigning appropriate IDs before updating.

Environment variables (configured bellow):
- `DYNAMODB`: The name of the DynamoDB table to use.
- `PASSTHROUGH_LAMBDA_ARN`: The ARN of the AWS Lambda passthrough function.
- `API_VERSION`: The version of the API being used.

Constants (configured bellow):
- `base_url`: The base URL of the API.
- `api_key`: The API key for authentication.
- `ldap_secret`: The secret key for accessing the LDAP.

Functions:
- get_existing_user(uid): Retrieves an existing user item from the DynamoDB table.
- update_user(user): Updates and inserts the user information into the DynamoDB table.

Execution:
- When executed as the main module, it updates a predefined set of users listed in `users_to_update`.
"""

import os

import boto3

from utils.dict_utils import get
from utils.ldap_utils import LDAP
from utils.log_utils import log_err, log_info
from utils.sap_user import sap_edit_user

os.environ["DYNAMODB"] = "MyResaUser_qta"
base_url = "https://api-acceptance.resa.be/latest"
api_key = "uAvIGTqpdgexRie3DHW0hXNkECc0GuLH"
os.environ["PASSTHROUGH_LAMBDA_ARN"] = "arn:aws:lambda:eu-west-1:************:function:passThrough-qta"
ldap_secret = "MyResaAPI/ActiveDirectory/qta"

# os.environ["DYNAMODB"] = "MyResaUser_qla"
# base_url = "https://api-qla.resa.be/latest"
# api_key = "xJtC6amoLA2Y45pekkUKjoQNEAmGtLb8"
# os.environ["PASSTHROUGH_LAMBDA_ARN"] = "arn:aws:lambda:eu-west-1:************:function:passThrough-qla"
# ldap_secret = "MyResaAPI/ActiveDirectory/QLA"

# os.environ["DYNAMODB"] = "MyResaUser_prd"
# base_url = "https://api.resa.be/latest"
# api_key = "66qvACMYHyqFwrv6aEMd2JMNkK3PKtYn"
# os.environ["PASSTHROUGH_LAMBDA_ARN"] = "arn:aws:lambda:eu-west-1:************:function:passThrough-production"
# ldap_secret = "MyResaAPI/ActiveDirectory/prd"

os.environ["API_VERSION"] = "latest"
dynamodb = boto3.resource("dynamodb")
table = dynamodb.Table(os.environ["DYNAMODB"])

users_to_update = [
    {
        "uid": "Tes.TestNom.bCSuW",
        # Place here other information to update
    }
]


def get_existing_user(uid: str) -> dict:
    """
    Parameters
    ----------
    uid : str
        The unique identifier for the user.

    Returns
    -------
    dict or None
        The existing user item if found, otherwise None.
    """
    resp = table.get_item(Key={"uid": uid})
    return resp.get("Item")


def update_user(user: dict):
    """
    Parameters
    ----------
    user : dict
        The user dictionary that contains user information, including optionally a "Commune" key with a nested dictionary.

    Notes
    -----
    If the user dictionary contains a "Commune" key and the nested dictionary contains an "Id" key, the value of this "Id" key will be assigned to the "commune_id" key in the user dictionary.

    The function will then call `sap_edit_user` passing the updated user dictionary and store the user dictionary in a table using the `put_item` method.
    """
    if get(user, "Commune", {}).get("Id"):
        user["commune_id"] = user["Commune"]["Id"]
    sap_edit_user(user)
    table.put_item(Item=user)


def update_login_info(uid: str, new_email: str):
    """
    Parameters
    ----------
    uid : str
        The unique identifier for the user whose email is to be updated.
    new_email : str
        The new email address to be set for the user.
    """
    ldap = LDAP.loadFromSecret(ldap_secret)

    ldap.setUserAttribute(uid, "mail", new_email)
    ldap.setUserAttribute(uid, "userPrincipalName", new_email)


if __name__ == "__main__":
    for user_info in users_to_update:
        user = get_existing_user(user_info["uid"])

        if not user:
            log_err(f"User {user_info['uid']} not found")
        else:
            update_user({**user, **user_info})
            if user_info.get("email"):
                update_login_info(user["uid"], user_info["email"])
            log_info(f"{user['uid']} / {user.get('bp')} / {user.get('email')} => {user_info.get('email', user.get('email'))}")
