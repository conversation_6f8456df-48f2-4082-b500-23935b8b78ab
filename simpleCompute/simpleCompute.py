#!/usr/bin/env python3
import json
import os
import sys
from importlib import import_module
from typing import Any

from utils.aws_handler_decorator import aws_lambda_handler
from utils.aws_utils import load_s3_file

func_dir_name = "simpleFunction"


def import_(elt):
    part = elt.split(":")
    mod = import_module(part[0], func_dir_name)
    return getattr(mod, part[1])


@aws_lambda_handler
def handler(event: dict, context: dict) -> Any:
    is_api_call = "httpMethod" in event and "methodArn" not in event

    if is_api_call:
        path = event["resource"]
        method = event["httpMethod"].upper()
    else:
        path = "tasks"
        method = event.get("task")

    linking_file = json.loads(load_s3_file(os.environ["MAPPING_BUCKET_NAME"], os.environ["MAPPING_BUCKET_FILE"]))  # pylint: disable=no-member
    linking = linking_file[path][method]

    current_dir = os.path.dirname(os.path.abspath(__file__))
    sys.path.append(os.path.join(current_dir, "simpleFunction"))
    return import_(f"{linking['function']}")(event, linking)
