import os
import pickle
import tempfile
import zipfile
from datetime import datetime, timedelta

from utils.api import api_caller
from utils.aws_handler_decorator import auth_handler, commune_handler
from utils.aws_utils import create_presigned_url, list_s3_files, load_s3_file, start_state_machine, upload_s3_file
from utils.log_utils import log_info_json
from utils.models.user import User
from utils.smart_conso_utils import data_to_csv, process_data_response

BUCKET = f"{os.environ['BUCKET']}-temp-storage"
BUCKET_DIR = "exports_ean_index"


@auth_handler
def start_export(event: dict, linking: dict, logged_user: User) -> dict:
    """
    Start the export of EAN data for a given user.

    Parameters
    ----------
    event : dict
        The Lambda event containing request data.
    linking : dict
        Configuration linking data.
    logged_user : User
        The authenticated user requesting the export.

    Returns
    -------
    dict
        Response containing status code and export ID.

    """
    eans = [
        {
            "ean": ean_obj.ean,
            "contract_date": [(contract.ctr_from, contract.ctr_to) for contract in ean_obj.contract],
        }
        for ean_obj in logged_user.ean
        if ean_obj.energy == "elec" and ean_obj.smart
    ]

    response = start_state_machine(
        "ean_export_workflow",
        {
            "email": logged_user.email,
            "eans": eans,
            "start_date": (datetime.now() - timedelta(days=366)).strftime("%Y%m%d"),
            "end_date": datetime.now().strftime("%Y%m%d"),
        },
    )

    log_info_json(response)

    return {
        "statusCode": 200,
        "body": {"ExportId": response["executionArn"].split(":")[-1]},
    }


@commune_handler(roles=["INDEX_CPT_CONSU", "INDEX_CPT_GERER"])
def start_export_communes(event: dict, linking: dict, logged_user: User) -> dict:
    """
    Start the export of EAN data for a given user's commune.

    Parameters
    ----------
    event : dict
        The Lambda event containing request data and headers.
    linking : dict
        Configuration linking data.
    logged_user : User
        The authenticated user with commune access rights.

    Returns
    -------
    dict
        Response containing status code and export ID.

    Notes
    -----
    This function requires specific roles: INDEX_CPT_CONSU or INDEX_CPT_GERER.
    It exports data for the last 366 days for all smart electric meters
    in the user's commune.

    """
    contract_date = [
        (
            (datetime.now() - timedelta(days=366)).strftime("%Y%m%d"),
            datetime.now().strftime("%Y%m%d"),
        ),
    ]

    # Get ean from commune
    res = api_caller(method="get", path="/communes/ean", headers=event["headers"], params={"Smart": "true"})
    eans = [
        {
            "ean": ean["Ean"],
            "contract_date": contract_date,
        }
        for ean_address in res
        for ean in ean_address["Eans"]
        if ean["Type"] == "Elec"
    ]

    response = start_state_machine(
        "ean_export_workflow",
        {
            "email": logged_user.email,
            "eans": eans,
            "start_date": (datetime.now() - timedelta(days=366)).strftime("%Y%m%d"),
            "end_date": datetime.now().strftime("%Y%m%d"),
        },
    )

    log_info_json(response)

    return {
        "statusCode": 200,
        "body": {"ExportId": response["executionArn"].split(":")[-1]},
    }


def raw_data_to_csv_by_ean(event: dict, linking: dict) -> dict:
    """Fetch raw data from S3 for a given EAN and meter and export it to a CSV file back to S3."""
    export_id = event["export_id"]
    ean_cpt = event["ean_cpt"]
    start_date = event["start_date"]
    end_date = event["end_date"]
    type_params = "HOURLY"

    # Get the raw data from S3
    raw_data = load_s3_file(bucket_name=BUCKET, object_name=f"{BUCKET_DIR}/{export_id}/raw/{ean_cpt}.raw", no_cache=True, write_cache=False)
    data = pickle.loads(raw_data)

    # Process the data and export it to CSV
    processed_data = process_data_response(data, "elec", start_date, end_date, type_params)
    csv_content = data_to_csv(processed_data)
    upload_s3_file(
        bucket_name=BUCKET,
        object_name=f"{BUCKET_DIR}/{export_id}/csv/{ean_cpt}.csv",
        data=csv_content,
    )


def zip_and_notify(event: dict, linking: dict) -> dict:
    """
    Zip all the CSV files from S3 for a given export and notify the user.

    Parameters
    ----------
    event : dict
        Event data containing email and export_id.
    linking : dict
        Configuration linking data.

    Returns
    -------
    dict
        Dictionary containing export ID, ZIP key, and download URL.

    Notes
    -----
    This function uses tempfile.mkstemp to create a temporary ZIP file
    to avoid memory issues when processing large datasets. The temporary
    file is automatically cleaned up after upload to S3.

    """
    email = event["email"]
    export_id = event["export_id"]

    # Get files info from S3 for this export
    files = list_s3_files(
        bucket_name=BUCKET,
        prefix=f"{BUCKET_DIR}/{export_id}/csv/",
    )

    # Use a temporary file to build the zip (to not use too much memory)
    with tempfile.NamedTemporaryFile() as temp_zip:
        with zipfile.ZipFile(temp_zip, "w", zipfile.ZIP_DEFLATED) as zip_file:
            for obj in files:
                key = obj.key
                file_name = key.split("/")[-1]

                # Get the CSV data
                csv_content = load_s3_file(bucket_name=BUCKET, object_name=key, no_cache=True, write_cache=False)

                # Add to zip
                zip_file.writestr(file_name, csv_content)

        # Upload the zip file to S3
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        zip_key = f"{BUCKET_DIR}/{export_id}/completed/export_{export_id}_{timestamp}.zip"

        temp_zip.seek(0)  # Reset the file pointer to the beginning before reading it
        upload_s3_file(bucket_name=BUCKET, object_name=zip_key, data=temp_zip.read())

    # Generate a presigned URL for the zip file and send a mail notification
    presigned_url = create_presigned_url(
        BUCKET,
        zip_key,
        expiration=172800,  # 2 days
    )

    send_notification(export_id, presigned_url, email)

    return {
        "ExportId": export_id,
        "ZipKey": zip_key,
        "DownloadUrl": presigned_url,
    }


def send_notification(export_id: str, download_url: str, email: str) -> None:
    """
    Send a notification to the user that the export is completed.

    Parameters
    ----------
    export_id : str
        The unique identifier for the export.
    download_url : str
        The presigned URL for downloading the export ZIP file.
    email : str
        The email address to send the notification to.

    Notes
    -----
    Sends an HTML email notification with a download link that is valid
    for 24 hours. Uses the envMessage API for email delivery.

    """
    subject = f"Export EAN {export_id} completed"
    body_html = f"""
    <html>
    <head></head>
    <body>
        <h1>Export EAN completed</h1>
        <p>Your export with ID {export_id} has been completed.</p>
        <p>You can download the results using the following link (valid for 24 hours):</p>
        <p><a href="{download_url}">Download Export Results</a></p>
    </body>
    </html>
    """

    api_caller(
        method="post",
        path="/envMessage",
        body={
            "Langue": os.environ["LANG"],
            "Header": {"TEMPLATE_ID": "EMPTY_RAW_HTML", "EMAIL": email},
            "subjects": subject,
            "raw_html": body_html,
        },
    )
