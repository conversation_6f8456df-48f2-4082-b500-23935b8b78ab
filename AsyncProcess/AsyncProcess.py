import os
from datetime import datetime

from boto3.dynamodb.conditions import Key
from requests import request

from utils.aws_handler_decorator import aws_lambda_handler
from utils.aws_utils import get_dynamodb_table
from utils.dict_utils import convert_float_to_decimal, get


@aws_lambda_handler
def handler(event, context):
    table = get_dynamodb_table(os.environ["AsynchroneProcessesTable"])
    process_id = get(event, "process_id")

    try:
        process_data = table.query(KeyConditionExpression=Key("id").eq(process_id))["Items"][0]
        input_ = process_data["input"]

        result = request(
            method=input_["method"].upper(),
            url=os.environ["API_URL"] + input_["path"].format(**input_["pathParameters"]),
            params=input_["queryStringParameters"],
            data=input_["body"],
            headers=input_["headers"],
        )

        try:
            result_body = result.json()
        except ValueError:
            result_body = result.text

        table.update_item(
            Key={"id": process_id},
            UpdateExpression="SET #content = :content, #headers = :headers, #finishAt = :finishAt, #status = :status, #statusCode = :statusCode",
            ExpressionAttributeNames={
                "#content": "content",
                "#headers": "headers",
                "#status": "status",
                "#finishAt": "finishAt",
                "#statusCode": "statusCode",
            },
            ExpressionAttributeValues=convert_float_to_decimal(
                {
                    ":content": result_body,
                    ":headers": result.headers,
                    ":statusCode": result.status_code,
                    ":status": "SUCCEED",
                    ":finishAt": int(datetime.now().timestamp() * 1000),
                },
            ),
        )
    except Exception as e:
        table.update_item(
            Key={"id": process_id},
            UpdateExpression="SET #status = :status, #finishAt = :finishAt, #error_message = :error_message",
            ExpressionAttributeNames={
                "#status": "status",
                "#finishAt": "finishAt",
                "#error_message": "error_message",
            },
            ExpressionAttributeValues={
                ":status": "FAILED",
                ":error_message": str(e),
                ":finishAt": int(datetime.now().timestamp() * 1000),
            },
        )
        raise e
