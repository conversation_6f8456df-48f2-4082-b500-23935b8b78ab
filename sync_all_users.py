import os
import traceback
import warnings
from datetime import datetime
from io import String<PERSON>
from multiprocessing.pool import Thr<PERSON><PERSON><PERSON>

import boto3
from boto3.dynamodb.conditions import Key, Attr
from tools.scripts.sync_user import sync_user
from tqdm import tqdm

from utils.aws_utils import scan_all_generator
from utils.dict_utils import get

dynamodb = boto3.resource("dynamodb")
table = dynamodb.Table(os.environ["DYNAMODB"])


# From https://stackoverflow.com/questions/11130156/suppress-stdout-stderr-print-from-python-functions
# Define a context manager to suppress stdout and stderr.
class suppress_stdout_stderr(object):
    """
    A context manager for doing a "deep suppression" of stdout and stderr in
    Python, i.e. will suppress all print, even if the print originates in a
    compiled C/Fortran sub-function.
       This will not suppress raised exceptions, since exceptions are printed
    to stderr just before a script exits, and after the context manager has
    exited (at least, I think that is why it lets exceptions through).
    """

    def __init__(self):
        # Open a pair of null files
        self.null_fds = [os.open(os.devnull, os.O_RDWR) for x in range(2)]
        # Save the actual stdout (1) and stderr (2) file descriptors.
        self.save_fds = [os.dup(1), os.dup(2)]

    def __enter__(self):
        # Assign the null pointers to stdout and stderr.
        os.dup2(self.null_fds[0], 1)
        os.dup2(self.null_fds[1], 2)

    def __exit__(self, *_):
        # Re-assign the real stdout/stderr back to (1) and (2)
        os.dup2(self.save_fds[0], 1)
        os.dup2(self.save_fds[1], 2)
        # Close all file descriptors
        for fd in self.null_fds + self.save_fds:
            os.close(fd)


def fetch_users_uid(log_file="users.tmp"):
    table_info = boto3.client("dynamodb").describe_table(TableName=os.environ["DYNAMODB"])
    user_count = table_info["Table"]["ItemCount"]

    users_gen = scan_all_generator(
        os.environ["DYNAMODB"],
        {
            "ProjectionExpression": "uid, firstname, lastname, ean, dossiers, bp, email, phone, adresse",
            "FilterExpression": ~(
                Key("uid").begins_with("ghost_")
                | Key("uid").begins_with("Tes.User.")
                | Key("uid").begins_with("Tes.Test.")
                | Key("uid").eq("DXC.Test1.1RMDk")  # Exclude PPP used by Atrias
                | Key("uid").eq("DXC.Test2.cHmO4")  # Exclude PPP used by Atrias
            )
            & Attr("valide").eq(True)
            & Attr("bp").exists(),
        },
    )

    with open(f"sync_all_users_output/{log_file}", "w") as users_save, tqdm(total=user_count) as progress:
        users_save.write("uid;firstname;lastname;bp;email;phone;ean;dossiers;adresse\n")
        for user in users_gen:
            users_save.write(
                f"{user['uid']};{user['firstname']};{user['lastname']};{user['bp']};{user['email']};{user.get('phone')};{[str(item.get('ean')) for item in get(user, 'ean', [])]};{[str(item.get('id')) for item in get(user, 'dossiers', [])]};{user['adresse']}\n"
            )
            progress.update()


def sync_users():
    global synced_users_eans
    global synced_users_dossiers

    uids = []
    synced_users_eans = 0
    synced_users_dossiers = 0
    with open("sync_all_users_output/users.tmp", "r") as users:
        _uids = [user.split(";")[0] for user in list(users)[1:]]
        for uid in _uids:
            uids.append(uid.strip())

    with tqdm(total=len(uids)) as progress, open("sync_all_users_output/output.log", "w") as logs:
        logs.write(f"***** START * {datetime.now().isoformat()} *****\n")
        thread_pool = ThreadPool(processes=16)

        def handle(uid):
            warnings.filterwarnings("ignore")
            log = StringIO()
            try:
                log.write(f"=== Syncing : {uid} : ")
                with suppress_stdout_stderr():
                    sync_user(uid)
            except Exception:
                log.write(f"ERROR : {uid} === {traceback.format_exc()}\n")
            logs.write(log.getvalue() + "\n")
            logs.flush()
            progress.update()

        thread_pool.map(handle, uids)

        logs.write(f"***** FINISH * {datetime.now().isoformat()} *****\n")

    return {
        "synced_users_eans": synced_users_eans,
        "synced_users_dossiers": synced_users_dossiers,
    }


if __name__ == "__main__":
    # Create output dir
    if not os.path.exists("sync_all_users_output"):
        os.makedirs("sync_all_users_output")

    # Store uids to file then retrieve them, so if something fail, we don't need to fetch again uids from DynamoDB
    print("----- Get uids -----")
    fetch_users_uid()

    print("----- Sync -----")
    # Sync users
    result_sync_users = sync_users()
