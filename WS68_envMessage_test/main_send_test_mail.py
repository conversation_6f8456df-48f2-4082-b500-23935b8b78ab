import argparse
import concurrent.futures
from concurrent.futures import ThreadPoolExecutor
from multiprocessing import Process
from time import sleep

from Scripts.WS68_envMessage_test.test_mail_processor import TestMailProcessor
from tools.local_flask.gateway import start_local_flask
from utils.aws_utils import get_dynamodb_table


def fetch_table_data(template_table):
    response = template_table.scan()
    items = response["Items"]
    while "LastEvaluatedKey" in response:
        response = template_table.scan(ExclusiveStartKey=response["LastEvaluatedKey"])
        items.extend(response["Items"])

    return {item["Id"]: item.get("TestData") for item in items}


def multithreaded_processing(templates_test_data):
    results = []
    with Thread<PERSON>oolExecutor(max_workers=10) as executor:
        futures = []
        for template_id, test_data in templates_test_data.items():
            futures.append(executor.submit(TestMailProcessor.process, template_id, test_data))
        for future in concurrent.futures.as_completed(futures):
            try:
                results.append(future.result())
            except Exception as exc:
                print(f"Generated an exception: {exc}")
    for result in results:
        template_id = result["template_id"]
        response = result["response"]
        print(f"Template {template_id}")
        for language, value in response.items():
            print(f"    {language} : {value}")


if __name__ == "__main__":
    # Parse command line arguments
    parser = argparse.ArgumentParser()
    parser.add_argument("--only", nargs="*", help="List of template IDs to be tested only")
    args = parser.parse_args()
    only = args.only

    # Fetch templates test data
    template_table = get_dynamodb_table("WS68_TEMPLATE_QTA")
    all_templates = fetch_table_data(template_table)

    # Filter templates based on CLI provided IDs
    templates = {id_: all_templates[id_] for id_ in only} if only else all_templates

    # Send tests
    flask = Process(target=start_local_flask, args=(8011, "./resources"))
    flask.start()
    sleep(2)
    multithreaded_processing(templates)
    flask.terminate()
