import json

from utils.api import api_caller
from utils.log_utils import log_err_json


class TestMailProcessor:
    TEST_MAIL = "<EMAIL>"
    NO_USER_CHECK_VALUE = "Y"

    @classmethod
    def process(cls, key, test_data):
        result = {}
        if not test_data:
            test_data = cls._generate_test_data(key)
        else:
            test_data = json.loads(test_data)
        language_list = ["FR", "DE"]
        header = test_data.get("Header", {})
        if isinstance(header, list):
            header = cls._process_list_header(header)
        else:
            header = cls._process_dict_header(header)
        test_data["Header"] = header
        for language in language_list:
            test_data["Langue"] = language
            responses = api_caller(method="post", path="/envMessage", body=test_data)
            result[language] = responses
            cls._capture_api_errors(key, language, responses, test_data)
        return {"template_id": key, "response": result}

    @classmethod
    def _process_list_header(cls, header):
        header.append({"Type": "NO_USER_CHECK", "Valeur": cls.NO_USER_CHECK_VALUE})
        email_item = {"Type": "EMAIL", "Valeur": cls.TEST_MAIL}
        for item in header:
            if item.get("Type") == "EMAIL":
                header.remove(item)
        if email_item not in header:
            header.append(email_item)
        return header

    @classmethod
    def _process_dict_header(cls, header):
        header["NO_USER_CHECK"] = cls.NO_USER_CHECK_VALUE
        header["EMAIL"] = cls.TEST_MAIL
        return header

    @classmethod
    def _generate_test_data(cls, key):
        return api_caller(method="get", path=f"/envMessage/{key}/body")

    @classmethod
    def _capture_api_errors(cls, key, lang, responses, log_data):
        if responses.get("StatusCode") != 200:
            print(f"error on mail {key} with language as : {lang}")
            log_err_json({"response": responses, "request": log_data})
