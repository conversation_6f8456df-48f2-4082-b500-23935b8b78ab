import json
import re


INFO_LENGTH = 6
INFO_START = "[INFO]"
EMAIL_REGEX = re.compile(r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b")


def convert_log_str_to_dict(log_entry):
    json_string = log_entry[len(INFO_START) :].strip() if log_entry.startswith(INFO_START) else log_entry.strip()
    try:
        return json.loads(json_string)
    except json.JSONDecodeError as e:
        print(f"JSON Decoding Error occurred: {e}")


def extract_log_content(logs, yopmail_name):
    results = {}
    for log in logs:
        log_entry = log[1]["value"]
        json_dict = convert_log_str_to_dict(log_entry)
        header_content = json_dict["request_body"]["Header"]
        if isinstance(header_content, list):
            for elem in list(header_content):
                if elem["Type"] == "TEMPLATE_ID":
                    template_id = elem["Valeur"]
                if elem["Type"] in ("PARTNER_ID", "EMAIL", "MOBILE_PHONE", "EAN", "PANNE_ID"):
                    header_content.remove(elem)
            header_content.append({"Type": "EMAIL", "Valeur": yopmail_name})
        else:
            template_id = header_content["TEMPLATE_ID"]
            header_content.pop("PARTNER_ID", None)
            header_content.pop("EMAIL", None)
            header_content.pop("MOBILE_PHONE", None)
            header_content.pop("EAN", None)
            header_content.pop("PANNE_ID", None)

            header_content["EMAIL"] = yopmail_name
        if template_id not in results.keys():
            results[template_id] = json_dict["request_body"]

    return replace_email_in_logs(results, yopmail_name)


def replace_email_in_logs(log_dict, replacement):
    for key, value in log_dict.items():
        json_str = json.dumps(value)
        modified_json_str = EMAIL_REGEX.sub(replacement, json_str)
        log_dict[key] = modified_json_str
    return log_dict
