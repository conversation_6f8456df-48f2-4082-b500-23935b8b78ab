import argparse
import os
import pickle

from Scripts.WS68_envMessage_test.extract_data import fetch_logs_over_months
from Scripts.WS68_envMessage_test.refactor_data import extract_log_content
from Scripts.WS68_envMessage_test.test_mail_processor import TestMailProcessor
from utils.aws_utils import get_dynamodb_table


class CacheManager:
    DEFAULT_FILENAME = "cache.pkl"

    @staticmethod
    def save_cache(data, filename=DEFAULT_FILENAME):
        with open(filename, "wb") as f:
            pickle.dump(data, f)

    @staticmethod
    def load_cache(filename=DEFAULT_FILENAME):
        if os.path.exists(filename):
            with open(filename, "rb") as f:
                return pickle.load(f)
        return None


if __name__ == "__main__":
    template_table = get_dynamodb_table("WS68_TEMPLATE_QTA")
    LOG_GROUPS_NAMES = "/aws/lambda/getDynamoDB-production"

    # Parse command line arguments
    parser = argparse.ArgumentParser()
    parser.add_argument("--only", nargs="*", help="List of template IDs to be fetched only")
    args = parser.parse_args()
    only = args.only

    # Fetch templates data
    if not only:
        response = template_table.scan()
        items = response["Items"]
        while "LastEvaluatedKey" in response:
            response = template_table.scan(ExclusiveStartKey=response["LastEvaluatedKey"])
            items.extend(response["Items"])
        templates_id = [item["Id"] for item in items if "EmailId" in item]
    else:
        templates_id = only

    # Fetch logs
    logs = fetch_logs_over_months(LOG_GROUPS_NAMES, templates_id, months=6)
    print(f"Retrieved {len(logs)} logs.")
    processed_logs = extract_log_content(logs, TestMailProcessor.TEST_MAIL)

    # Save test data
    for key, test_data in processed_logs.items():
        template_table.update_item(Key={"Id": key}, UpdateExpression="SET TestData = :val", ExpressionAttributeValues={":val": test_data})

    # Print stats
    found_id = [key for key in processed_logs.keys() if key in templates_id]
    missing_id = [key for key in templates_id if key not in processed_logs.keys()]
    print(f"Missing ids: {missing_id}\n Missing ids count: {len(missing_id)}\n Founded logs :  {found_id}\n Missing logs : {missing_id}")
