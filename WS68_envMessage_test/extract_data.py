from concurrent.futures import ThreadPoolExecutor
from datetime import datetime, time
from time import sleep

import boto3
from botocore.exceptions import BotoCoreError, ClientError
from dateutil.relativedelta import relativedelta


def get_query_results_with_retry(client, query_id, max_retries=5):
    retries = 0
    while retries < max_retries:
        try:
            response = client.get_query_results(queryId=query_id)
            return response
        except (ClientError, BotoCoreError) as error:
            if error.response.get("Error").get("Code") == "ThrottlingException":
                sleep_time = 2**retries
                sleep(sleep_time)
                retries += 1
            else:
                raise Exception(f"Failed to get query results after {max_retries} retries.")


def get_logs(log_group, template_id, start_time, end_time, all_logs):
    client = boto3.client("logs")
    start_timestamp = int(start_time.timestamp() * 1000)
    end_timestamp = int(end_time.timestamp() * 1000)

    query = f"""
        fields @timestamp, @message
        | filter request.resource = "/envMessage" and @message like /{template_id}/
        | sort @timestamp desc
        | limit 100
        """
    try:
        query_id = client.start_query(
            logGroupName=log_group,
            startTime=start_timestamp,
            endTime=end_timestamp,
            queryString=query,
        )["queryId"]
    except ClientError as ex:
        print(f"Failed to start query for {start_time} to {end_time} : {ex}")
        return []
    response = get_query_results_with_retry(client, query_id)

    while response["status"] == "Running" or response["status"] == "Scheduled":
        print(f"Waiting for query to complete... Status: {response['status']}")
        sleep(2)  # Sleep for a short interval before checking again
        response = get_query_results_with_retry(client, query_id)

    if response["status"] == "Complete":
        print("Query completed successfully")
        response_results = response.get("results", [])
        print(f"Mail found : {len(response_results)}")
        all_logs.extend(response_results)
    else:
        print("Query did not complete successfully. Status:", response["status"])
        return []


def split_interval_and_query(start, end, fail_list):
    # Split the time interval into two equal parts
    interval_size = (end - start) // 2
    interval1_start = start
    interval1_end = start + interval_size
    interval2_start = interval1_end
    interval2_end = end
    print(interval1_start, interval1_end, interval2_start, interval2_end)
    fail_list.append((interval1_start, interval1_end))
    fail_list.append((interval2_start, interval2_end))


def fetch_logs_over_months(log_group_name, template_ids, months=6):
    all_logs = []
    end_time = datetime.combine(datetime.today(), time(23, 59, 59))
    start_time = datetime.combine(end_time - relativedelta(days=30 * months), time())

    with ThreadPoolExecutor(max_workers=25) as executor:
        for template_id in template_ids:
            # Create a task for threadpool for each time interval
            executor.submit(get_logs, log_group_name, template_id, start_time, end_time, all_logs)

    return all_logs
