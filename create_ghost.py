import boto3
import requests

# table_name = 'MyResaUser_qta'
# base_url = 'https://api-acceptance.resa.be/v0'
# api_key = 'uAvIGTqpdgexRie3DHW0hXNkECc0GuLH'
table_name = "MyResaUser_qla"
base_url = "https://api-qla.resa.be/latest"
api_key = "xJtC6amoLA2Y45pekkUKjoQNEAmGtLb8"

dynamodb = boto3.resource("dynamodb")
table = dynamodb.Table(table_name)

users_to_create = [
    {"bp": 3100163909, "phone": "+32477455642", "email": "<EMAIL>"},
    {"bp": 3100713836, "phone": "+32477455642", "email": "<EMAIL>"},
    {"bp": 3100253841, "phone": "+32477455642", "email": "<EMAIL>"},
    {"bp": 3100270507, "phone": "+32477455642", "email": "<EMAIL>"},
    {"bp": 3100077825, "phone": "+32477455642", "email": "<EMAIL>"},
]


def create(user):
    user_data = {}

    if user.get("email"):
        user_data["Email"] = user.get("email")
    if user.get("phone"):
        user_data["Phone"] = user.get("phone")

    response = requests.post(f"{base_url}/utilisateurs", json=user_data, headers={"x-api-key": api_key})
    res_data = response.json()

    user["uid"] = res_data["SessionId"]


def set_bp_and_pref(user):
    table.update_item(
        Key={"uid": user["uid"]},
        UpdateExpression="SET bp = :x, preferences.com_encod_index_mail = :true, preferences.com_encod_index_sms = :true",
        ExpressionAttributeValues={":x": user["bp"], ":true": True},
    )


if __name__ == "__main__":
    for user in users_to_create:
        create(user)
        set_bp_and_pref(user)
        print(f"{user['uid']} / {user['bp']} / {user.get('email')} / {user.get('phone')}")
