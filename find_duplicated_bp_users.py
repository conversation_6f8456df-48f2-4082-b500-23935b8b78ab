import os

import boto3
from boto3.dynamodb.conditions import Attr

from utils.aws_utils import scan_all_generator


def find_duplicates():
    # Create output dir
    if not os.path.exists("find_duplicated_bp_users_output"):
        os.makedirs("find_duplicated_bp_users_output")

    users = scan_all_generator(
        os.environ["DYNAMODB"],
        {
            "ProjectionExpression": "uid, bp, email",
            "FilterExpression": Attr("bp").exists() & Attr("email").exists() & ~Attr("uid").begins_with("ghost_"),
        },
    )

    duplicated = {}
    for user in users:
        bp = user["bp"]

        if bp not in duplicated:
            duplicated[bp] = []

        duplicated[bp].append({"email": user["email"], "uid": user["uid"], "bp": str(user["bp"])})

    with open("find_duplicated_bp_users_output/duplicated.csv", "w") as output:
        output.write("mail;uid;bp\n")

        for k, v in duplicated.items():
            if len(v) > 1:
                for item in v:
                    output.write(f"{item['email']};{item['uid']};{item['bp']}\n")


def remove_duplicates():
    dynamodb = boto3.resource("dynamodb")
    table = dynamodb.Table(os.environ["DYNAMODB"])

    with open("find_duplicated_bp_users_output/duplicated.csv", "r") as duplicates:
        for duplicate in duplicates:
            if ";" in duplicate:
                uid = duplicate.split(";")[1]
                table.delete_item(Key={"uid": uid})


if __name__ == "__main__":
    find_duplicates()
    # remove_duplicates()
