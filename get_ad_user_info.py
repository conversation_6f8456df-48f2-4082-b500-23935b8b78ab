"""
Petit script pour vérifier un utilisateur AD et le débloquer si besoin.
"""

from datetime import datetime

from utils.ldap_utils import LDAP

# --- CONFIG ---
LDAP_SECRET = "MyResaAPI/ActiveDirectory/prd"
USER_ID = "Isa.Servais.BlbRQ"  # cn de l'utilisateur à tester
SEARCH_ATTR = "cn"  # attribut LDAP utilisé pour la recherche
unlock_it = False  # set it to unlock the account
THRESHOLD = 4  # si badPwdCount > THRESHOLD → on débloque
# ----------------

ldap = LDAP.loadFromSecret(LDAP_SECRET)
user = ldap.findUser(SEARCH_ATTR, USER_ID)

if not user:
    print(f"Utilisateur {SEARCH_ATTR}={USER_ID} introuvable")
    exit(1)

print("=== Infos utilisateur ===")
print(f"cn: {user.get('cn')}")
print(f"badPwdCount: {user.get('badPwdCount')}")
print(f"badPasswordTime: {user.get('badPasswordTime')}")
print(f"lockoutTime: {user.get('lockoutTime')}")
when_changed = user.get("whenChanged")
if when_changed:
    try:
        dt = datetime.strptime(when_changed, "%Y%m%d%H%M%S.0Z")
        print(f"Dernier changement: {dt.strftime('%Y-%m-%d %H:%M:%S')} UTC")
    except ValueError:
        print("Impossible de parser whenChanged.")
# Déblocage si besoin
if int(user.get("badPwdCount", 0)) > THRESHOLD and unlock_it:
    print("badPwdCount trop élevé → tentative de déblocage...")
    ldap.setUserAttribute("badPasswordTime", 0)
    print("Débloqué ! (badPasswordTime = 0)")
else:
    print("Aucun déblocage nécessaire.")
