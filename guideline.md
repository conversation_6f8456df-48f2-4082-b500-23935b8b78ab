# Development Guidelines for AWS API Web Project

This document provides essential information for developers working on the AWS API Web project. It includes build/configuration instructions, testing information, and development
guidelines.

## Build/Configuration Instructions

### Environment Setup

1. **Python Version**:
    - The project requires Python 3.11
    - Use `pyenv` to install the required python version
        - `pyenv install 3.11`
        - `pyenv shell 3.11`
        - `python -V` _to check that the version is correctly set_
    - Then venv to create a virtual environment
        - `python -m venv .venv`
        - `source .venv/bin/activate`

2. **Dependency Management**:
    - The project uses `pip` for dependency management.
    - Install dependencies with: `pip install`

3. **Environment Variables**:
    - Environment variables are defined in the `unittest.env` file for testing.
    - Key environment variables include:
        - `API_VERSION`: Version of the API (e.g., "v0")
        - `BUCKET`: S3 bucket name for resources
        - `DYNAMODB`: DynamoDB table name for user data
        - Various AWS service configurations

4. **AWS Configuration**:
    - The project interacts with multiple AWS services including:
        - Lambda
        - DynamoDB
        - S3
        - SQS
        - Secrets Manager
    - Ensure AWS credentials are properly configured in your environment

5. **Terminal emulator**:
    - The command bellows are written for fish and bash, check which terminal you are on with `bash -c 'cat /proc/$PPID/comm'` to use corrects commands

## Testing Information

### Test Configuration

1. **Testing Framework**:
    - The project uses `pytest` for testing.
    - Configuration is in `pytest.ini` and `.coveragerc`.
    - Tests run with parallel execution (`-n auto`) and code coverage reporting.

2. **Before running tests**:
    - Put the correct path for Python. Is needed the base directory and all modules directory:
        - for bash :
      ```
      export PYTHONPATH="$PWD"
      for dir in "$PWD"/*; do
          if [ -d "$dir" ]; then
              export PYTHONPATH="$PYTHONPATH:$dir";
          fi;
      done
      ```
        - for fish :
      ```
      export PYTHONPATH="$PWD"
      for dir in "$PWD"/*
        if [ -d "$dir" ]
          export PYTHONPATH="$PYTHONPATH:$dir";
        end
      end
      ```
    - Load test environment variables stored in `unittest.env`. The way to load it depend of your terminal emulator.
        - for bash: `set -a && source unittest.env && set +a`
        - for fish: `export (envsubst < unittest.env)`
3. **Running Tests**:
    - Run all tests: `python -m pytest`
    - Run specific test: `python -m pytest path/to/test_file.py`
    - Run with coverage: `python -m pytest --cov`
    - To know the url of the Lambda you want to test, you can look inside `ressources/linking.json`

3. **Test Organization**:
    - Tests are organized in `__test__` directories within each module.
    - Test files follow the naming convention `test_*.py`.

### Creating New Tests

1. **Test Structure**:
    - Tests use the `unittest` framework with pytest.
    - Test classes inherit from `unittest.TestCase`.
    - Use descriptive test method names starting with `test_`.

2. **Mocking**:
    - The project has extensive mocking utilities in `utils/mocking.py`.
    - Use the `@mock_api` decorator for API tests.
    - Use `unittest.mock` for mocking dependencies.

3. **Example Test**:

```python
import unittest

from utils.api import api_caller
from utils.mocking import mock_api


@mock_api
class TestExample(unittest.TestCase):
    def test_functionality(self):
        response = api_caller(
            "GET",
            "/test",
            raw=True
        )
        self.assertEqual(response.status_code, 200)
```

## Code Style and Development Guidelines

### Code Style

1. **Formatting**:
    - Line length: 180 characters
    - Indentation: 4 spaces
    - UTF-8 encoding
    - LF line endings
    - Docstrings are in numpy format

2. **Linting and Formatting Tools**:
    - The project uses Ruff for code formatting.
    - Ruff is also used for linting with various rule sets enabled.
    - Configuration is in `pyproject.toml`.

3. **Import Organization**:
    - Imports should be sorted by type.
    - Standard library imports first, then third-party, then local.

### Development Workflow

1. **Version Control**:
    - The project uses Git for version control.
    - The repository includes Git hooks in `.githooks/`.
    - Azure DevOps pipelines are configured for CI/CD.

2. **Project Structure**:
    - The project is organized into multiple modules, each with its own functionality.
    - Import of file inside a module doesn't need to use the module name because it's injected in the path
    - Common utilities are in the `utils` directory.
    - AWS Lambda functions are organized by functionality.
    - Each module correspond to a Lambda, so it's impossible to use function from other modules. The only exception is the `utils` directory which is inserted inside each Lambda
      module

3. **Error Handling**:
    - Custom error classes are defined in `utils/errors.py`.
    - Use appropriate error classes for different types of errors.

4. **Logging**:
    - Use the logging utilities in `utils/log_utils.py`.
    - Include appropriate context in log messages.

## Additional Resources

- Check the `resources/CHANGELOG.md` for version history.
- Azure pipeline configurations are in the root directory.
- Terraform configurations for infrastructure are in the `terraform` directory.
