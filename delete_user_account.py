import os
import sys

from boto3.dynamodb.conditions import Key

from utils.dict_utils import first
from utils.errors import NotFoundError
from utils.ldap_utils import LDAP
from utils.sap_user import sap_edit_user


def delete_user(ldap_secret: str, uid: str | None = None, mail: str | None = None, delete_ghost: bool = False) -> None:
    """Delete user account from DynamoDB and ADFS, then disconnect it from SAP."""
    from compte import find_user_by_bp, find_user_by_email, user_table  # noqa: PLC0415

    ldap = LDAP.loadFromSecret(ldap_secret)
    bp = None
    try:
        if not uid and mail:
            ldap_account = ldap.findUser("userPrincipalName", mail)
            if not ldap_account:
                raise NotFoundError(  # noqa: TRY301, TRY003
                    "No account linked to the given token",
                    error_code="NO_ACCOUNT_FOUND",
                )
            uid = ldap_account["cn"]

        # get user data
        user_data = first(
            user_table.query(
                KeyConditionExpression=Key("uid").eq(uid),
            )["Items"],
        )
        bp = user_data.get("bp")

        if not user_data:
            raise NotFoundError("No account linked to the given token", error_code="NO_ACCOUNT_FOUND")  # noqa: TRY003, TRY301

        # Delete from onprem AD
        ldap.deleteUserByUid(uid)
        print(f"{uid=} {mail=} {bp=} - deleted from AD")

        # Flag as disconnected in SAP, but only if not multiple account on same BP (shouldn't happen for account created after 27/04/2023)
        if bp and len(find_user_by_bp(bp)) == 1:
            user_data["preferences"]["BP_CONNECTE"] = False
            sap_edit_user(user_data)
            print(f"{uid=} {mail=} {bp=} - untagged from SAP")

        # Delete account from DynamoDB
        user_table.delete_item(Key={"uid": user_data["uid"]})
        print(f"{uid=} {mail=} {bp=} - deleted from Dynamo")

        if delete_ghost and mail:
            # Delete account from DynamoDB by mail
            accounts = find_user_by_email(mail)
            for account in accounts:
                if "ghost" in account["uid"]:
                    user_table.delete_item(Key={"uid": account["uid"]})
                    print(f"uid={account['uid']} {mail=} {bp=} - deleted from Dynamo")
    except NotFoundError:
        print(f"{uid=} {mail=} {bp=} - not found", file=sys.stderr)
    except Exception:
        print(f"{uid=} {mail=} {bp=} - error", file=sys.stderr)
        raise
    finally:
        del ldap


if __name__ == "__main__":
    os.environ["DYNAMODB"] = "MyResaUser_qta"
    os.environ["API_VERSION"] = "latest"
    os.environ["PASSTHROUGH_LAMBDA_ARN"] = "arn:aws:lambda:eu-west-1:************:function:passThrough-qta"
    _ldap_secret = "MyResaAPI/ActiveDirectory/qta"

    # os.environ["DYNAMODB"] = "MyResaUser_qla"
    # os.environ["API_VERSION"] = "latest"
    # os.environ["PASSTHROUGH_LAMBDA_ARN"] = "arn:aws:lambda:eu-west-1:************:function:passThrough-qla"
    # _ldap_secret = "MyResaAPI/ActiveDirectory/QLA"

    # os.environ["DYNAMODB"] = "MyResaUser_prd"
    # os.environ["API_VERSION"] = "latest"
    # os.environ["PASSTHROUGH_LAMBDA_ARN"] = "arn:aws:lambda:eu-west-1:************:function:passThrough-production"
    # _ldap_secret = "MyResaAPI/ActiveDirectory/prd"

    mails = ["<EMAIL>"]

    for _mail in mails:
        delete_user(_ldap_secret, mail=_mail.lower(), delete_ghost=True)
