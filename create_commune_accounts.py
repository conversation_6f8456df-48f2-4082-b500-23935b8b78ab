import os
from datetime import datetime, timedelta

import boto3
import requests
from boto3.dynamodb.conditions import Attr, Key

from Scripts.create_user_accounts import force_bp
from Scripts.delete_user_account import delete_user
from utils.dict_utils import first
from utils.token_utils import encode_simple_jwt

os.environ["DYNAMODB"] = "MyResaUser_qta"
base_url = "https://api-acceptance.resa.be/latest"
api_key = "uAvIGTqpdgexRie3DHW0hXNkECc0GuLH"
os.environ["PASSTHROUGH_LAMBDA_ARN"] = "arn:aws:lambda:eu-west-1:************:function:passThrough-qta"
ldap_secret = "MyResaAPI/ActiveDirectory/qta"

# os.environ["DYNAMODB"] = "MyResaUser_qla"
# base_url = "https://api-qla.resa.be/latest"
# api_key = "xJtC6amoLA2Y45pekkUKjoQNEAmGtLb8"
# os.environ["PASSTHROUGH_LAMBDA_ARN"] = "arn:aws:lambda:eu-west-1:************:function:passThrough-qla"
# ldap_secret = "MyResaAPI/ActiveDirectory/QLA"

# os.environ["DYNAMODB"] = "MyResaUser_prd"
# base_url = "https://api.resa.be/latest"
# api_key = "66qvACMYHyqFwrv6aEMd2JMNkK3PKtYn"
# os.environ["PASSTHROUGH_LAMBDA_ARN"] = "arn:aws:lambda:eu-west-1:************:function:passThrough-production"
# ldap_secret = "MyResaAPI/ActiveDirectory/prd"

os.environ["API_VERSION"] = "latest"
dynamodb = boto3.resource("dynamodb")
table = dynamodb.Table(os.environ["DYNAMODB"])

users_to_create = [
    {
        "firstname": "ADK",
        "lastname": "ResaAdmin",
        "email": "<EMAIL>",
        "bp": "3101232236",
        "Commune": {
            "Fonction": "",
            "Departement": "",
            "Localite": "",
            "Roles": [],
            "CodePostaux": [],
            "Id": "ADK",
            "Admin": True,
            "Actif": True,
        },
    },
    {
        "firstname": "CILE",
        "lastname": "ResaAdmin",
        "email": "<EMAIL>",
        "bp": "3101232237",
        "Commune": {
            "Fonction": "",
            "Departement": "",
            "Localite": "",
            "Roles": [],
            "CodePostaux": [],
            "Id": "CILE",
            "Admin": True,
            "Actif": True,
        },
    },
    {
        "firstname": "Liege",
        "lastname": "ResaAdmin",
        "email": "<EMAIL>",
        "bp": "3101008249",
        "Commune": {
            "Fonction": "Fonct.",
            "Departement": "Depart.",
            "Localite": "Liège",
            "Roles": [],
            "CodePostaux": [4030, 4032, 4000, 4430, 4031, 4020],
            "Id": "62063",
            "Admin": True,
            "Actif": True,
        },
    },
    {
        "firstname": "Oupeye",
        "lastname": "ResaAdmin",
        "email": "<EMAIL>",
        "bp": "3101008531",
        "Commune": {
            "Fonction": "Fonct.",
            "Departement": "Depart.",
            "Localite": "Oupeye",
            "Roles": [],
            "CodePostaux": [4683, 4684, 4681, 4682, 4680],
            "Id": "62079",
            "Admin": True,
            "Actif": True,
        },
    },
    {
        "firstname": "SaintNicolas",
        "lastname": "ResaAdmin",
        "email": "<EMAIL>",
        "bp": "3101008522",
        "Commune": {
            "Fonction": "Fonct.",
            "Departement": "Depart.",
            "Localite": "Saint-Nicolas",
            "Roles": [],
            "CodePostaux": [4420],
            "Id": "62093",
            "Admin": True,
            "Actif": True,
        },
    },
    {
        "firstname": "Seraing",
        "lastname": "ResaAdmin",
        "email": "<EMAIL>",
        "bp": "3101008524",
        "Commune": {
            "Fonction": "Fonct.",
            "Departement": "Depart.",
            "Localite": "Seraing",
            "Roles": [],
            "CodePostaux": [4101, 4102, 4100],
            "Id": "62096",
            "Admin": True,
            "Actif": True,
        },
    },
    {
        "firstname": "Soumagne",
        "lastname": "ResaAdmin",
        "email": "<EMAIL>",
        "bp": "3101008532",
        "Commune": {
            "Fonction": "Fonct.",
            "Departement": "Depart.",
            "Localite": "Soumagne",
            "Roles": [],
            "CodePostaux": [4633, 4631, 4630, 4632],
            "Id": "62099",
            "Admin": True,
            "Actif": True,
        },
    },
    {
        "firstname": "Sprimont",
        "lastname": "ResaAdmin",
        "email": "<EMAIL>",
        "bp": "3101008525",
        "Commune": {
            "Fonction": "Fonct.",
            "Departement": "Depart.",
            "Localite": "Sprimont",
            "Roles": [],
            "CodePostaux": [4141, 4140],
            "Id": "62100",
            "Admin": True,
            "Actif": True,
        },
    },
]


def create(user):
    user["data"] = {
        "Firstname": user["firstname"],
        "Lastname": user["lastname"],
        "Email": user["email"],
        "Phone": user.get("phone", ""),
    }
    response = requests.post(f"{base_url}/utilisateurs", json=user["data"], headers={"x-api-key": api_key})
    res_data = response.json()

    user["uid"] = res_data["SessionId"]


def activate(user):
    token = encode_simple_jwt(
        {
            "uid": user["uid"],
            "email": user["email"].lower(),
            "expirationTimestamp": (datetime.now() + timedelta(minutes=5)).timestamp(),
        },
    )
    user["data"]["Password"] = user.get("password", f"Welcome@{datetime.now().year}")

    response = requests.post(
        f"{base_url}/utilisateurs/activate?Token={token}",
        json=user["data"],
        headers={"x-api-key": api_key},
    )
    res_data = response.json()
    if response.status_code != requests.codes.ok or "Uid" not in res_data:
        raise Exception(f"Error activating user: {res_data}")
    else:
        user["uid"] = res_data["Uid"]


def get_existing_user(email):
    resp = table.query(
        IndexName="email-index",
        KeyConditionExpression=Key("email").eq(email),
        FilterExpression=Attr("valide").eq(True),
    )["Items"]
    return first(resp)


def add_commune_info(user):
    table.update_item(
        Key={"uid": user["uid"]},
        UpdateExpression="SET #c = :c, #c_id = :c_id",
        ExpressionAttributeNames={"#c": "Commune", "#c_id": "commune_id"},
        ExpressionAttributeValues={
            ":c": user["Commune"],
            ":c_id": user["Commune"]["Id"],
        },
    )


if __name__ == "__main__":
    for user in users_to_create:
        existing_user = get_existing_user(user["email"])
        if existing_user:
            delete_user(ldap_secret, mail=user["email"], delete_ghost=True)
        create(user)
        add_commune_info(user)
        activate(user)
        if user.get("bp"):
            force_bp(user)
        print(f"{user['uid']} / {user['email']}")
