import os
from datetime import datetime, timedelta
from time import sleep

import boto3
import requests
from boto3.dynamodb.conditions import Attr, Key

from Scripts.delete_user_account import delete_user
from utils.dict_utils import first
from utils.sap_user import sap_edit_user
from utils.token_utils import encode_simple_jwt

os.environ["DYNAMODB"] = "MyResaUser_qta"
base_url = "https://api-acceptance.resa.be/latest"
api_key = "uAvIGTqpdgexRie3DHW0hXNkECc0GuLH"
os.environ["PASSTHROUGH_LAMBDA_ARN"] = "arn:aws:lambda:eu-west-1:************:function:passThrough-qta"
ldap_secret = "MyResaAPI/ActiveDirectory/qta"

# os.environ["DYNAMODB"] = "MyResaUser_qla"
# base_url = "https://api-qla.resa.be/latest"
# api_key = "xJtC6amoLA2Y45pekkUKjoQNEAmGtLb8"
# os.environ["PASSTHROUGH_LAMBDA_ARN"] = "arn:aws:lambda:eu-west-1:************:function:passThrough-qla"
# ldap_secret = "MyResaAPI/ActiveDirectory/QLA"

# os.environ["DYNAMODB"] = "MyResaUser_prd"
# base_url = "https://api.resa.be/latest"
# api_key = "66qvACMYHyqFwrv6aEMd2JMNkK3PKtYn"
# os.environ["PASSTHROUGH_LAMBDA_ARN"] = "arn:aws:lambda:eu-west-1:************:function:passThrough-production"
# ldap_secret = "MyResaAPI/ActiveDirectory/prd"

os.environ["API_VERSION"] = "latest"
dynamodb = boto3.resource("dynamodb")
table = dynamodb.Table(os.environ["DYNAMODB"])

users_to_create = [
    {"email": "<EMAIL>", "bp": "3100550470", "uid": "Tes.TestNom.YyJBH"},
]


def create(user):
    user["data"] = {
        "Firstname": "Test Prénom",
        "Lastname": "Test Nom",
        "Email": user["email"],
        "Phone": "+32496123456",
        "Adresse": {
            "Cdpostal": "4000",
            "Localite": "Liège",
            "Rue": "Rue louvrex",
            "NumRue": "95",
            "CodePays": "BE",
        },
    }
    response = requests.post(
        f"{base_url}/utilisateurs",
        json=user["data"],
        headers={"x-api-key": api_key},
    )
    res_data = response.json()

    user["uid"] = res_data["SessionId"]


def activate(user):
    token = encode_simple_jwt(
        {
            "uid": user["uid"],
            "email": user["email"],
            "expirationTimestamp": (datetime.now() + timedelta(minutes=5)).timestamp(),
        },
    )
    user["data"]["Password"] = f"Welcome@{datetime.now().year}"

    response = requests.post(
        f"{base_url}/utilisateurs/activate?Token={token}",
        json=user["data"],
        headers={"x-api-key": api_key},
    )
    res_data = response.json()

    user["uid"] = res_data["Uid"]


def force_bp(user):
    while True:
        dynamo_user = first(
            table.query(
                KeyConditionExpression=Key("email").eq(user["email"]),
                IndexName="email-index",
            ).get("Items", []),
        )

        if dynamo_user.get("valide", False):
            user["uid"] = dynamo_user["uid"]
            table.update_item(
                Key={"uid": user["uid"]},
                UpdateExpression="SET #x = :x",
                ExpressionAttributeNames={"#x": "bp"},
                ExpressionAttributeValues={":x": int(user["bp"])},
            )
            sap_edit_user(dynamo_user)
            break

        sleep(10)


if __name__ == "__main__":
    for user in users_to_create:
        existing_user = first(
            table.query(
                IndexName="email-index",
                KeyConditionExpression=Key("email").eq(user["email"]),
                FilterExpression=Attr("valide").eq(True),
            )["Items"],
        )
        if existing_user:
            delete_user(ldap_secret, mail=user["email"], delete_ghost=True)

        if not user.get("uid"):
            create(user)
            activate(user)
        if user.get("bp"):
            force_bp(user)
        print(f"{user['uid']} / {user['email']}")
