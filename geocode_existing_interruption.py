from os import environ
from time import sleep

import boto3
import requests

from utils.aws_utils import scan_all_generator
from utils.geo_utils import Address

dynamodb = boto3.resource("dynamodb")
table = dynamodb.Table(environ["PANNE_LOCATION_TABLE"])


def get_interruption_id_from_dynamo():
    interruptions = scan_all_generator(environ["PANNE_LOCATION_TABLE"])

    return {elem["panne_id"] for elem in interruptions}


def get_no_located_interruption_from_ws():
    response = requests.get("https://api.resa.be/latest/pannes?EP=true&EnCours=true&PageSize=1000000")
    results = response.json().get("Data")

    no_located_interruptions = []
    for result in results:
        if not (result["Adresse"]["Long"] and result["Adresse"]["Lat"]):
            no_located_interruptions.append(
                {
                    "Id": result["Id"],
                    "Rue": result["Adresse"]["Rue"],
                    "Cp": result["Adresse"]["Zipcode"],
                    "Commune": result["Adresse"]["Ville"],
                    "Numero": result["Adresse"]["Numero"],
                }
            )

    return no_located_interruptions


def add_interruption_coords(interruption):
    address = Address(
        road=interruption.get("Rue"),
        number=interruption.get("Numero"),
        city=interruption.get("Commune"),
        zip_code=interruption.get("Cp"),
    )
    address.geocode()

    table.put_item(
        Item={
            "panne_id": interruption.get("Id"),
            "road": address.road,
            "number": address.number,
            "city": address.city,
            "zip_code": address.zip_code,
            "lat": address.lat,
            "lng": address.lng,
        }
    )


if __name__ == "__main__":
    located_interruptions_id = get_interruption_id_from_dynamo()
    sap_interruptions = get_no_located_interruption_from_ws()

    no_location = [interruption for interruption in sap_interruptions if interruption["Id"] not in located_interruptions_id]

    for interruption in no_location:
        add_interruption_coords(interruption)
        sleep(1)
