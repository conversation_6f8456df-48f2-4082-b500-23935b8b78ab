import os

import boto3
from boto3.dynamodb.conditions import Attr
from tqdm import tqdm

from utils.aws_utils import scan_all_generator

dynamodb = boto3.resource("dynamodb")
table = dynamodb.Table(os.environ["DYNAMODB"])


def fetch_users_uid(log_file="users.csv"):
    table_info = boto3.client("dynamodb").describe_table(TableName=os.environ["DYNAMODB"])
    user_count = table_info["Table"]["ItemCount"]

    users_gen = scan_all_generator(
        os.environ["DYNAMODB"],
        {
            "ProjectionExpression": "bp, email, firstname, lastname, Commune.Actif, Commune.Admin, Commune.CodePostaux, Commune.Departement, Commune.Fonction, Commune.Id, Commune.Localite",
            "FilterExpression": Attr("commune_id").exists(),
            "IndexName": "commune_id-index",
        },
    )

    with open(f"export_commune_user_output/{log_file}", "w") as users_save, tqdm(total=user_count) as progress:
        users_save.write("bp;email;firstname;lastname;Commune_Id;Commune_Admin;Commune_Actif;Commune_Localite;Commune_CodePostaux;Commune_Departement;Commune_Fonction\n")
        for user in users_gen:
            if user["Commune"].get("CodePostaux"):
                user["Commune"]["CodePostaux"] = ",".join(str(cp) for cp in user["Commune"]["CodePostaux"])
            users_save.write(
                f"{user.get('bp')};{user['email']};{user['firstname']};{user['lastname']};{user['Commune']['Id']};{user['Commune'].get('Admin', False)};{user['Commune'].get('Actif', bool(user.get('bp')))};{user['Commune']['Localite']};{user['Commune'].get('CodePostaux')};{user['Commune']['Departement']};{user['Commune']['Fonction']}\n"
            )
            progress.update()


if __name__ == "__main__":
    # Create output dir
    if not os.path.exists("export_commune_user_output"):
        os.makedirs("export_commune_user_output")

    print("----- Get users -----")
    fetch_users_uid()
