variable "workspace" {}
variable "name" {}
variable "templatefile" {}
variable "variables" { default = {} }
variable "policy" { default = "" }

resource "aws_iam_role" "state_machine_role" {
  name               = "state_machine_role-${var.name}-${var.workspace["stage"]}"
  path               = "/"
  assume_role_policy = <<EOF
  {
    "Version": "2012-10-17",
    "Statement": [
      {
        "Action": "sts:AssumeRole",
        "Principal": {
          "Service": "states.amazonaws.com"
        },
        "Effect": "Allow"
      }
    ]
  }
  EOF
}

resource "aws_sfn_state_machine" "state_machine" {
  name     = "${var.name}-${var.workspace["stage"]}"
  role_arn = aws_iam_role.state_machine_role.arn
  publish  = true
  definition = templatefile("sfn_templates/${var.templatefile}", merge({
    STAGE   = var.workspace["stage"]
    VERSION = var.workspace["api_stage"]
  }, var.variables))

  tags = {
    Name        = var.name
    Environment = var.workspace["stage_tag"]
    Service     = "MyResaAPI"
    Project     = "DigitalPlatform"
  }
}

resource "aws_iam_role_policy" "state_machine_policy" {
  name   = "state_machine_policy-${var.name}-${var.workspace["stage"]}"
  role   = aws_iam_role.state_machine_role.id
  policy = <<EOF
    {
    "Version": "2012-10-17",
      "Statement": [
        {
          "Effect": "Allow",
          "Action": ["states:StartExecution"],
          "Resource": ["${aws_sfn_state_machine.state_machine.arn}"]
        }
        ${var.policy == null ? "" : ",${var.policy}"}
      ]
    }
    EOF
}

resource "aws_sfn_alias" "state_machine_alias" {
  name = var.workspace["api_stage"]

  routing_configuration {
    state_machine_version_arn = aws_sfn_state_machine.state_machine.state_machine_version_arn
    weight                    = 100
  }
}

output "state_machine" {
  value = aws_sfn_state_machine.state_machine
}

output "state_machine_alias" {
  value = aws_sfn_alias.state_machine_alias
}