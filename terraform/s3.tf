//region env_message_non_prod
/*
This bucket is used to store messages sent from /envMessage when not in production environment.
*/
resource "aws_s3_bucket" "envMessageNonProd" {
  bucket = "${local.workspace["envMessageLogBucket"]}-${local.workspace["stage"]}"
  acl    = "private"

  lifecycle_rule {
    id      = "clean"
    enabled = true

    expiration {
      days = 60
    }
  }
}
//endregion

//region sharepointUploads / documentUpload
resource "aws_s3_bucket" "sharepointUploads" {
  bucket = "${local.workspace["S3Bucket"]}-sharepoint-uploads"
  acl    = "private"
}

resource "aws_s3_bucket" "documentUploads" {
  bucket = "${local.workspace["S3Bucket"]}-uploads"
  acl    = "private"

  lifecycle_rule {
    id      = "cleanup"
    enabled = true

    expiration {
      days = 30
    }
  }

  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["PUT"]
    allowed_origins = ["*"]
    expose_headers  = [""]
  }
}

resource "aws_s3_bucket_notification" "documentUploadNotification" {
  bucket = aws_s3_bucket.documentUploads.id

  lambda_function {
    lambda_function_arn = module.sharepointUploadV2.lambda_alias.arn
    events              = ["s3:ObjectCreated:*"]
  }
}
//endregion

resource "aws_s3_bucket" "tempStorage" {
  bucket = "${local.workspace["S3Bucket"]}-temp-storage"
  acl    = "private"

  lifecycle_rule {
    id      = "cleanup"
    enabled = true

    expiration {
      days = 2
    }
  }

  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["PUT", "GET"]
    allowed_origins = ["*"]
    expose_headers  = [""]
  }
}

resource "aws_s3_bucket" "PowalcoFiles" {
  bucket = local.workspace["PowalcoBucket"]
}