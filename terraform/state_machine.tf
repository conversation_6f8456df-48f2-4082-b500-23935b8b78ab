module "ean_export_workflow" {
  source       = "./StateMachine"
  workspace    = local.workspace
  name         = "ean_export_workflow"
  templatefile = "ean_export_state_machine.json"
  policy       = <<EOF
  {
    "Effect": "Allow",
    "Action": ["lambda:InvokeFunction"],
    "Resource": [
      "${module.eanExportLambda.lambda.arn}*",
      "${module.simpleComputeLambda.lambda.arn}*"
    ]
  }
  EOF
}