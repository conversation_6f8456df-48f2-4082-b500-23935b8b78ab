// endpoints ------------------------------------------------
module "passThroughLambda" {
  source         = "./Lambda"
  workspace      = local.workspace
  subnets        = var.subnets
  memory_size    = 2048
  securityGroups = var.securityGroups
  name           = "passThrough"
  environment = {
    MAPPING_BUCKET_NAME            = local.workspace["S3Bucket"]
    MAPPING_BUCKET_FILE            = "resources/${local.workspace["api_version"]}/linking.json"
    IS_PROD                        = local.workspace["prod"]
    AD_SECRET                      = local.workspace["ad_secret"]
    PayementTable                  = local.workspace["PayementTable"]
    DossierConfActionsTable        = local.workspace["DossierConfActionsTable"]
    SHAREPOINT_SECRET              = local.workspace["sharepoint_secret"]
    DIGACERT_STORE_CERT_LAMBDA_ARN = module.digacertStoreCert.lambda_alias.arn
    BUCKET_NAME_UPLOADS            = aws_s3_bucket.documentUploads.bucket
    BUCKET_NAME_SHAREPOINT_UPLOADS = aws_s3_bucket.sharepointUploads.bucket
    SharepointUpload_LAMBDA_ARN    = module.sharepointUploadV2.lambda_alias.arn
    MD_TABLE                       = local.workspace["dynamodb_md"]
    PANNE_LOCATION_TABLE           = local.workspace["panne_location_table"]
  }
  policy = <<EOF
{
  "Effect": "Allow",
  "Action": ["lambda:InvokeFunction"],
  "Resource": ["${module.digacertStoreCert.lambda.arn}*"]
},
{
  "Effect": "Allow",
  "Action": ["lambda:InvokeFunction"],
  "Resource": ["${module.sharepointUploadV2.lambda.arn}*"]
},
{
  "Effect": "Allow",
  "Action": ["dynamodb:*"],
  "Resource": [
    "*"
  ]
},
{
  "Effect": "Allow",
  "Action": ["s3:*"],
  "Resource": [
    "*"
  ]
}
EOF
}

module "genericPassThroughLambda" {
  source         = "./Lambda"
  workspace      = local.workspace
  subnets        = var.subnets
  memory_size    = 512
  securityGroups = var.securityGroups
  name           = "genericPassThrough"
  policy         = <<EOF
{
  "Effect": "Allow",
  "Action": ["s3:*"],
  "Resource": [
    "*"
  ]
}
EOF
}

module "sqlDrivenLambda" {
  source         = "./Lambda"
  workspace      = local.workspace
  subnets        = var.subnets
  securityGroups = var.securityGroups
  name           = "sqlDriven"
  memory_size    = 2048
  environment = {
    MAPPING_BUCKET_NAME     = local.workspace["S3Bucket"]
    MAPPING_BUCKET_FILE     = "resources/${local.workspace["api_version"]}/linking.json"
    IS_PROD                 = local.workspace["prod"]
    AD_SECRET               = local.workspace["ad_secret"]
    PayementTable           = local.workspace["PayementTable"]
    DossierConfActionsTable = local.workspace["DossierConfActionsTable"]
    SHAREPOINT_SECRET       = local.workspace["sharepoint_secret"]
    PANNE_LOCATION_TABLE    = local.workspace["panne_location_table"]
    UserEnergyProfiles      = local.workspace["UserEnergyProfiles"]
    SmartConsoAlerts        = local.workspace["SmartConsoAlerts"]
    HistoricAlerts          = local.workspace["HistoricAlerts"]
  }
  policy = <<EOF
{
  "Effect": "Allow",
  "Action": ["dynamodb:*"],
  "Resource": [
    "arn:aws:dynamodb:eu-west-1:${local.account_id}:table/${local.workspace["PayementTable"]}",
    "arn:aws:dynamodb:eu-west-1:${local.account_id}:table/${local.workspace["DossierConfActionsTable"]}",
     "arn:aws:dynamodb:eu-west-1:${local.account_id}:table/${local.workspace["panne_location_table"]}",
     "arn:aws:dynamodb:eu-west-1:${local.account_id}:table/${local.workspace["UserEnergyProfiles"]}",
     "arn:aws:dynamodb:eu-west-1:${local.account_id}:table/${local.workspace["SmartConsoAlerts"]}",
     "arn:aws:dynamodb:eu-west-1:${local.account_id}:table/${local.workspace["HistoricAlerts"]}"
  ]
}
EOF
}

# Copy of the SqlDriven but specifically used for the ean export case
# This copy is made because we need to limit the max concurrent execution to 5 at all time
module "eanExportLambda" {
  source                    = "./Lambda"
  workspace                 = local.workspace
  subnets                   = var.subnets
  securityGroups            = var.securityGroups
  name                      = "eanExport"
  code_path                 = "sqlDriven"
  memory_size               = 8192
  max_concurrent_executions = 5
  environment = {
    MAPPING_BUCKET_NAME     = local.workspace["S3Bucket"]
    MAPPING_BUCKET_FILE     = "resources/${local.workspace["api_version"]}/linking.json"
    IS_PROD                 = local.workspace["prod"]
    AD_SECRET               = local.workspace["ad_secret"]
    PayementTable           = local.workspace["PayementTable"]
    DossierConfActionsTable = local.workspace["DossierConfActionsTable"]
    SHAREPOINT_SECRET       = local.workspace["sharepoint_secret"]
    PANNE_LOCATION_TABLE    = local.workspace["panne_location_table"]
    UserEnergyProfiles      = local.workspace["UserEnergyProfiles"]
    SmartConsoAlerts        = local.workspace["SmartConsoAlerts"]
    HistoricAlerts          = local.workspace["HistoricAlerts"]
  }
  policy = <<EOF
{
  "Effect": "Allow",
  "Action": ["dynamodb:*"],
  "Resource": [
    "arn:aws:dynamodb:eu-west-1:${local.account_id}:table/${local.workspace["PayementTable"]}",
    "arn:aws:dynamodb:eu-west-1:${local.account_id}:table/${local.workspace["DossierConfActionsTable"]}",
     "arn:aws:dynamodb:eu-west-1:${local.account_id}:table/${local.workspace["panne_location_table"]}",
     "arn:aws:dynamodb:eu-west-1:${local.account_id}:table/${local.workspace["UserEnergyProfiles"]}",
     "arn:aws:dynamodb:eu-west-1:${local.account_id}:table/${local.workspace["SmartConsoAlerts"]}",
     "arn:aws:dynamodb:eu-west-1:${local.account_id}:table/${local.workspace["HistoricAlerts"]}"
  ]
}
EOF
}

module "getADTokenLambda" {
  source         = "./Lambda"
  workspace      = local.workspace
  subnets        = var.subnets
  securityGroups = var.securityGroups
  name           = "get_ad_token"
  environment = {
    MAPPING_BUCKET_NAME = local.workspace["S3Bucket"]
    MAPPING_BUCKET_FILE = "resources/${local.workspace["api_version"]}/linking.json"
    IS_PROD             = local.workspace["prod"]
    ADFS_SECRET         = local.workspace["adfs_secret"]
    AD_SECRET           = local.workspace["ad_secret"]
  }
}

// Data Lambda ------------------------------------------

module "getDynamoDBLambda" {
  source         = "./Lambda"
  workspace      = local.workspace
  subnets        = var.subnets
  securityGroups = var.securityGroups
  name           = "getDynamoDB"
  memory_size    = 512
  environment = {
    MAPPING_BUCKET_NAME            = local.workspace["S3Bucket"]
    MAPPING_BUCKET_FILE            = "resources/${local.workspace["api_version"]}/linking.json"
    JWK_BUCKET                     = local.workspace["S3Bucket"]
    JWK_KEY                        = "resources/AD_publickey.json"
    USER_ACTIVATION_SQS_URL        = module.userActivation.queue.id
    PASSTHROUGH_LAMBDA_ARN         = module.passThroughLambda.lambda_alias.arn
    IS_PROD                        = local.workspace["prod"]
    API_REGION                     = var.region
    SMS_CODES                      = local.workspace["sms_codes"]
    API_STAGE                      = local.workspace["api_stage"]
    AsyncProcessLambda             = module.AsyncProcess.lambda_alias.arn
    AsynchroneProcessesTable       = local.workspace["AsynchroneProcesses_table"]
    AD_SECRET                      = local.workspace["ad_secret"]
    PayementTable                  = local.workspace["PayementTable"]
    PowalcoFiles                   = local.workspace["PowalcoFiles"]
    PowalcoBucket                  = local.workspace["PowalcoBucket"]
    MOLLIE_TOKEN                   = local.workspace["mollieToken"]
    ENV_MESSAGE_LOG_BUCKET         = aws_s3_bucket.envMessageNonProd.bucket
    SHAREPOINT_SECRET              = local.workspace["sharepoint_secret"]
    BUCKET_NAME_UPLOADS            = aws_s3_bucket.documentUploads.bucket
    BUCKET_NAME_SHAREPOINT_UPLOADS = aws_s3_bucket.sharepointUploads.bucket
    SharepointUpload_LAMBDA_ARN    = module.sharepointUploadV2.lambda_alias.arn
    MD_TABLE                       = local.workspace["dynamodb_md"]
    ADFS_WEB                       = local.workspace["ADFSWeb"]
    PANNE_DYNAMODB_TABLE           = local.workspace["panne_dynamodb_table"]
    BornesRechargeTable            = local.workspace["BornesRechargeTable"]
    FormulairesTable               = local.workspace["FormulairesTable"]
    UserEnergyProfiles             = local.workspace["UserEnergyProfiles"]
    SALESFORCE_QUEUE_URL           = module.sendToSalesForceQueue.queue.id
    HTML_TO_PDF_LAMBDA             = module.transformHtmlToPdfLambda.lambda_alias.arn
    SmartConsoAlerts               = local.workspace["SmartConsoAlerts"]
    HistoricAlerts                 = local.workspace["HistoricAlerts"]
  }
  policy = <<EOF
{
  "Effect": "Allow",
  "Action": ["ses:VerifyEmailIdentity"],
  "Resource": ["*"]
},
{
  "Effect": "Allow",
  "Action": ["sqs:*"],
  "Resource": [
    "${module.userActivation.queue.arn}",
    "${module.sendToSalesForceQueue.queue.arn}"
  ]
},
{
  "Effect": "Allow",
  "Action": ["lambda:InvokeFunction"],
  "Resource": [
    "${module.AsyncProcess.lambda.arn}*",
    "${module.sharepointUploadV2.lambda.arn}*",
    "${module.passThroughLambda.lambda.arn}*",
    "${module.transformHtmlToPdfLambda.lambda.arn}*"
  ]
},
{
  "Effect": "Allow",
  "Action": ["dynamodb:*"],
  "Resource": [
    "*"
  ]
},
{
  "Effect": "Allow",
  "Action": ["s3:*"],
  "Resource": [
    "arn:aws:s3:::${local.workspace["PowalcoBucket"]}/*",
    "arn:aws:s3:::${local.workspace["S3Bucket"]}/*",
    "${aws_s3_bucket.envMessageNonProd.arn}/*"
  ]
},
{
  "Effect": "Allow",
  "Action": [
    "logs:DescribeLogStreams",
    "logs:GetLogEvents"
  ],
  "Resource": [
    "arn:aws:logs:*:*:log-group:monitoring-*:log-stream:*",
    "arn:aws:logs:*:*:log-group:monitoring-*"
  ]
}
EOF
}

// routines --------------------------------------------------------
module "updateActiveDirectoryPublicKey" {
  source         = "./Lambda"
  workspace      = local.workspace
  subnets        = var.subnets
  securityGroups = var.securityGroups
  name           = "httpToS3"
  handler        = "update.handler"
  environment = {
    KEY     = "resources/AD_publickey.json"
    URL     = "https://login.resa.be/adfs/discovery/keys"
    HOST    = "login.resa.be"
    IS_PROD = local.workspace["prod"]
  }
  policy = <<EOF
{
  "Effect": "Allow",
  "Action": ["s3:PutObject"],
  "Resource": ["arn:aws:s3:::${local.workspace["S3Bucket"]}/resources/AD_publickey.json"]
}
EOF
}

module "ghostCleaner" {
  source         = "./Lambda"
  workspace      = local.workspace
  subnets        = var.subnets
  securityGroups = var.securityGroups
  name           = "ghostCleaner"
  memory_size    = 1024
  environment = {
    IS_PROD = local.workspace["prod"]
  }
}

module "simpleComputeLambda" {
  source         = "./Lambda"
  workspace      = local.workspace
  subnets        = var.subnets
  securityGroups = var.securityGroups
  name           = "simpleCompute"
  memory_size    = 2048
  environment = {
    MAPPING_BUCKET_NAME = local.workspace["S3Bucket"]
    MAPPING_BUCKET_FILE = "resources/${local.workspace["api_version"]}/linking.json"
    IS_PROD             = local.workspace["prod"]
  }
  policy = <<EOF
  {
    "Effect": "Allow",
    "Action": ["states:StartExecution"],
    "Resource": [
      "${module.ean_export_workflow.state_machine_alias.arn}",
      "${module.ean_export_workflow.state_machine.arn}"
    ]
  }
  EOF
}

module "monthlyApiActivityChecker" {
  source         = "./Lambda"
  workspace      = local.workspace
  subnets        = var.subnets
  securityGroups = var.securityGroups
  name           = "monthlyApiActivityChecker"
  memory_size    = 1024
  environment = {
    IS_PROD                = local.workspace["prod"]
    API_ID                 = aws_api_gateway_rest_api.MyResaAPI.id
    API_NAME               = aws_api_gateway_rest_api.MyResaAPI.name
    ENV_MESSAGE_LOG_BUCKET = aws_s3_bucket.envMessageNonProd.bucket
  }
  policy = <<EOF
{
  "Effect": "Allow",
  "Action": ["cloudwatch:*","ivs:*","apigateway:*"],
  "Resource":  ["*"]
}
EOF
}

resource "aws_cloudwatch_event_target" "trigger_monthlyApiActivityChecker" {
  rule      = aws_cloudwatch_event_rule.every_one_month.name
  target_id = "monthlyApiActivityCheckerLambda"
  arn       = module.monthlyApiActivityChecker.lambda.arn
}

resource "aws_lambda_permission" "allow_cloudwatch_to_call_monthlyApiActivityCheckerLambda" {
  statement_id  = "AllowExecutionFromCloudWatch"
  action        = "lambda:InvokeFunction"
  function_name = module.monthlyApiActivityChecker.lambda.function_name
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.every_one_month.arn
}

module "extract_preferences" {
  source         = "./Lambda"
  workspace      = local.workspace
  subnets        = var.subnets
  securityGroups = var.securityGroups
  name           = "extract_preferences"
  memory_size    = 2048
  environment = {
    IS_PROD = local.workspace["prod"]
  }
  policy = <<EOF
{
  "Effect": "Allow",
  "Action": ["s3:PutObject"],
  "Resource": ["arn:aws:s3:::${local.workspace["S3Bucket"]}/output/*"]
}
EOF
}

module "trigger_sync_storelocations" {
  source         = "./Lambda"
  workspace      = local.workspace
  subnets        = var.subnets
  securityGroups = var.securityGroups
  name           = "trigger_sync_storelocations"
  timeout        = 240
  environment = {
    IS_PROD          = local.workspace["prod"]
    sftp_bucket_name = "resa-pointsderecharge"
    environment_name = local.workspace["stage"]
  }
  policy = <<EOF
  {
    "Sid": "InvokePermission",
    "Effect": "Allow",
    "Action": ["lambda:InvokeFunction"],
    "Resource": ["${module.sync_storelocations.lambda.arn}*"]
  },
  {
    "Effect": "Allow",
    "Action": ["s3:*"],
    "Resource": ["*"]
  }
  EOF
}

module "sync_storelocations" {
  source         = "./Lambda"
  workspace      = local.workspace
  subnets        = var.subnets
  securityGroups = var.securityGroups
  name           = "sync_storelocations"
  timeout        = 800
  environment = {
    IS_PROD                 = local.workspace["prod"]
    sftp_bucket_name        = "resa-pointsderecharge"
    storelocation_db_secret = local.workspace["storelocation_db_secret"]
  }
  policy = <<EOF
  {
      "Effect": "Allow",
      "Action": [
        "s3:GetObject"
      ],
      "Resource": ["*"]
  }
  EOF
}

module "getDocApilambda" {
  source         = "./Lambda"
  workspace      = local.workspace
  subnets        = var.subnets
  securityGroups = var.securityGroups
  name           = "getDocApi"
  timeout        = 800
  policy         = <<EOF
  {
      "Effect": "Allow",
      "Action": [
        "s3:GetObject"
      ],
      "Resource": ["*"]
  }
  EOF
}

// authorizer ------------------------------------------------------
module "tokenAuthorizer" {
  source         = "./Lambda"
  workspace      = local.workspace
  subnets        = var.subnets
  securityGroups = var.securityGroups
  name           = "tokenAuthorizer"
  environment = {
    KEY       = "resources/AD_publickey.json"
    IS_PROD   = local.workspace["prod"]
    AD_SECRET = local.workspace["ad_secret"]
  }
}

module "basicAuthorizer" {
  source         = "./Lambda"
  workspace      = local.workspace
  subnets        = var.subnets
  securityGroups = var.securityGroups
  name           = "basicAuthorizer"
  environment = {
    IS_PROD = local.workspace["prod"]
  }
}

module "signedUrlAuthorizer" {
  source         = "./Lambda"
  workspace      = local.workspace
  subnets        = var.subnets
  securityGroups = var.securityGroups
  name           = "signedUrlAuthorizer"
  environment = {
    IS_PROD = local.workspace["prod"]
  }
}

module "smsAuthorizer" {
  source         = "./Lambda"
  workspace      = local.workspace
  subnets        = var.subnets
  securityGroups = var.securityGroups
  name           = "smsAuthorizer"
  environment = {
    IS_PROD = local.workspace["prod"]
  }
}

// uploads ------------------------------------------------
module "documentUpload" {
  source         = "./Lambda"
  workspace      = local.workspace
  subnets        = var.subnets
  securityGroups = var.securityGroups
  name           = "documentUpload"
  environment = {
    IS_PROD                        = local.workspace["prod"]
    API_REGION                     = var.region
    API_STAGE                      = local.workspace["api_stage"]
    BUCKET_NAME_UPLOADS            = aws_s3_bucket.documentUploads.bucket
    BUCKET_NAME_SHAREPOINT_UPLOADS = aws_s3_bucket.sharepointUploads.bucket
    SharepointUpload_LAMBDA_ARN    = module.sharepointUploadV2.lambda_alias.arn
    MD_TABLE                       = local.workspace["dynamodb_md"]
  }
  policy = <<EOF
 {
   "Effect": "Allow",
   "Action": ["s3:*"],
   "Resource": ["*"]
 },
 {
   "Effect": "Allow",
   "Action": ["dynamodb:*"],
   "Resource": ["*"]
 }
 EOF
}

// temporary file upload ------------------------------------------------
module "tempFileUpload" {
  source         = "./Lambda"
  workspace      = local.workspace
  subnets        = var.subnets
  securityGroups = var.securityGroups
  name           = "tempFileUpload"
}

//region sharepointUpload depreciated todo : delete if the v2 doesn't have any issue
module "sharepointUpload" {
  source         = "./Lambda"
  workspace      = local.workspace
  subnets        = var.subnets
  securityGroups = var.securityGroups
  name           = "sharepointUpload"
  memory_size    = 2048
  layers         = ["arn:aws:lambda:eu-west-1:113088814899:layer:Klayers-python37-Pillow:11"]
  environment = {
    IS_PROD                        = local.workspace["prod"]
    API_REGION                     = var.region
    API_STAGE                      = local.workspace["api_stage"]
    BUCKET_NAME_UPLOADS            = aws_s3_bucket.documentUploads.bucket
    BUCKET_NAME_SHAREPOINT_UPLOADS = aws_s3_bucket.sharepointUploads.bucket
    SHAREPOINT_SECRET              = local.workspace["sharepoint_secret"]
    MD_TABLE                       = local.workspace["dynamodb_md"]
  }
  policy = <<EOF
 {
   "Effect": "Allow",
   "Action": ["s3:*"],
   "Resource": ["*"]
 },
 {
   "Effect": "Allow",
   "Action": ["dynamodb:*"],
   "Resource": ["*"]
 }
 EOF
}
//endregion
//region sharepointUploadV2
module "sharepointUploadV2" {
  source         = "./LambdaECRImage"
  workspace      = local.workspace
  subnets        = var.subnets
  securityGroups = var.securityGroups
  name           = "sharepointUploadV2"
  memory_size    = 2048
  environment = {
    IS_PROD                        = local.workspace["prod"]
    API_REGION                     = var.region
    API_STAGE                      = local.workspace["api_stage"]
    BUCKET_NAME_UPLOADS            = aws_s3_bucket.documentUploads.bucket
    BUCKET_NAME_SHAREPOINT_UPLOADS = aws_s3_bucket.sharepointUploads.bucket
    SHAREPOINT_SECRET              = local.workspace["sharepoint_secret"]
    MD_TABLE                       = local.workspace["dynamodb_md"]
  }
  policy = <<EOF
 {
   "Effect": "Allow",
   "Action": ["s3:*"],
   "Resource": ["*"]
 },
 {
   "Effect": "Allow",
   "Action": ["dynamodb:*"],
   "Resource": ["*"]
 }
 EOF
}

resource "aws_lambda_permission" "allow_documentUploadNotification_to_trigger_sharepointUploadLambda" {
  statement_id  = "AllowExecutionFromS3"
  action        = "lambda:InvokeFunction"
  function_name = module.sharepointUploadV2.lambda_alias.arn
  principal     = "s3.amazonaws.com"
  source_arn    = aws_s3_bucket.documentUploads.arn
}
//endregion

module "powalcoUploadLambda" {
  source         = "./Lambda"
  workspace      = local.workspace
  subnets        = var.subnets
  securityGroups = var.securityGroups
  name           = "powalcoUpload"
  environment = {
    IS_PROD       = local.workspace["prod"]
    API_REGION    = var.region
    API_STAGE     = local.workspace["api_stage"]
    PowalcoApiKey = local.workspace["PowalcoApiKey"]
    PowalcoApi    = local.workspace["PowalcoApi"]
    PowalcoFiles  = local.workspace["PowalcoFiles"]
    PowalcoBucket = local.workspace["PowalcoBucket"]
    PrefixUrl     = local.powalco_prefix_url
  }
  policy = <<EOF
 {
   "Effect": "Allow",
   "Action": ["s3:*"],
   "Resource": ["*"]
 },
 {
   "Effect": "Allow",
   "Action": ["dynamodb:*"],
   "Resource": ["*"]
 }
 EOF
}
module "powalcoSharepointUploadLambda" {
  source         = "./Lambda"
  workspace      = local.workspace
  subnets        = var.subnets
  securityGroups = var.securityGroups
  name           = "powalcoSharepointUpload"
  environment = {
    IS_PROD           = local.workspace["prod"]
    API_REGION        = var.region
    API_STAGE         = local.workspace["api_stage"]
    PowalcoFiles      = local.workspace["PowalcoFiles"]
    PowalcoBucket     = local.workspace["PowalcoBucket"]
    SHAREPOINT_SECRET = local.workspace["sharepoint_secret"]
    SHAREPOINT_SYSTEM = local.workspace["sharepoint_system"]
  }
  policy = <<EOF
 {
   "Effect": "Allow",
   "Action": ["s3:*"],
   "Resource": ["*"]
 },
 {
   "Effect": "Allow",
   "Action": ["dynamodb:*"],
   "Resource": ["*"]
 },
 {
   "Effect": "Allow",
   "Action": ["sqs:ReceiveMessage"],
   "Resource": ["*"]
 }
 EOF
}

module "AsyncProcess" {
  source         = "./Lambda"
  workspace      = local.workspace
  subnets        = var.subnets
  securityGroups = var.securityGroups
  name           = "AsyncProcess"
  environment = {
    IS_PROD                  = local.workspace["prod"]
    MAPPING_BUCKET_NAME      = local.workspace["S3Bucket"]
    MAPPING_BUCKET_FILE      = "resources/${local.workspace["api_version"]}/linking.json"
    AsynchroneProcessesTable = local.workspace["AsynchroneProcesses_table"]
  }
  policy = <<EOF
 {
   "Effect": "Allow",
   "Action": ["s3:*"],
   "Resource": ["*"]
 },
 {
   "Effect": "Allow",
   "Action": ["dynamodb:*"],
   "Resource": ["*"]
 },
 {
   "Effect": "Allow",
   "Action": ["lambda:InvokeFunction"],
   "Resource": ["arn:aws:lambda:*:*:function:*-${local.workspace["stage"]}*"]
 }
 EOF
}

module "userActivationLambda" {
  source         = "./Lambda"
  workspace      = local.workspace
  subnets        = var.subnets
  securityGroups = var.securityGroups
  name           = "userActivation"
  environment = {
    IS_PROD                = local.workspace["prod"],
    API_STAGE              = local.workspace["api_stage"]
    AD_SECRET              = local.workspace["ad_secret"]
    PASSTHROUGH_LAMBDA_ARN = module.passThroughLambda.lambda_alias.arn
    MD_TABLE               = local.workspace["dynamodb_md"]
  }
  policy = <<EOF
 {
   "Effect": "Allow",
   "Action": ["s3:*"],
   "Resource": ["*"]
 },
 {
   "Effect": "Allow",
   "Action": ["dynamodb:*"],
   "Resource": ["*"]
 },
 {
   "Effect": "Allow",
   "Action": ["sqs:*"],
   "Resource": ["*"]
 },
 {
   "Effect": "Allow",
   "Action": ["lambda:InvokeFunction"],
   "Resource": ["*"]
 }
 EOF
}

module "deadletterHandlerLambda" {
  source         = "./Lambda"
  workspace      = local.workspace
  subnets        = var.subnets
  securityGroups = var.securityGroups
  timeout        = 30
  name           = "deadletterHandler"
  environment = {
    IS_PROD                = local.workspace["prod"],
    API_STAGE              = local.workspace["api_stage"]
    AD_SECRET              = local.workspace["ad_secret"]
    ACTIVATION_FAILURE_SNS = data.aws_sns_topic.on_userActivation_failure.arn
    BASIC_DEADLETTER_SNS   = aws_sns_topic.basic_deadletter_notification.arn
  }
  policy = <<EOF
 {
   "Effect": "Allow",
   "Action": ["sns:ListSubscriptionsByTopic"],
   "Resource": [
    "${data.aws_sns_topic.on_userActivation_failure.arn}",
    "${aws_sns_topic.basic_deadletter_notification.arn}"
   ]
 }
 EOF
}

//region gazMeterPinLambda
module "gazMeterPinLambda" {
  source         = "./Lambda"
  workspace      = local.workspace
  subnets        = var.subnets
  securityGroups = var.securityGroups
  name           = "gazMeterPin"
  environment = {
    IS_PROD             = local.workspace["prod"]
    MAPPING_BUCKET_NAME = local.workspace["S3Bucket"]
    PINPOINT_APP_ID     = aws_pinpoint_app.MyResaAPI.application_id
  }
  policy = <<EOF
 {
   "Effect": "Allow",
   "Action": ["mobiletargeting:SendMessages"],
   "Resource": ["${aws_pinpoint_app.MyResaAPI.arn}/messages"]
 }
 EOF
}

resource "aws_lambda_permission" "allow_gaz_pin_sms_to_trigger_gazMeterPinLambda" {
  statement_id  = "AllowExecutionFromSNS"
  action        = "lambda:InvokeFunction"
  function_name = module.gazMeterPinLambda.lambda_alias.arn
  principal     = "sns.amazonaws.com"
  source_arn    = aws_sns_topic.gaz_pin_sms.arn
}
//endregion

//region usersCoherenceCheckLambda
module "usersCoherenceCheckLambda" {
  source         = "./Lambda"
  workspace      = local.workspace
  subnets        = var.subnets
  securityGroups = var.securityGroups
  memory_size    = 1024
  name           = "usersCoherenceCheck"
  environment = {
    IS_PROD   = local.workspace["prod"]
    AD_SECRET = local.workspace["ad_secret"]
  }
}

resource "aws_cloudwatch_event_target" "trigger_usersCoherenceCheckLambda_every_five_minutes" {
  rule      = aws_cloudwatch_event_rule.every_one_month.name
  target_id = "usersCoherenceCheckLambda"
  arn       = module.usersCoherenceCheckLambda.lambda_alias.arn
}

resource "aws_lambda_permission" "allow_cloudwatch_to_call_trigger_usersCoherenceCheckLambda" {
  statement_id  = "AllowExecutionFromCloudWatch"
  action        = "lambda:InvokeFunction"
  function_name = module.usersCoherenceCheckLambda.lambda.function_name
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.every_one_month.arn
}
//endregion

module "eanFormatCheckerLambda" {
  source         = "./Lambda"
  workspace      = local.workspace
  subnets        = var.subnets
  securityGroups = var.securityGroups
  name           = "eanFormatChecker"
}

//region sendEmailOnReleaseLambda
module "sendEmailOnReleaseLambda" {
  source         = "./Lambda"
  workspace      = local.workspace
  subnets        = var.subnets
  securityGroups = var.securityGroups
  name           = "sendEmailOnRelease"
  environment = {
    ATTRIBUTE       = local.workspace["CognitoMailAttribute"]
    COGNITO_POOL_ID = local.workspace["CognitoPoolId"]
    SENDER_EMAIL    = local.workspace["NewDeploySenderMail"]
  }
  policy = <<EOF
    {
      "Effect": "Allow",
      "Action": ["ses:SendRawEmail"],
      "Resource": ["*"]
    },
    {
        "Effect": "Allow",
        "Action": ["s3:GetObject"],
        "Resource": ["arn:aws:s3:::${local.workspace["S3Bucket"]}/*"]
    },
    {
        "Effect": "Allow",
        "Action": ["cognito-idp:ListUsers"],
        "Resource": ["arn:aws:cognito-idp:eu-west-1:${local.account_id}:userpool/eu-west-1_03nJTXjhD"]
    }
EOF
}

resource "aws_lambda_permission" "allow_codestar_new_version_to_trigger_sendEmailOnReleaseLambda" {
  statement_id  = "AllowExecutionFromSNS"
  action        = "lambda:InvokeFunction"
  function_name = module.sendEmailOnReleaseLambda.lambda_alias.arn
  principal     = "sns.amazonaws.com"
  source_arn    = aws_sns_topic.codestar_new_version.arn
}
// endregion

module "digacertStoreCert" {
  source         = "./Lambda"
  workspace      = local.workspace
  subnets        = var.subnets
  securityGroups = var.securityGroups
  name           = "digacertStoreCert"
  memory_size    = 512
  environment = {
    SHAREPOINT_SECRET = local.workspace["sharepoint_secret"]
  }
}

module "UserRefundFormLambda" {
  source         = "./Lambda"
  workspace      = local.workspace
  subnets        = var.subnets
  securityGroups = var.securityGroups
  name           = "userRefundForm"
  environment = {
    FORM_DYNAMO_TABLE = local.workspace["form_dynamo_table"]
  }

  policy = <<EOF
 {
   "Effect": "Allow",
   "Action": ["dynamodb:*"],
   "Resource": ["arn:aws:dynamodb:eu-west-1:${local.account_id}:table/${local.workspace["form_dynamo_table"]}"]
 }
 EOF
}

module "IncomingSmsLambda" {
  source         = "./Lambda"
  workspace      = local.workspace
  subnets        = var.subnets
  securityGroups = var.securityGroups
  name           = "incomingSms"
  environment = {
    PANNE_DYNAMODB_TABLE = local.workspace["panne_dynamodb_table"]
  }
  policy = <<EOF
 {
   "Effect": "Allow",
   "Action": ["dynamodb:*"],
   "Resource": ["arn:aws:dynamodb:eu-west-1:${local.account_id}:table/${local.workspace["panne_dynamodb_table"]}"]
 }
 EOF
}

module "PanneCheckerLambda" {
  source         = "./Lambda"
  workspace      = local.workspace
  subnets        = var.subnets
  securityGroups = var.securityGroups
  name           = "panneChecker"
}

module "MockCallLambda" {
  source         = "./Lambda"
  workspace      = local.workspace
  subnets        = var.subnets
  securityGroups = var.securityGroups
  name           = "mock_call"
  memory_size    = 512
}

module "CommuneUploadIndexlambda" {
  source         = "./Lambda"
  workspace      = local.workspace
  subnets        = var.subnets
  securityGroups = var.securityGroups
  name           = "CommuneUploadIndex"
  memory_size    = 512
  environment = {
    ENV_MESSAGE_LOG_BUCKET = aws_s3_bucket.envMessageNonProd.bucket
    SHAREPOINT_SECRET      = local.workspace["sharepoint_secret"]
    ADFS_WEB               = local.workspace["ADFSWeb"]
  }
  policy = <<EOF
  {
    "Effect": "Allow",
    "Action": ["lambda:InvokeFunction"],
    "Resource": ["arn:aws:lambda:eu-west-1:204480941676:function:CommuneUploadIndex-${local.workspace["stage"]}"]
  },
  {
          "Effect": "Allow",
          "Action": ["s3:PutObject"],
          "Resource": [
            "${aws_s3_bucket.envMessageNonProd.arn}/*",
            "arn:aws:s3:::${local.workspace["S3Bucket"]}-temp-storage/*",
            "arn:aws:s3:::${local.workspace["S3Bucket"]}/*"
          ]
      }
  EOF
}

module "coutModifRaccordementLambda" {
  source         = "./Lambda"
  workspace      = local.workspace
  subnets        = var.subnets
  securityGroups = var.securityGroups
  name           = "coutModifRaccordement"
  memory_size    = 512
}

module "vehiclesDataHandlerLambda" {
  source         = "./Lambda"
  workspace      = local.workspace
  subnets        = var.subnets
  securityGroups = var.securityGroups
  name           = "vehiclesDataHandler"
  memory_size    = 512
  environment = {
    HTML_TO_PDF_LAMBDA = module.transformHtmlToPdfLambda.lambda_alias.arn
  }
  policy = <<EOF
 {
   "Effect": "Allow",
   "Action": ["dynamodb:*"],
   "Resource": ["arn:aws:dynamodb:eu-west-1:${local.account_id}:table/UserVehicles_${local.workspace["stage_tag"]}",
                "arn:aws:dynamodb:eu-west-1:${local.account_id}:table/UserVehicles_${local.workspace["stage_tag"]}/*"
]
 },
{
    "Effect": "Allow",
    "Action": ["lambda:InvokeFunction"],
    "Resource": ["${module.transformHtmlToPdfLambda.lambda.arn}*"]
  }
 EOF
}

module "transformHtmlToPdfLambda" {
  source         = "./LambdaECRImage"
  workspace      = local.workspace
  subnets        = var.subnets
  securityGroups = var.securityGroups
  name           = "transformHtmlToPdf"
  memory_size    = 512
}

module "sendToSalesForceLambda" {
  source         = "./Lambda"
  workspace      = local.workspace
  subnets        = var.subnets
  securityGroups = var.securityGroups
  name           = "sendToSalesForce"
  environment = {
    IS_PROD   = local.workspace["prod"],
    API_STAGE = local.workspace["api_stage"]
  }
}

module "bornesRechargeDataHandlerLambda" {
  source         = "./Lambda"
  workspace      = local.workspace
  subnets        = var.subnets
  securityGroups = var.securityGroups
  name           = "bornesRechargeDataHandler"
  memory_size    = 512
  environment = {
    BornesRechargeTable = local.workspace["BornesRechargeTable"]
  }
  policy = <<EOF
  {
    "Effect": "Allow",
    "Action": ["s3:*"],
    "Resource": [
      "arn:aws:s3:::${local.workspace["S3Bucket"]}/*"
    ]
  },
  {
   "Effect": "Allow",
   "Action": ["dynamodb:*"],
   "Resource": [
      "arn:aws:dynamodb:eu-west-1:${local.account_id}:table/${local.workspace["BornesRechargeTable"]}",
      "arn:aws:dynamodb:eu-west-1:${local.account_id}:table/${local.workspace["BornesRechargeTable"]}/*"
    ]
  }
 EOF
}

module "evdbVehiclesRecuperationLambda" {
  source         = "./Lambda"
  workspace      = local.workspace
  subnets        = var.subnets
  securityGroups = var.securityGroups
  name           = "evdbVehiclesRecuperation"
  memory_size    = 512

  policy = <<EOF
    {
        "Effect": "Allow",
        "Action": ["s3:PutObject"],
        "Resource": [
          "arn:aws:s3:::${local.workspace["S3Bucket"]}/*"
        ]
    }
EOF
}

module "evdbVehiclesExpositionLambda" {
  source         = "./Lambda"
  workspace      = local.workspace
  subnets        = var.subnets
  securityGroups = var.securityGroups
  name           = "evdbVehiclesExposition"
  memory_size    = 512
}

module "sharepointHandlerLambda" {
  source         = "./Lambda"
  workspace      = local.workspace
  subnets        = var.subnets
  securityGroups = var.securityGroups
  name           = "sharepointHandler"
  environment = {
    SHAREPOINT_SECRET = local.workspace["sharepoint_secret"]
  }
}
