{"Comment": "Export EAN data workflow", "StartAt": "ExportEANDataMap", "States": {"ExportEANDataMap": {"Type": "Map", "ItemsPath": "$.eans", "MaxConcurrency": 1, "ItemBatcher": {"MaxItemsPerBatch": 2, "BatchInput": {"export_id.$": "$$.Execution.Name", "start_date.$": "$.start_date", "end_date.$": "$.end_date"}}, "Iterator": {"StartAt": "ExportEANData", "States": {"ExportEANData": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "TimeoutSeconds": 900, "Retry": [{"ErrorEquals": ["States.Timeout"], "IntervalSeconds": 60, "MaxAttempts": 3, "BackoffRate": 2.5, "JitterStrategy": "FULL"}, {"ErrorEquals": ["Lambda.TooManyRequestsException"], "IntervalSeconds": 300, "MaxAttempts": 100, "BackoffRate": 1.25, "JitterStrategy": "FULL"}], "Parameters": {"FunctionName": "eanExport-${STAGE}:${VERSION}", "Payload": {"task": "export_ean_data-fetch_raw_data_all_ean", "execution_id.$": "$$.Execution.Name", "export_id.$": "$.BatchInput.export_id", "start_date.$": "$.BatchInput.start_date", "end_date.$": "$.BatchInput.end_date", "eans.$": "$.Items"}}, "ResultSelector": {"$.$": "States.StringToJson($.Payload.body)"}, "ResultPath": "$.ean_cpt_list", "End": true}}, "ProcessorConfig": {"Mode": "DISTRIBUTED", "ExecutionType": "STANDARD"}}, "ResultPath": "$.ean_cpt_results", "Next": "ProcessEANDataMap"}, "ProcessEANDataMap": {"Type": "Map", "ItemsPath": "$.ean_cpt_list", "MaxConcurrency": 100, "ItemSelector": {"export_id.$": "$$.Execution.Name", "start_date.$": "$.start_date", "end_date.$": "$.end_date", "item.$": "$$.Map.Item.Value"}, "Iterator": {"StartAt": "ProcessEANData", "States": {"ProcessEANData": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "TimeoutSeconds": 900, "Retry": [{"ErrorEquals": ["States.Timeout"], "IntervalSeconds": 60, "MaxAttempts": 3, "BackoffRate": 2.5, "JitterStrategy": "FULL"}, {"ErrorEquals": ["Lambda.TooManyRequestsException"], "IntervalSeconds": 300, "MaxAttempts": 100, "BackoffRate": 1.25, "JitterStrategy": "FULL"}], "Parameters": {"FunctionName": "simpleCompute-${STAGE}:${VERSION}", "Payload": {"task": "export_ean_data-raw_data_to_csv_by_ean", "execution_id.$": "$$.Execution.Name", "export_id.$": "$.export_id", "ean_cpt.$": "$.item", "start_date.$": "$.start_date", "end_date.$": "$.end_date"}}, "ResultPath": null, "End": true}}, "ProcessorConfig": {"Mode": "DISTRIBUTED", "ExecutionType": "STANDARD"}}, "ResultPath": null, "Next": "ZipAndNotify"}, "ZipAndNotify": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "Parameters": {"FunctionName": "simpleCompute-${STAGE}:${VERSION}", "Payload": {"task": "export_ean_data-zip_and_notify", "email.$": "$.email", "export_id.$": "$$.Execution.Name", "execution_id.$": "$$.Execution.Name"}}, "End": true}}}