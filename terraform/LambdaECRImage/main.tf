variable "workspace" {}
variable "name" {}
variable "subnets" {}
variable "securityGroups" {}
variable "handler" { default = null }
variable "timeout" { default = 900 }
variable "environment" {
  type    = map
  default = {}
}
variable "memory_size" {
  default = 256
}
variable "policy" { default = null }

data "aws_caller_identity" "current" {}

locals {
  TokenInvalidation_table = "TokenInvalidation"
  account_id              = data.aws_caller_identity.current.account_id
}

data "aws_sns_topic" "SNSErrors" {
  name = var.workspace["SNSErrors"]
}

data "aws_ecr_repository" "lambda_image" {
  name = "my_resa_api/${lower(replace(replace(var.name, "/(.)([A-Z][a-z]+)/", "$1-$2"), "/([a-z0-9])([A-Z])/", "$1-$2"))}/${var.workspace["stage"]}"
}

data "aws_ecr_image" "lambda_image" {
  repository_name = data.aws_ecr_repository.lambda_image.name
  image_tag       = var.workspace["api_version"]
}

resource "aws_cloudwatch_metric_alarm" "alarm" {
  alarm_name          = "${aws_lambda_function.lambda.function_name} Errors"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "1"
  metric_name         = "Errors"
  namespace           = "AWS/Lambda"
  period              = "60"
  statistic           = "Maximum"
  threshold           = "1"
  alarm_actions       = [data.aws_sns_topic.SNSErrors.arn]
  dimensions = {
    FunctionName = aws_lambda_function.lambda.function_name
  }
}

resource "aws_lambda_function" "lambda" {
  depends_on    = [aws_iam_role.lambda]
  package_type  = "Image"
  image_uri     = "${data.aws_ecr_repository.lambda_image.repository_url}@${data.aws_ecr_image.lambda_image.id}"
  function_name = "${var.name}-${var.workspace["stage"]}"
  role          = aws_iam_role.lambda.arn
  memory_size   = var.memory_size
  timeout       = var.timeout
  publish       = var.workspace["prod"]
  vpc_config {
    subnet_ids         = var.subnets
    security_group_ids = var.securityGroups
  }
  environment {
    variables = merge(
      {
        IS_PROD                     = var.workspace["prod"]
        FunctionName                = "${var.name}-${var.workspace["stage"]}"
        BUCKET                      = var.workspace["S3Bucket"]
        DYNAMODB                    = var.workspace["dynamodb"]
        DYNAMODB_TOKEN_INVALIDATION = local.TokenInvalidation_table
        STAGE                       = var.workspace["stage"]
        STAGE_TAG                   = var.workspace["stage_tag"]
        ENV_FILE                    = var.workspace["env_file"]
        API_VERSION                 = var.workspace["api_version"]
        DOMAIN_NAME                 = var.workspace["domain_name"]
        SMS_CODES                   = var.workspace["sms_codes"]
        NO_TRASHMAIL                = var.workspace["stage"] == "production" ? "true" : null
        EmailSendsTable             = "EmailSends"
        ErrorSNS                    = data.aws_sns_topic.SNSErrors.arn
        API_URL = "https://${var.workspace["domain_name"]}/${var.workspace["api_version"]}"
        CACHE_BASE_DIR              = var.workspace["cache_base_dir"]
      },
      var.environment
    )
  }
  lifecycle {
    prevent_destroy = true
  }
}

resource "aws_lambda_alias" "lambda_alias" {
  name             = var.workspace["api_stage"]
  description      = aws_lambda_function.lambda.last_modified
  function_name    = aws_lambda_function.lambda.arn
  function_version = aws_lambda_function.lambda.version
}

resource "aws_iam_role" "lambda" {
  name               = "lambdaRole-${var.name}-${var.workspace["stage"]}"
  path               = "/"
  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "lambda.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
EOF

}

resource "aws_iam_role_policy" "lambda_policy" {
  depends_on = [aws_iam_role.lambda]
  name       = "lambda_policy-${var.name}-${var.workspace["stage"]}"
  role       = aws_iam_role.lambda.id
  policy     = <<EOF
{
"Version": "2012-10-17",
"Statement": [
    {
    "Sid": "VisualEditor0",
    "Effect": "Allow",
    "Action": [
        "ec2:CreateNetworkInterface",
        "ec2:DescribeNetworkInterfaces",
        "ec2:DeleteNetworkInterface",
        "logs:CreateLogGroup",
        "logs:CreateLogStream",
        "logs:PutLogEvents",
        "logs:DescribeLogStreams"
    ],
    "Resource": ["*"]
    },
    {
    "Effect": "Allow",
    "Action": [
        "dynamodb:*"
    ],
    "Resource": [
      "arn:aws:dynamodb:eu-west-1:${local.account_id}:table/${var.workspace["dynamodb"]}",
      "arn:aws:dynamodb:eu-west-1:${local.account_id}:table/${var.workspace["sms_codes"]}",
      "arn:aws:dynamodb:eu-west-1:${local.account_id}:table/${var.workspace["PowalcoFiles"]}",
      "arn:aws:dynamodb:eu-west-1:${local.account_id}:table/${local.TokenInvalidation_table}",
      "arn:aws:dynamodb:eu-west-1:${local.account_id}:table/${var.workspace["dynamodb"]}/index/*",
      "arn:aws:dynamodb:eu-west-1:${local.account_id}:table/${var.workspace["sms_codes"]}/index/*",
      "arn:aws:dynamodb:eu-west-1:${local.account_id}:table/${var.workspace["PowalcoFiles"]}/index/*",
      "arn:aws:dynamodb:eu-west-1:${local.account_id}:table/EmailSends"
    ]
    },
    {
    "Sid": "VisualEditor1",
    "Effect": "Allow",
    "Action": [
        "secretsmanager:GetSecretValue"
    ],
    "Resource": "*",
    "Condition": {
        "StringEqualsIgnoreCase": {
            "secretsmanager:ResourceTag/MyResaAPI": "Allow"
        }
    }
    },
    {
        "Effect": "Allow",
        "Action": ["s3:GetObject"],
        "Resource": [
          "arn:aws:s3:::${var.workspace["S3Bucket"]}/*",
          "arn:aws:s3:::${var.workspace["S3Bucket"]}-temp-storage/*"
        ]
    },
    {
        "Effect": "Allow",
        "Action": ["s3:PutObject"],
        "Resource": [
          "arn:aws:s3:::${var.workspace["S3Bucket"]}-temp-storage/*"
        ]
    },
    {
        "Effect": "Allow",
        "Action": ["sns:Publish"],
        "Resource": ["${data.aws_sns_topic.SNSErrors.arn}"]
    }
    ${var.policy == null ? "" : ",${var.policy}"}
]
}
EOF
}

output "lambda" {
  value = aws_lambda_function.lambda
}
output "lambda_alias" {
  value = aws_lambda_alias.lambda_alias
}
output "role" {
  value = aws_iam_role.lambda
}
