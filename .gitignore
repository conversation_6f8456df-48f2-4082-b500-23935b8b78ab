tmp/
venv/
venv*/
pyvenv.cfg
*.pyc
*.pyo
.idea/
node_modules
.serverless
bundle.zip
bundle/
# Local .terraform directories
**/.terraform/*

# .tfstate files
*.tfstate
*.tfstate.*

reports/

documentation.html
myresaapi_openapi_doc.json
myresaapi_openapi_doc.yaml
monitoring.zip
bin/queryAD.py
users.json
nbBp_amount.json
local.env.json

*.log
*.tmp
*.ipynb
!user_creation_workflow.ipynb
*_output/
.local/
/Scripts/
.coverage
coverage.xml
htmlcov/