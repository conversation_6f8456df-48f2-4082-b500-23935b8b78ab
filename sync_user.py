import os
import sys

import boto3
from boto3.dynamodb.conditions import Key

from utils.sap_user import sap_edit_user

dynamodb = boto3.resource("dynamodb")
table = dynamodb.Table(os.environ["DYNAMODB"])


def fetch_user(uid):
    response = table.query(KeyConditionExpression=Key("uid").eq(uid))
    response = response["Items"][0]
    return response


def sync_user(uid):
    user = fetch_user(uid)
    sap_edit_user(user)


if __name__ == "__main__":
    sync_user(sys.argv[1])
