#!/bin/bash

while read -r local_ref local_sha remote_ref remote_sha
do
    if [[ "$local_ref" == refs/heads/* ]]; then
#        # Run unittest before push
#        if [[ "$local_ref" == refs/heads/feature/* || "$local_ref" == refs/heads/bugfix/* ]] && ! git log -1 --pretty=%B | grep -q -- "--no-test" && ! git log -1 --pretty=%B | grep -q -- "WIP"; then
#            set -a && source unittest.env && set +a
#            export PYTHONPATH="$PWD"
#            for dir in "$PWD"/*; do
#                if [ -d "$dir" ]; then
#                    export PYTHONPATH="$PYTHONPATH:$dir"
#                fi
#            done
#
#            # check if tests passed
#            if ! pytest; then
#                echo -e "Aborting push: Test failed"
#                exit 1
#            fi
#        fi
        if [ "$local_ref" = "refs/heads/main" ]; then
            # read version file and add tag
            version=$(cat version)
            tag="v${version}"
            git tag "$tag"
            git push origin "$tag"
        fi
    fi
done

exit 0
