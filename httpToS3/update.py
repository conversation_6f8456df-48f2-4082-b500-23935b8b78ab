import os

import requests

from utils.aws_handler_decorator import aws_lambda_handler
from utils.aws_utils import write_s3_file
from utils.log_utils import log_err


@aws_lambda_handler
def handler(event, context):
    try:
        url = os.environ["URL"]
        bucket = os.environ["BUCKET"]
        key = os.environ["KEY"]
        host = os.environ["HOST"]
        headers = {"Host": host}

        resp = requests.get(url, headers=headers, verify=False)
        resp.raise_for_status()
        content = resp.text
        resp.close()

        write_s3_file(bucket, key, content)
    except Exception as e:
        log_err(e)
        raise e
