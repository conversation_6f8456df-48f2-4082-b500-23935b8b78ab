#!/usr/bin/env python3
import argparse
import csv
from decimal import Decimal

import boto3


def csv_handling(csv_file_path):
    id_mapping = {}

    with open(csv_file_path, mode="r") as csv_file:
        csv_reader = csv.DictReader(csv_file, delimiter=";")
        for row in csv_reader:
            old_id = row["OLD_ID"]
            new_id = row["NEW_ID"]
            # The following two lines need modification if the logic related to '#' changes.
            if old_id.startswith("#"):
                id_mapping[old_id[1:]] = new_id[1:]
    return id_mapping


def dynamoDB_handling(id_mapping, stage):
    dynamodb = boto3.resource("dynamodb")
    table_name = f"{stage}"
    table = dynamodb.Table(table_name)

    response = table.scan()

    for item in response["Items"]:
        for lang in item.get("EmailId", []):
            old_email_id = str(item["EmailId"][lang])
            if old_email_id in id_mapping:
                # Préparez le nouvel ID
                new_email_id = id_mapping[old_email_id]

                # Mettre à jour l'élément dans DynamoDB
                table.update_item(
                    Key={"Id": item["Id"]},
                    UpdateExpression=f"SET EmailId.{lang} = :new_id",
                    ExpressionAttributeValues={":new_id": Decimal(new_email_id)},
                )
                print(f"Updated item with ID {item['Id']} and email ID {old_email_id} to {new_email_id}")


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("-e", "--env", required=True, help="get name for current env")
    args = parser.parse_args()

    id_ammping = csv_handling("old_new_id.csv")
    dynamoDB_handling(id_ammping, f"WS68_TEMPLATE_{args.env}")


if __name__ == "__main__":
    main()
