#!/usr/bin/env python3
import argparse
import csv
from decimal import Decimal

import boto3


def import_csv_to_dynamo(table, csv_file_path):
    with open(csv_file_path, mode="r") as csv_file:
        csv_reader = csv.DictReader(csv_file, delimiter=";")
        for row in csv_reader:
            put_dict = {}
            for key, value in row.items():
                key, val_type = key[:-1].split(" (")
                if value:
                    if val_type == "Decimal":
                        value = Decimal(value)
                    else:
                        value = getattr(__builtins__, val_type)(value)

                    if "." in key:
                        parent_key, child_key = key.split(".")
                        if parent_key not in put_dict:
                            put_dict[parent_key] = {}
                        put_dict[parent_key][child_key] = value
                    else:
                        put_dict[key] = value

            try:
                table.put_item(Item=put_dict)
                print(f"Added {put_dict}")
            except Exception as e:
                print(f"Error adding {put_dict} with {e}")


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("-e", "--env", required=True, help="get name for current env")
    args = parser.parse_args()

    dynamodb = boto3.resource("dynamodb")
    table_name = f"WS68_TEMPLATE_{args.env}"
    table = dynamodb.Table(table_name)
    csv_file_name = "dynamodb_data.csv"

    import_csv_to_dynamo(table, csv_file_name)


if __name__ == "__main__":
    main()
