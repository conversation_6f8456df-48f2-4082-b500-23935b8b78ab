#!/usr/bin/env python3
import argparse
import csv
import json
from decimal import Decimal

import boto3


def decimal_default(obj):
    if isinstance(obj, Decimal):
        if obj % 1 == 0:
            return int(obj)
        else:
            return float(obj)
    raise TypeError


def flatten_dict_key(_dict: dict):
    keys = []

    for key, val in _dict.items():
        if isinstance(val, dict):
            for sub_key in flatten_dict_key(val):
                keys.append(f"{key}.{sub_key}")
        else:
            keys.append(f"{key} ({val.__class__.__name__})")

    return keys


def get_dynamodb_data(table):
    response = table.scan()
    columns = set()

    if response["Items"]:
        for item in response["Items"]:
            columns.update(flatten_dict_key(item))

    return list(columns), response


def csv_handling(csv_file_name, columns, response):
    with open(csv_file_name, "w", newline="", encoding="utf-8") as csv_file:
        csv_writer = csv.writer(csv_file, delimiter=";")
        csv_writer.writerow(columns)
        for item in response["Items"]:
            row = []
            for column in columns:
                column = column.split(" ")[0]  # remove type annotation
                if column in item:
                    value = json.dumps(item.get(column, ""), default=decimal_default)
                    clean_value = value.replace('"', "")
                    row.append(clean_value)
                else:
                    col_id = column.split(".")
                    col_value = item.get(col_id[0], "")
                    if col_value:
                        value = json.dumps(col_value.get(col_id[1], ""), default=decimal_default)
                        clean_value = value.replace('"', "")
                        row.append(clean_value)
                    else:
                        row.append("")
            csv_writer.writerow(row)


def get_index_position(search_list: list, item: any):
    try:
        return search_list.index(item)
    except ValueError:
        return 9999999999


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("-e", "--env", required=True, help="get name for current env")
    parser.add_argument("--col_order", nargs="+", help="order of the columns")
    parser.add_argument("--order_by", help="columns to order by")
    args = parser.parse_args()

    dynamodb = boto3.resource("dynamodb")
    table_name = f"WS68_TEMPLATE_{args.env}"
    table = dynamodb.Table(table_name)
    csv_file_name = "dynamodb_data.csv"
    columns, response = get_dynamodb_data(table)
    if args.col_order:
        columns.sort(key=lambda col: get_index_position(args.col_order, col.split(" ")[0]))
    if args.order_by:
        response["Items"].sort(key=lambda row: row.get(args.order_by, 9999999999))
    csv_handling(csv_file_name, columns, response)


if __name__ == "__main__":
    main()
