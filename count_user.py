#!/usr/bin/env python
# coding=UTF-8

import argparse
import datetime

import boto3
from boto3.dynamodb.conditions import Attr

dynamodb = boto3.resource("dynamodb")


def count_entries_between_dates(start_date, end_date, table_name):
    table = dynamodb.Table(table_name)
    count = 0

    response = table.scan(
        FilterExpression=Attr("createdAt").between(int(start_date.timestamp() * 1000), int(end_date.timestamp() * 1000)) & Attr("valide").eq(True) & Attr("bp").exists(),
    )

    count += response["Count"]

    while "LastEvaluatedKey" in response:
        last_key = response["LastEvaluatedKey"]
        response = table.scan(
            FilterExpression=Attr("createdAt").between(int(start_date.timestamp() * 1000), int(end_date.timestamp() * 1000)) & Attr("valide").eq(True) & Attr("bp").exists(),
            ExclusiveStartKey=last_key,
        )
        count += response["Count"]

    return count


def count_active_user(start_date, end_date, table_name):
    table = dynamodb.Table(table_name)
    count = 0

    start_date_iso = start_date.isoformat()
    end_date_iso = end_date.isoformat()

    response = table.scan(
        FilterExpression=Attr("last_login").between(start_date_iso, end_date_iso),
    )

    count += response["Count"]

    while "LastEvaluatedKey" in response:
        last_key = response["LastEvaluatedKey"]
        response = table.scan(
            FilterExpression=Attr("last_login").between(start_date_iso, end_date_iso),
            ExclusiveStartKey=last_key,
        )
        count += response["Count"]

    return count


if __name__ == "__main__":
    # Définition des arguments en ligne de commande
    parser = argparse.ArgumentParser(description="Compter le nombre d'entrées dans une table DynamoDB entre deux dates.")
    parser.add_argument("--start", help="Date de début au format YYYY-MM-DD", required=True)
    parser.add_argument("--end", help="Date de fin au format YYYY-MM-DD", required=True)
    parser.add_argument("--table", help="Nom de la table DynamoDB", required=True)
    parser.add_argument("-a", help="Uniquement utilisateurs actifs", required=False, action="store_true", default=False)
    args = parser.parse_args()

    # Conversion des arguments en objets datetime
    start_date = datetime.datetime.strptime(args.start, "%Y-%m-%d")
    end_date = datetime.datetime.strptime(args.end, "%Y-%m-%d")

    # Comptage des entrées
    entry_count = count_active_user(start_date, end_date, args.table) if args.a else count_entries_between_dates(start_date, end_date, args.table)

    # Affichage du résultat
    print(
        "Nombre d'entrées entre le",
        start_date.strftime("%d %B"),
        "et le",
        end_date.strftime("%d %B"),
        ":",
        entry_count,
    )
