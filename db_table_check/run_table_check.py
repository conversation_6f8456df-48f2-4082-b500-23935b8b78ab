#!/usr/bin/env python3
"""
Quick runner script for database table crawler.
This provides a simple interface for common use cases.
"""

import subprocess
import sys
from pathlib import Path

# Get the directory where this script is located
SCRIPT_DIR = Path(__file__).parent.absolute()
CRAWLER_SCRIPT = SCRIPT_DIR / "database_table_crawler.py"
TABLES_FILE = SCRIPT_DIR / "tables.txt"


def run_crawler_interactive():
    """Interactive mode to help users run the crawler easily."""
    # Check if the crawler script exists
    if not CRAWLER_SCRIPT.exists():
        print(f"Error: database_table_crawler.py not found at {CRAWLER_SCRIPT}")
        print("Make sure you're running this script from the correct location.")
        return

    print("Database Table Usage Checker")
    print("=" * 40)
    print()

    # Get table names
    print("How would you like to specify the table names?")
    print("1. Type them manually (space-separated)")
    print("2. Use a file containing table names")
    print("3. Use the tables.txt file")

    choice = input("Enter choice (1-3): ").strip()

    cmd = ["python", str(CRAWLER_SCRIPT)]

    if choice == "1":
        tables = input("Enter table names (space-separated): ").strip()
        if not tables:
            print("No table names provided. Exiting.")
            return
        cmd.extend(["--tables"] + tables.split())

    elif choice == "2":
        file_path = input("Enter path to file containing table names: ").strip()
        if not Path(file_path).exists():
            print(f"File {file_path} does not exist. Exiting.")
            return
        cmd.extend(["--tables-file", file_path])

    elif choice == "3":
        if not TABLES_FILE.exists():
            print("tables.txt not found. Exiting.")
            return
        cmd.extend(["--tables-file", str(TABLES_FILE)])

    else:
        print("Invalid choice. Exiting.")
        return

    # Get directory
    directory = input("Enter directory to scan (press Enter for current directory): ").strip()
    if directory:
        cmd.extend(["--directory", directory])

    # Case sensitivity
    case_sensitive = input("Case-sensitive search? (y/N): ").strip().lower()
    if case_sensitive == "y":
        cmd.append("--case-sensitive")

    # Output options
    save_report = input("Save report to file? (y/N): ").strip().lower()
    if save_report == "y":
        output_file = input("Enter output filename (default: table_usage_report.txt): ").strip()
        if not output_file:
            output_file = "table_usage_report.txt"
        cmd.extend(["--output", output_file])

    save_json = input("Save detailed results as JSON? (y/N): ").strip().lower()
    if save_json == "y":
        json_file = input("Enter JSON filename (default: table_usage_results.json): ").strip()
        if not json_file:
            json_file = "table_usage_results.json"
        cmd.extend(["--json-output", json_file])

    print()
    print("Running command:", " ".join(cmd))
    print("-" * 40)

    # Run the command
    try:
        result = subprocess.run(cmd, check=True)
        print("\nScan completed successfully!")

        if save_report == "y":
            print(f"Report saved to: {output_file}")
        if save_json == "y":
            print(f"JSON results saved to: {json_file}")

    except subprocess.CalledProcessError as e:
        print(f"Error running crawler: {e}")
    except FileNotFoundError:
        print(f"Error: database_table_crawler.py not found at {CRAWLER_SCRIPT}")


def show_help():
    """Show help information."""
    print("Database Table Usage Checker - Quick Runner")
    print("=" * 50)
    print()
    print("This script helps you easily run the database table crawler.")
    print()
    print("Usage:")
    print("  python run_table_check.py              # Interactive mode")
    print("  python run_table_check.py --help       # Show this help")
    print()
    print("For advanced usage, run the crawler directly:")
    print("  python database_table_crawler.py --help")
    print()
    print("Examples:")
    print("  # Quick scan with example tables")
    print("  python database_table_crawler.py --tables-file tables.txt")
    print()
    print("  # Scan specific tables in a directory")
    print("  python database_table_crawler.py --tables users orders products --directory ./src")
    print()
    print("  # Case-sensitive search with output file")
    print("  python database_table_crawler.py --tables User Order --case-sensitive --output report.txt")


def main():
    if len(sys.argv) > 1 and sys.argv[1] in ["--help", "-h", "help"]:
        show_help()
    else:
        run_crawler_interactive()


if __name__ == "__main__":
    main()
