#!/usr/bin/env python3
"""
Database Table Usage Crawler

This script crawls through all files in a directory and subdirectories
to check if specific database table names are being used in the code.
Useful for identifying unused database tables.
"""

import argparse
import json
import os
import re
from collections import defaultdict
from pathlib import Path


class DatabaseTableCrawler:
    def __init__(self, table_names: list[str], case_sensitive: bool = False):
        """
        Initialize the crawler with table names to search for.

        Args:
            table_names: List of database table names to search for
            case_sensitive: Whether to perform case-sensitive search

        """
        self.table_names = table_names
        self.case_sensitive = case_sensitive
        self.results = defaultdict(list)
        self.file_extensions = {
            ".py",
            ".sql",
        }

    def should_scan_file(self, file_path: Path) -> bool:
        """
        Determine if a file should be scanned based on its extension.

        Args:
            file_path: Path to the file

        Returns:
            True if file should be scanned, False otherwise

        """
        return file_path.suffix.lower() in self.file_extensions

    def search_in_file(self, file_path: Path) -> dict[str, list[tuple[int, str]]]:
        """
        Search for table names in a specific file.

        Args:
            file_path: Path to the file to search

        Returns:
            Dictionary mapping table names to list of (line_number, line_content) tuples

        """
        matches = defaultdict(list)

        try:
            with open(file_path, encoding="utf-8", errors="ignore") as file:
                for line_num, line in enumerate(file, 1):
                    for table_name in self.table_names:
                        # Create different search patterns
                        patterns = [
                            # Direct table name match (word boundary)
                            rf"\b{re.escape(table_name)}\b",
                            # Table name in quotes
                            rf'["\']({re.escape(table_name)})["\']',
                            # Table name with common prefixes/suffixes
                            rf"\b{re.escape(table_name)}s?\b",  # plural form
                            rf"\b{re.escape(table_name)}_\w+\b",  # with suffix
                            rf"\b\w+_{re.escape(table_name)}\b",  # with prefix
                        ]

                        for pattern in patterns:
                            flags = 0 if self.case_sensitive else re.IGNORECASE
                            if re.search(pattern, line, flags):
                                matches[table_name].append((line_num, line.strip()))
                                break  # Avoid duplicate matches for the same line

        except (UnicodeDecodeError, PermissionError, FileNotFoundError) as e:
            print(f"Warning: Could not read file {file_path}: {e}")

        return matches

    def crawl_directory(self, directory: str) -> dict[str, dict[str, list[tuple[int, str]]]]:
        """
        Crawl through directory and subdirectories to find table usage.

        Args:
            directory: Root directory to start crawling from

        Returns:
            Dictionary mapping file paths to table matches

        """
        directory_path = Path(directory)
        all_results = {}

        if not directory_path.exists():
            raise FileNotFoundError(f"Directory {directory} does not exist")

        print(f"Crawling directory: {directory_path.absolute()}")
        print(f"Looking for tables: {', '.join(self.table_names)}")
        print("-" * 50)

        file_count = 0
        for root, dirs, files in os.walk(directory_path):
            # Skip common directories that usually don't contain relevant code
            dirs[:] = [
                d
                for d in dirs
                if not d.startswith(".")
                and d
                not in {
                    "__pycache__",
                    "node_modules",
                    "venv",
                    ".venv",
                    "env",
                    ".git",
                    "build",
                    "dist",
                    "target",
                    "bin",
                    "obj",
                }
            ]

            for file in files:
                file_path = Path(root) / file

                if self.should_scan_file(file_path):
                    file_count += 1
                    if file_count % 100 == 0:
                        print(f"Processed {file_count} files...")

                    matches = self.search_in_file(file_path)
                    if matches:
                        relative_path = file_path.relative_to(directory_path)
                        all_results[str(relative_path)] = dict(matches)

        print(f"Finished processing {file_count} files")
        return all_results

    def generate_report(self, results: dict[str, dict[str, list[tuple[int, str]]]]) -> str:
        """
        Generate a human-readable report of the findings.

        Args:
            results: Results from crawl_directory

        Returns:
            Formatted report string

        """
        report = []
        report.append("DATABASE TABLE USAGE REPORT")
        report.append("=" * 50)
        report.append("")

        # Summary of tables found/not found
        tables_found = set()
        for file_matches in results.values():
            tables_found.update(file_matches.keys())

        tables_not_found = set(self.table_names) - tables_found

        report.append("SUMMARY:")
        report.append(f"- Total tables searched: {len(self.table_names)}")
        report.append(f"- Tables found in code: {len(tables_found)}")
        report.append(f"- Tables NOT found: {len(tables_not_found)}")
        report.append("")

        if tables_not_found:
            report.append("TABLES NOT FOUND (potentially unused):")
            for table in sorted(tables_not_found):
                report.append(f"  - {table}")
            report.append("")

        if tables_found:
            report.append("TABLES FOUND IN CODE:")
            for table in sorted(tables_found):
                report.append(f"  - {table}")
            report.append("")

        # Detailed findings
        if results:
            report.append("DETAILED FINDINGS:")
            report.append("-" * 30)

            for file_path, file_matches in sorted(results.items()):
                report.append(f"\nFile: {file_path}")
                for table_name, matches in file_matches.items():
                    report.append(f"  Table: {table_name}")
                    for line_num, line_content in matches:
                        report.append(f"    Line {line_num}: {line_content}")

        return "\n".join(report)


def main():
    parser = argparse.ArgumentParser(
        description="Crawl files to check database table usage",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python database_table_crawler.py --tables users orders products --directory ./src
  python database_table_crawler.py --tables-file tables.txt --case-sensitive
  python database_table_crawler.py --tables user_accounts --output report.txt
        """,
    )

    # Table input options
    table_group = parser.add_mutually_exclusive_group(required=True)
    table_group.add_argument(
        "--tables",
        nargs="+",
        help="List of table names to search for",
    )
    table_group.add_argument(
        "--tables-file",
        help="File containing table names (one per line)",
    )

    # Other options
    parser.add_argument(
        "--directory",
        default=".",
        help="Directory to crawl (default: current directory)",
    )
    parser.add_argument(
        "--case-sensitive",
        action="store_true",
        help="Perform case-sensitive search",
    )
    parser.add_argument(
        "--output",
        help="Output file for the report (default: print to console)",
    )
    parser.add_argument(
        "--json-output",
        help="Output results as JSON to specified file",
    )

    args = parser.parse_args()

    # Get table names
    if args.tables:
        table_names = args.tables
    else:
        try:
            with open(args.tables_file) as f:
                table_names = [line.strip() for line in f if line.strip()]
        except FileNotFoundError:
            print(f"Error: File {args.tables_file} not found")
            return 1

    if not table_names:
        print("Error: No table names provided")
        return 1

    # Run the crawler
    crawler = DatabaseTableCrawler(table_names, args.case_sensitive)

    try:
        results = crawler.crawl_directory(args.directory)

        # Generate and output report
        report = crawler.generate_report(results)

        if args.output:
            with open(args.output, "w") as f:
                f.write(report)
            print(f"Report saved to {args.output}")
        else:
            print(report)

        # Save JSON output if requested
        if args.json_output:
            with open(args.json_output, "w") as f:
                json.dump(results, f, indent=2)
            print(f"JSON results saved to {args.json_output}")

        return 0

    except Exception as e:
        print(f"Error: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
