import boto3

from utils.aws_utils import default_lambda_config

lambda_client = boto3.client("lambda", config=default_lambda_config)


def clean_lambda_alias(env: str, alias: str, dry_run=True):
    with open("../lambdas", "r") as lambdas:
        for _lambda in lambdas:
            _lambda = _lambda.replace("\n", "")
            try:
                function = lambda_client.get_function(FunctionName=f"{_lambda}-{env}", Qualifier=alias)
                arn = function["Configuration"]["FunctionArn"]
            except Exception as e:
                print(f"{_lambda}-{env}:{alias} error => {e}")
            else:
                print(f"delete function alias {arn}")
                if not dry_run:
                    lambda_client.delete_alias(FunctionName=arn, Name=alias)


def clean_unused_lambda_versions(env: str, dry_run=True):
    with open("../lambdas", "r") as lambdas:
        for _lambda in lambdas:
            _lambda = _lambda.replace("\n", "")
            try:
                function = lambda_client.get_function(FunctionName=f"{_lambda}-{env}")
                versions = _get_all_lambda_version(function["Configuration"]["FunctionArn"])
                version_with_alias = [alias["FunctionVersion"] for alias in lambda_client.list_aliases(FunctionName=function["Configuration"]["FunctionArn"])["Aliases"]]
                # Add current version to avoid deleting it
                version_with_alias.append(function["Configuration"]["Version"])
            except Exception as e:
                print(f"{_lambda}-{env} error => {e}")
            else:
                for version in versions:
                    if version["Version"] not in version_with_alias:
                        arn = version["FunctionArn"]
                        print(f"delete function version {arn}")
                        if not dry_run:
                            lambda_client.delete_function(FunctionName=arn)


def _get_all_lambda_version(arn: str) -> list:
    versions = []
    call_params = {"FunctionName": arn}
    while True:
        version_by_function = lambda_client.list_versions_by_function(**call_params)
        versions.extend(version_by_function["Versions"])
        call_params["Marker"] = version_by_function.get("NextMarker")
        if not call_params["Marker"]:
            break
    return versions


if __name__ == "__main__":
    dry_run = False
    # clean_lambda_alias('qta', 'v0', dry_run=dry_run)
    clean_unused_lambda_versions("qta", dry_run=dry_run)
    clean_unused_lambda_versions("qla", dry_run=dry_run)
