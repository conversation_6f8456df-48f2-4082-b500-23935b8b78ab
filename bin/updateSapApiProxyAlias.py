#!/usr/bin/env python3

import http.client
import json
import sys

import boto3


def get_oauth_token(client_id: str, client_secret: str, token_host: str) -> str:
    conn = http.client.HTTPSConnection(token_host)
    conn.request(
        method="POST",
        url="/oauth/token",
        headers={"Content-Type": "application/x-www-form-urlencoded"},
        body=f"client_id={client_id}&client_secret={client_secret}&grant_type=client_credentials",
    )
    res = conn.getresponse()
    data = res.read().decode("utf-8")
    json_data = json.loads(data)

    return f"Bearer {json_data.get('access_token')}"


def create_proxy_alias(token: str, host_alias: str, proxy_alias_name: str, target_version: str):
    conn = http.client.HTTPSConnection("eu10apiportal.cfapps.eu10.hana.ondemand.com")

    conn.request(
        method="GET",
        url="/apiportal/api/1.0/Management.svc/VirtualHosts",
        headers={"Authorization": token, "Accept": "application/json"},
    )

    with conn.getresponse() as res:
        if res.status == 200:
            data = res.read()
            data = json.loads(data)

            virtual_host = data["d"]["results"][0]["id"]
        else:
            raise Exception(f"create APIProxies ERROR {res.status} {res.reason}\n{res.read().decode('utf-8')}")

    conn.request(
        method="POST",
        url="/apiportal/api/1.0/Management.svc/APIProxies",
        headers={
            "Authorization": token,
            "Content-Type": "application/json",
            "Accept": "application/json",
        },
        body=f"""{{
"name":"Proxy_{proxy_alias_name}",
"version":"1",
"title":"Proxy {proxy_alias_name}",
"releaseStatus":"Active",
"releaseMetadata":"{{\\"reason\\":\\"\\"}}",
"description":null,
"isPublished":false,
"service_code":"REST",
"provider_name":"NONE",
"status_code":"REGISTERED",
"state":"DEPLOYED",
"proxyEndPoints":[
    {{
        "base_path":"/{proxy_alias_name}/",
        "name":"default",
        "isDefault":true,
        "apiResources":[],
        "conditionalFlows":[],
        "properties":[],
        "routeRules":[
            {{
                "name":"default",
                "targetEndPointName":"default",
                "sequence":1
            }}
        ],
        "virtualhosts":[{{"__metadata":{{"uri":"VirtualHosts('{virtual_host}')"}}}}]
    }}
],
"targetEndPoints":[{{"name":"default","isDefault":true,"url":"https://{host_alias}/{target_version}/","relativePath":null,"provider_id":"NONE","properties":[],"targetAPIProxyName":null}}],
"isVersioned":false,
"__metadata":{{"type":"apiportal.APIProxy"}}
}}""".replace("\n", ""),
    )
    with conn.getresponse() as res:
        if res.status != 201:
            raise Exception(f"create APIProxies ERROR {res.status} {res.reason}\n{res.read().decode('utf-8')}")


def update_proxy_alias(token: str, host_alias: str, proxy_alias_name: str, target_version: str):
    conn = http.client.HTTPSConnection("eu10apiportal.cfapps.eu10.hana.ondemand.com")

    conn.request(
        method="GET",
        url=f"/apiportal/api/1.0/Management.svc/APITargetEndPoints?$filter=FK_API_NAME%20eq%20'Proxy_{proxy_alias_name}'",
        headers={"Authorization": token, "Accept": "application/json"},
    )

    with conn.getresponse() as res:
        if res.status == 200:
            data = res.read()
            data = json.loads(data)

            if not data["d"]["results"]:
                create_proxy_alias(token, host_alias, proxy_alias_name, target_version)
            else:
                target_id = data["d"]["results"][0]["id"]
                conn.request(
                    method="POST",
                    url="/apiportal/api/1.0/Management.svc/$batch",
                    headers={
                        "Content-Type": "multipart/mixed; boundary=batch_cicd",
                        "Authorization": token,
                    },
                    body=f"""
--batch_cicd
Content-Type: multipart/mixed; boundary=changeset_cicd

--changeset_cicd
Content-Type: application/http
Content-Transfer-Encoding: binary

PUT APIProxies(name='Proxy_{proxy_alias_name}') HTTP/1.1
x-csrf-token: 3bdea51a69529d91-Gd1qvH0LAMp53Ozum0Fa46EvKJ0
Accept-Language: en
Accept: application/json
MaxDataServiceVersion: 2.0
DataServiceVersion: 2.0
RequestId: 5ba88a72-7eed-4f6a-878e-a28cf1b38f4e
Content-Type: application/json

{{"name":"Proxy_{proxy_alias_name}","title":"Proxy {proxy_alias_name}","description":"<p></p>","version":"1","status_code":"PUBLISHED","service_code":"REST","isPublished":false,"releaseStatus":"Active","successorAPI":null,"isUnmanaged":false,"releaseMetadata":"","provider_name":"NONE","state":"DEPLOYED"}}
--changeset_cicd
Content-Type: application/http
Content-Transfer-Encoding: binary

PUT APITargetEndPoints(id='{target_id}') HTTP/1.1
x-csrf-token: 3bdea51a69529d91-Gd1qvH0LAMp53Ozum0Fa46EvKJ0
Accept-Language: en
Accept: application/json
MaxDataServiceVersion: 2.0
DataServiceVersion: 2.0
RequestId: 5ba88a72-7eed-4f6a-878e-a28cf1b38f4e
Content-Type: application/json

{{"id":"{target_id}","name":"default","provider_id":"NONE","url":"https://{host_alias}/{target_version}/","relativePath":null,"isDefault":true,"properties":[],"targetAPIProxyName":null}}
--changeset_cicd--

--batch_cicd--
                """.replace("\n", "\r\n"),  # the API expect Windows like new lines
                )
                with conn.getresponse() as res:
                    if res.status >= 300:
                        raise Exception(f"update_proxy ERROR {res.status} {res.reason}\n{res.read().decode('utf-8')}")
        elif res.status == 404:
            create_proxy_alias(token, host_alias, proxy_alias_name, target_version)
        else:
            raise Exception(f"get APITargetEndPoints ERROR {res.status} {res.reason}\n{res.read().decode('utf-8')}")


def get_config_values(env_name: str) -> dict:
    # Create a Secrets Manager client
    session = boto3.session.Session()
    client = session.client(service_name="secretsmanager", region_name="eu-west-1")

    get_secret_value_response = client.get_secret_value(SecretId=f"MyResaAPI/SapApiDeploy/{env_name}")

    return json.loads(get_secret_value_response["SecretString"])


if __name__ == "__main__":
    """
    Usage : ./updateSapApiProxyAlias.py [environment] [alias] [version]

    This script will update an ApiProxy alias and on SAP API Portal.
    """
    if len(sys.argv) < 3:
        raise Exception("The environment and version are mandatory as positionals arguments\nUsage : ./updateSapApiProxyAlias.py [environment] [alias] [version]")

    # Set configuration variable
    env = sys.argv[1]
    alias = sys.argv[2]
    version = sys.argv[3]

    config_values = get_config_values(env)

    # start update
    token = get_oauth_token(
        config_values["client_id"],
        config_values["client_secret"],
        config_values["token_host"],
    )
    update_proxy_alias(token, config_values["host_alias"], alias, version)
