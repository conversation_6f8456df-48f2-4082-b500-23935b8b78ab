<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<APIProxy>
    <name>{api_name}_{api_version}</name>
    <title>{api_name}_{api_version}</title>
    <isVersioned>true</isVersioned>
    <service_code>REST</service_code>
    <version>{api_version}</version>
    <APIState>Active</APIState>
    <proxyEndPoints>
        <proxyEndPoint>
            <proxyEndPointName>default</proxyEndPointName>
        </proxyEndPoint>
    </proxyEndPoints>
    <targetEndPoints>
        <targetEndPoint>default</targetEndPoint>
    </targetEndPoints>
    <policies>
        <policy type="VerifyAPIKey">CheckAPIKey</policy>
        <policy type="RaiseFault">defaultRaiseFaultPolicy</policy>
        <policy type="Javascript">RemoveAwsHeaders</policy>
    </policies>
    <fileResources>
        <fileResource type="js">RemoveAwsHeaders</fileResource>
    </fileResources>
</APIProxy>
