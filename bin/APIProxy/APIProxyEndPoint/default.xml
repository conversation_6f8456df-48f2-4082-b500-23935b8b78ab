<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<ProxyEndPoint default="true">
    <name>default</name>
    <base_path>/{api_base_path}/{api_slug}</base_path>
    <properties/>
    <routeRules>
        <routeRule>
            <name>default</name>
            <targetEndPointName>default</targetEndPointName>
            <sequence>1</sequence>
            <faultRules/>
        </routeRule>
    </routeRules>
    <faultRules/>
    <preFlow>
        <name>PreFlow</name>
        <request>
            <isRequest>true</isRequest>
            <steps>
                <step>
                    <policy_name>CheckAPIKey</policy_name>
                    <sequence>1</sequence>
                    <!--                    No API key needed for those endpoints                    -->
                    <condition>NOT(
                        request.verb = "OPTIONS"
                        OR proxy.pathsuffix MatchesPath "/payements/mollie/update"
                        OR proxy.pathsuffix MatchesPath "/sms/incoming"
                        OR proxy.pathsuffix MatchesPath "/utilisateurs/edit/*/preferences"
                        OR proxy.pathsuffix MatchesPath "/panne_subscription/*/remove"
                        OR proxy.pathsuffix MatchesPath "/utilisateurs/activate/checkMail"
                        OR proxy.pathsuffix MatchesPath "/health"
                        OR proxy.pathsuffix MatchesPath "/documentation"
                        )
                    </condition>
                </step>
            </steps>
        </request>
    </preFlow>
    <postFlow>
        <name>PostFlow</name>
        <response>
            <isRequest>false</isRequest>
            <steps>
                <step>
                    <policy_name>RemoveAwsHeaders</policy_name>
                    <sequence>1</sequence>
                </step>
            </steps>
        </response>
    </postFlow>
    <conditionalFlows/>
</ProxyEndPoint>
