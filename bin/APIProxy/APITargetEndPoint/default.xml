<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<TargetEndPoint>
    <name>default</name>
    <provider_id>{provider_id}</provider_id>
    <additionalAPIProviders/>
    <isDefault>true</isDefault>
    <relativePath>{target_endpoint}</relativePath>
    <properties/>
    <faultRules/>
    <preFlow>
        <name>PreFlow</name>
        <request>
            <isRequest>true</isRequest>
            <steps/>
        </request>
    </preFlow>
    <postFlow>
        <name>PostFlow</name>
    </postFlow>
    <conditionalFlows/>
    <loadBalancerConfigurations>
        <isRetry>false</isRetry>
    </loadBalancerConfigurations>
</TargetEndPoint>
