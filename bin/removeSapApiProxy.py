#!/usr/bin/env python3

import http.client
import json
import sys
from typing import List

import boto3

from utils.log_utils import LogTime


class DuplicateBasePathError(Exception):
    pass


def get_oauth_token(client_id: str, client_secret: str, token_host: str) -> str:
    conn = http.client.HTTPSConnection(token_host)
    conn.request(
        method="POST",
        url="/oauth/token",
        headers={"Content-Type": "application/x-www-form-urlencoded"},
        body=f"client_id={client_id}&client_secret={client_secret}&grant_type=client_credentials",
    )
    res = conn.getresponse()
    data = res.read().decode("utf-8")
    json_data = json.loads(data)

    return f"Bearer {json_data.get('access_token')}"


def get_product_linked_to_api(api_proxy_name: str):
    with LogTime(f"get_product_linked_to_api {api_proxy_name=}"):
        # check if the product already exist or not
        conn = http.client.HTTPSConnection("eu10apiportal.cfapps.eu10.hana.ondemand.com")
        conn.request(
            method="GET",
            url=f"/apiportal/api/1.0/Management.svc/APIProxies(name='{api_proxy_name}')/apiProducts",
            headers={"Authorization": token, "Accept": "application/json"},
        )

        with conn.getresponse() as res:
            data = res.read().decode("utf-8")
            if res.status >= 300:
                raise Exception(f"get_product_linked_to_api ERROR {res.status} {res.reason}\n{data}")
            else:
                json_data = json.loads(data)
                products_name = [elem["name"] for elem in json_data["d"]["results"]]
                return products_name


def delete_api_proxy(token: str, api_proxy_name: str):
    endpoints = get_endpoint_list(token, api_proxy_name)

    products_name_linked = get_product_linked_to_api(api_proxy_name)
    total_endpoints = len(endpoints.items())
    for i, (top_path, sub_endpoints) in enumerate(endpoints.items()):
        product_name = f"{deploy_config.get('product_name')}_{top_path}"

        if product_name not in products_name_linked:
            print(f"[{i + 1}/{total_endpoints}] - Product {product_name} already unlinked from {api_proxy_name}, good !")
        else:
            print(f"[{i + 1}/{total_endpoints}] - Product {product_name} found in {api_proxy_name}, unlinking...")
            remove_proxy_from_product(token, api_proxy_name, product_name, sub_endpoints)

    conn = http.client.HTTPSConnection("eu10apiportal.cfapps.eu10.hana.ondemand.com")
    conn.request(
        method="GET",
        url=f"/apiportal/api/1.0/Management.svc/APIProxies(name='{api_proxy_name}')",
        headers={"Content-Type": "application/octet-stream", "Authorization": token},
    )
    with conn.getresponse() as res:
        if res.status == 404:
            print(f"{api_proxy_name} already removed")
            return

    conn = http.client.HTTPSConnection("eu10apiportal.cfapps.eu10.hana.ondemand.com")
    conn.request(
        method="DELETE",
        url=f"/apiportal/api/1.0/Management.svc/APIProxies(name='{api_proxy_name}')",
        headers={"Content-Type": "application/octet-stream", "Authorization": token},
    )
    with conn.getresponse() as res:
        if res.status >= 300:
            raise Exception(f"delete_api_proxy ERROR {res.status} {res.reason}\n{res.read().decode('utf-8')}")


def delete_product(token: str, product_name: str):
    conn = http.client.HTTPSConnection("eu10apiportal.cfapps.eu10.hana.ondemand.com")
    conn.request(
        method="DELETE",
        url=f"/apiportal/api/1.0/Management.svc/APIProducts(name='{product_name}')",
        headers={"Content-Type": "application/octet-stream", "Authorization": token},
    )
    with conn.getresponse() as res:
        if res.status >= 300:
            raise Exception(f"delete_api_proxy ERROR {res.status} {res.reason}\n{res.read().decode('utf-8')}")


def get_endpoint_list(token: str, api_proxy_name: str):
    # get endpoint info by proxy name
    conn = http.client.HTTPSConnection("eu10apiportal.cfapps.eu10.hana.ondemand.com")
    conn.request(
        method="GET",
        url=f"/apiportal/api/1.0/Management.svc/APIProxyEndPoints?$filter=FK_API_NAME%20eq%20'{api_proxy_name}'",
        headers={"Authorization": token, "Accept": "application/json"},
    )
    result = {"full_access": []}
    with conn.getresponse() as res:
        if res.status == 200:
            data = res.read()
            data = json.loads(data)

            proxy_endpoints_id = None
            results = data.get("d", {}).get("results", [])
            if results:
                proxy_endpoints_id = results[0].get("id")

            if proxy_endpoints_id:
                conn = http.client.HTTPSConnection("eu10apiportal.cfapps.eu10.hana.ondemand.com")
                conn.request(
                    method="GET",
                    url=f"/apiportal/api/1.0/Management.svc/APIProxyEndPoints('{proxy_endpoints_id}')/apiResources",
                    headers={"Authorization": token, "Accept": "application/json"},
                )

                with conn.getresponse() as res2:
                    if res2.status == 200:
                        data = res2.read()
                        data = json.loads(data)

                        for endpoint in data.get("d", {}).get("results", []):
                            path = endpoint["resource_path"]
                            if path != "SWAGGER_JSON":
                                top_path = path.split("/")[0]
                                if top_path not in result:
                                    result[top_path] = []
                                result[top_path].append({"path": path, "id": endpoint["id"]})
                    elif res2.status == 404:
                        pass
                    else:
                        raise Exception(f"get_top_endpoint_list ERROR {res2.status} {res2.reason}\n{res2.read().decode('utf-8')}")
        elif res.status == 404:
            pass
        else:
            raise Exception(f"get_top_endpoint_list ERROR {res.status} {res.reason}\n{res.read().decode('utf-8')}")

    return result


def remove_proxy_from_product(token, api_proxy_name: str, product_name: str, endpoints: List[dict]):
    with LogTime(f"remove_proxy_from_product {api_proxy_name=} {product_name=}"):
        conn = http.client.HTTPSConnection("eu10apiportal.cfapps.eu10.hana.ondemand.com")

        # check if the proxy already exist in the product
        conn.request(
            method="GET",
            url=f"/apiportal/api/1.0/Management.svc/APIProducts(name='{product_name}')/apiProxies('{api_proxy_name}')",
            headers={"Authorization": token},
        )
        with conn.getresponse() as res:
            if res.status == 404:
                return
            elif res.status == 200:
                remove_endpoints = "\n".join(
                    f"""--changeset_cicd
Content-Type: application/http
Content-Transfer-Encoding: binary

DELETE APIProducts(name='{product_name}')/$links/apiResources('{endpoint["id"]}') HTTP/1.1


"""
                    for endpoint in endpoints
                )

                conn = http.client.HTTPSConnection("eu10apiportal.cfapps.eu10.hana.ondemand.com")
                conn.request(
                    method="POST",
                    url="/apiportal/api/1.0/Management.svc/$batch",
                    headers={
                        "Content-Type": "multipart/mixed; boundary=batch_cicd",
                        "Authorization": token,
                    },
                    body=f"""--batch_cicd
Content-Type: multipart/mixed; boundary=changeset_cicd

--changeset_cicd
Content-Type: application/http
Content-Transfer-Encoding: binary

PATCH APIProducts(name='{product_name}') HTTP/1.1
Content-Type: application/json

{{"name":"{product_name}"}}
--changeset_cicd
Content-Type: application/http
Content-Transfer-Encoding: binary

DELETE APIProducts(name='{product_name}')/$links/apiProxies('{api_proxy_name}') HTTP/1.1


--changeset_cicd
Content-Type: application/http
Content-Transfer-Encoding: binary

DELETE APIProxies(name='{api_proxy_name}')/$links/apiProducts('{product_name}') HTTP/1.1


{remove_endpoints}
--changeset_cicd--

--batch_cicd--""".replace("\n", "\r\n"),  # the API expect Windows like new lines
                )
                with conn.getresponse() as res:
                    data = res.read().decode("utf-8")
                    if res.status == 400 and "APIPROXY_NOT_LINKED_ERROR" in data:
                        delete_product(token, product_name)
                    elif res.status >= 300:
                        raise Exception(f"remove_from_product ERROR {res.status} {res.reason}\n{data}")
            else:
                raise Exception(f"get_product ERROR {res.status} {res.reason}\n{res.read().decode('utf-8')}")


def get_config_values(env_name: str) -> dict:
    # Create a Secrets Manager client
    session = boto3.session.Session()
    client = session.client(service_name="secretsmanager", region_name="eu-west-1")

    get_secret_value_response = client.get_secret_value(SecretId=f"MyResaAPI/SapApiDeploy/{env_name}")

    return json.loads(get_secret_value_response["SecretString"])


if __name__ == "__main__":
    """
    Usage: removeSapApiProxy.py [environment] [version] [alias] [target]

    Arguments:
    [environment]        - deployment environment. Required argument 
    [version]            - version of the API proxy. Required argument
    [alias]              - alias of the API proxy. Optional argument
    [target]             - target endpoint of the API proxy. Optional argument

    Example:
    To deploy to development environment with version 1.0.0 and alias as myalias and target as mytarget
      >>> python removeSapApiProxy.py dev 1.0.0 myalias mytarget
    """
    if len(sys.argv) < 3:
        raise Exception(
            "The environment and version are mandatory as positionals arguments\nUsage : ./removeSapApiProxy.py [environment] [version] [alias|optional] [target|optional]"
        )

    # Set configuration variable
    env = sys.argv[1]
    version = sys.argv[2]
    alias = sys.argv[3] if len(sys.argv) > 3 else None
    target = sys.argv[4] if len(sys.argv) > 4 else None
    env_name = env

    if env not in ("dev", "qta", "qla", "production"):
        env_name = "sandbox"

    config_values = get_config_values(env_name)

    deploy_config = {
        # ---- API config ----
        "api_name": f"MyResaApiProxy_{alias}" if alias else "MyResaApiProxy",
        "api_base_path": alias or version,
        "api_version": f"v_{version.replace('.', '').replace('-', '').replace('hotfix', 'hf')}",
        "api_slug": "",
        "host_alias": config_values["host_alias"],
        "provider_id": config_values["provider_id"],
        "target_endpoint": f"/{(target or version).replace('.', '-')}",
        # ---- Product config ----
        "product_name": "MyResaApi",
    }

    if env_name == "sandbox":
        deploy_config["provider_id"] += env
        deploy_config["api_name"] += f"_{env}"
        deploy_config["api_slug"] += f"sandbox-{env}"
        deploy_config["product_name"] += f"_{env}"

    if env_name in ("dev", "sandbox"):
        deploy_config["target_endpoint"] = "/v0"

    # start creation and deploy
    token = get_oauth_token(
        config_values["client_id"],
        config_values["client_secret"],
        config_values["token_host"],
    )
    delete_api_proxy(token, f"{deploy_config.get('api_name')}_{deploy_config.get('api_version')}")
