#!/bin/bash
# Usage : ./deploy.sh [profile] [workspace=dev]

fail() {
    printf '%s\n' "$1" >&2
    exit "${2-1}"
}

# --- set variables ---


ENV=${1:-"dev"}

echo "ENV=$ENV"
export PYTHONPATH="${PYTHONPATH}:${PWD}/utils"

# --- END set variables ---
version=$(./bin/get_version.sh "$ENV")

echo "Deploy SAP API Proxy $ENV $version"
if [ $ENV == 'dev' ] || [ $ENV == "qta" ] || [[ $ENV =~ "sandbox-".* ]]; then
  ./bin/deploySapApiProxy.py $ENV v0 || fail "cannot deploy SAP API Proxy v0"
elif [ $ENV == "qla" ] || [ $ENV == "production" ]; then
  ./bin/deploySapApiProxy.py $ENV $version || fail "cannot deploy SAP API Proxy"
  ./bin/updateSapApiProxyAlias.py $ENV latest $version || fail "cannot update SAP API Proxy latest"
else
  ./bin/deploySapApiProxy.py $ENV $version || fail "cannot deploy SAP API Proxy"
fi
