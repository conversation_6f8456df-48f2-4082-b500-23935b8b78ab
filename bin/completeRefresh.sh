#!/bin/bash
# Usage : ./completeRefresh.sh [profile] [workspace=dev]

PROFILE=$1
ENV=${2:-"dev"}

if [ -n "${PROFILE}" ]; then
    export AWS_PROFILE=$PROFILE
fi

fail() {
    printf '%s\n' "$1" >&2
    exit "${2-1}"
}

./bin/deploy.sh "$@" || fail "deployment failed"
./bin/terraform-only.sh "$PROFILE" "$ENV" || fail "terraform deployment failed"

if [ $ENV == "qla" ] || [ $ENV == "production" ] ; then
    ./bin/eject-stage.sh "$ENV" || fail "resources ejection failed"
fi