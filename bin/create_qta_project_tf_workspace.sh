#!/bin/bash


ENV=$1

if ! [[ $ENV =~ "qta_".* ]]; then
  ENV="qta_"$ENV
fi

cd ./terraform

terraform init -force-copy
terraform workspace select qta_web-resa-proj
terraform state pull > qta.tfstate

terraform workspace new $ENV
terraform state push qta.tfstate
rm qta.tfstate

terraform state rm 'aws_api_gateway_stage.MyResaAPI_Stage'
terraform state rm 'aws_api_gateway_deployment.MyResaAPI_Deployment'
terraform state rm 'aws_api_gateway_base_path_mapping.base_path_mapping'
terraform state rm 'aws_api_gateway_method_settings.general_settings'
terraform state rm 'aws_wafv2_web_acl_association.APIGatewayWAFAssociation'

lambda_alias=$(terraform state list | grep 'aws_lambda_alias.lambda_alias')
for alias in ${lambda_alias[*]}
do
    terraform state rm $alias
done

echo workspace $ENV created