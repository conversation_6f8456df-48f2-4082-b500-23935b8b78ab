#!/bin/bash
#Eject the stage from terraform state
#Run this script imply to increment stage version in terraform variables

ENV=$(echo "$1" | tr '[:upper:]' '[:lower:]')
if [ -z "${ENV}" ]; then
    echo "Usage : ./bin/eject-stage.sh env"
    exit
fi

cd ./terraform

terraform init -force-copy
terraform workspace select $ENV

terraform state rm 'aws_api_gateway_stage.MyResaAPI_Stage'
terraform state rm 'aws_api_gateway_deployment.MyResaAPI_Deployment'
terraform state rm 'aws_api_gateway_base_path_mapping.base_path_mapping'
terraform state rm 'aws_api_gateway_method_settings.general_settings'
terraform state rm 'aws_wafv2_web_acl_association.APIGatewayWAFAssociation'

lambda_alias=$(terraform state list | grep 'aws_lambda_alias.lambda_alias')

for alias in ${lambda_alias[*]}
do
    terraform state rm $alias
done