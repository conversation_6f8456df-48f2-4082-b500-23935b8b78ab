#!/usr/bin/env python3

import subprocess
import sys
import threading

failed = False


def fail(message, code=1):
    global failed
    failed = True
    print(message, file=sys.stderr)
    sys.exit(code)


def deploy_lambda(name, env):
    try:
        result = subprocess.run(
            ["./bin/updateLambda.sh", name, env],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
        )
        if result.returncode == 0:
            print(f"Lambda {name} code uploaded")
        else:
            fail(f"Cannot deploy lambda {name}. Return code: {result.returncode}\nStandard Output: {result.stdout}\nError Output: {result.stderr}")
    except subprocess.CalledProcessError as e:
        fail(f"Error while executing subprocess: {e}")


if len(sys.argv) < 2:
    fail("Usage : ./deploy-lambdas.py env")

ENV = sys.argv[1]

with open("./lambdas") as f:
    lambdas = f.read().splitlines()

threads = []
for lambda_name in lambdas:
    thread = threading.Thread(target=deploy_lambda, args=(lambda_name, ENV))
    thread.start()
    threads.append(thread)

# Attendre la fin de tous les threads
for thread in threads:
    thread.join()

if failed:
    print("An error occurred.")
    sys.exit(1)
else:
    print("All lambdas code was uploaded successfully.")
    sys.exit(0)
