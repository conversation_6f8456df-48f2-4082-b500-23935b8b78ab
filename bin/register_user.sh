#!/bin/bash
if [ -z "$3" ]; then
    echo "Usage : ./bin/register_user_with_ean.sh user prenom nom"
    exit 1
fi

if ! command -v jq &> /dev/null
then
    echo "you must install jq"
    exit
fi

API="https://api-acceptance.resa.be/v0"

USER=$1
PRENOM=$2
NOM=$3
PASSWORD="Welcome@2020"

ghost=$(curl -X POST -s "$API/utilisateurs" | jq -r '.SessionId')

curl -X POST "$API/utilisateurs/activate/sendMail?Callback=http%3A%2F%2Fresa.be%2Factivate" -s -H "SessionId: $ghost" -d "{\"Email\":\"$<EMAIL>\"}"

echo "\n"
python -mwebbrowser "http://www.yopmail.com/gen-rss.php?c=$USER"
read -p "Token : " token

curl -X POST "$API/utilisateurs/activate?Token=$token" -H "SessionId: $ghost" -d "{\"Password\":\"$PASSWORD\",\"Firstname\":\"$PRENOM\",\"Lastname\":\"$NOM\",\"Phone\":\"0000\",\"Adresse\":{\"Rue\":\"mock street\",\"NumRue\":\"5\",\"Localite\":\"mock city\",\"Cdpostal\":\"0000\"}}"