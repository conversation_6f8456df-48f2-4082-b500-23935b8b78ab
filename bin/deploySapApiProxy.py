#!/usr/bin/env python3

import base64
import contextlib
import http.client
import json
import os
import subprocess
import sys
import zipfile
from io import Bytes<PERSON>
from typing import List

import boto3

from utils.log_utils import LogTime


class DuplicateBasePathError(Exception):
    pass


def fill_template_to_zip(template_replace_map, env_name) -> bytes:
    with LogTime("fill_template_to_zip"):
        # Generate openapi file
        subprocess.run(["./bin/genOpenapi.py", "-e", env_name])

        with BytesIO() as zip_data:
            with zipfile.ZipFile(zip_data, "w") as zip_handler:
                template_replace_map["api_ressource_names"] = ""

                # Create ressource (path) and doc based on openapi spec
                with open("./resources/myresaapi_openapi_doc.json", "r") as openapi_doc_file:
                    openapi_doc_str = openapi_doc_file.read()
                    openapi_doc = json.loads(openapi_doc_str)
                    paths = openapi_doc.get("paths", {})

                    for path in paths:
                        content = f"""<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<APIResource xmlns="http://www.sap.com/apimgmt">
    <name>{path}</name>
    <title>{path}</title>
    <canShowGet>{"true" if "get" in paths[path] else "false"}</canShowGet>
    <canShowPost>{"true" if "post" in paths[path] else "false"}</canShowPost>
    <canShowPut>{"true" if "put" in paths[path] else "false"}</canShowPut>
    <canShowDelete>{"true" if "delete" in paths[path] else "false"}</canShowDelete>
    <canShowHead>false</canShowHead>
    <canShowOption>false</canShowOption>
    <canShowPatch>{"true" if "patch" in paths[path] else "false"}</canShowPatch>
    <isGetChecked>{"true" if "get" in paths[path] else "false"}</isGetChecked>
    <isPostChecked>{"true" if "post" in paths[path] else "false"}</isPostChecked>
    <isPutChecked>{"true" if "put" in paths[path] else "false"}</isPutChecked>
    <isDeleteChecked>{"true" if "delete" in paths[path] else "false"}</isDeleteChecked>
    <isHeadChecked>false</isHeadChecked>
    <isOptionChecked>false</isOptionChecked>
    <isPatchChecked>{"true" if "patch" in paths[path] else "false"}</isPatchChecked>
    <resource_path>{path[1:]}</resource_path>
    <proxyEndPointName>default</proxyEndPointName>
    <documentations>
        <documentation locale="en" mimeType="HTML">{path[1:]}</documentation>
    </documentations>
</APIResource>
    """
                        zip_handler.writestr(f"APIProxy/APIResource{path}.xml", content)
                        zip_handler.writestr(
                            f"APIProxy/Documentation{path}_en.html",
                            json.dumps(paths[path]),
                        )

                with contextlib.chdir("./bin"):
                    for root, dirs, files in os.walk("APIProxy"):
                        path = root.replace("\\", "/")

                        for file in files:
                            with open(f"{path}/{file}", "r") as template_file:
                                template = template_file.read()
                                if file.endswith(".js"):
                                    formatted_content = template  # no templating for js currently
                                else:
                                    formatted_content = template.format_map(template_replace_map)

                                zip_handler.writestr(f"{path}/{file}", formatted_content)

            return zip_data.getvalue()


def get_oauth_token(client_id: str, client_secret: str, token_host: str) -> str:
    with LogTime("get_oauth_token"):
        # Get OAuth token
        conn = http.client.HTTPSConnection(token_host)
        conn.request(
            method="POST",
            url="/oauth/token",
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            body=f"client_id={client_id}&client_secret={client_secret}&grant_type=client_credentials",
        )
        res = conn.getresponse()
        data = res.read().decode("utf-8")
        json_data = json.loads(data)

        return f"Bearer {json_data.get('access_token')}"


def replace_or_create_api_proxy(token: str, api_proxy_name: str, api_proxy_zip: bytes):
    with LogTime(f"replace_or_create_api_proxy {api_proxy_name}"):
        # check if the api proxy already exist or not
        conn = http.client.HTTPSConnection("eu10apiportal.cfapps.eu10.hana.ondemand.com")
        conn.request(
            method="GET",
            url=f"/apiportal/api/1.0/Management.svc/APIProxies(name='{api_proxy_name}')",
            headers={"Authorization": token},
        )
        with conn.getresponse() as res:
            if res.status == 200:
                if os.environ.get("API_PROXI_FORCE_REPLACE"):
                    delete_api_proxy(
                        token,
                        f"{deploy_config.get('api_name')}_{deploy_config.get('api_version')}",
                    )
            elif res.status == 404:
                try:
                    create_api_proxy(token, api_proxy_zip)
                except DuplicateBasePathError:
                    if os.environ.get("API_PROXI_FORCE_REPLACE"):
                        part = deploy_config.get("api_name").split("_")
                        delete_api_proxy(token, f"{part[0]}_v_{part[1]}")
                        create_api_proxy(token, api_proxy_zip)
            else:
                raise Exception(f"replace_or_create_api_proxy ERROR {res.status} {res.reason}\n{res.read().decode('utf-8')}")


def create_api_proxy(token: str, api_proxy_zip: bytes):
    with LogTime("create_api_proxy"):
        conn = http.client.HTTPSConnection("eu10apiportal.cfapps.eu10.hana.ondemand.com")
        conn.request(
            method="POST",
            url="/apiportal/api/1.0/Transport.svc/APIProxies",
            headers={
                "Content-Type": "application/octet-stream",
                "Authorization": token,
            },
            body=base64.b64encode(api_proxy_zip),
        )
        with conn.getresponse() as res:
            if res.status >= 300:
                message = res.read().decode("utf-8")
                if "DUPLICATE_BASE_PATH_ERROR" in message:
                    raise DuplicateBasePathError
                else:
                    raise Exception(f"create_api_proxy ERROR {res.status} {res.reason}\n{message}")


def delete_api_proxy(token: str, api_proxy_name: str):
    with LogTime(f"delete_api_proxy {api_proxy_name}"):
        endpoints = get_endpoint_list(token, api_proxy_name)
        for top_path, sub_endpoints in endpoints.items():
            remove_proxy_from_product(
                token,
                api_proxy_name,
                f"{deploy_config.get('product_name')}_{top_path}",
                sub_endpoints,
            )

        conn = http.client.HTTPSConnection("eu10apiportal.cfapps.eu10.hana.ondemand.com")
        conn.request(
            method="DELETE",
            url=f"/apiportal/api/1.0/Management.svc/APIProxies(name='{api_proxy_name}')",
            headers={
                "Content-Type": "application/octet-stream",
                "Authorization": token,
            },
        )
        with conn.getresponse() as res:
            if res.status >= 300:
                raise Exception(f"delete_api_proxy ERROR {res.status} {res.reason}\n{res.read().decode('utf-8')}")


def delete_product(token: str, product_name: str):
    with LogTime(f"delete_product {product_name}"):
        conn = http.client.HTTPSConnection("eu10apiportal.cfapps.eu10.hana.ondemand.com")
        conn.request(
            method="DELETE",
            url=f"/apiportal/api/1.0/Management.svc/APIProducts(name='{product_name}')",
            headers={
                "Content-Type": "application/octet-stream",
                "Authorization": token,
            },
        )
        with conn.getresponse() as res:
            if res.status >= 300:
                raise Exception(f"delete_api_proxy ERROR {res.status} {res.reason}\n{res.read().decode('utf-8')}")


def get_endpoint_list(token: str, api_proxy_name: str):
    with LogTime(f"get_endpoint_list {api_proxy_name}"):
        # get endpoint info by proxy name
        conn = http.client.HTTPSConnection("eu10apiportal.cfapps.eu10.hana.ondemand.com")
        conn.request(
            method="GET",
            url=f"/apiportal/api/1.0/Management.svc/APIProxyEndPoints?$filter=FK_API_NAME%20eq%20'{api_proxy_name}'",
            headers={"Authorization": token, "Accept": "application/json"},
        )
        result = {"full_access": []}
        with conn.getresponse() as res:
            if res.status == 200:
                data = res.read()
                data = json.loads(data)

                proxy_endpoints_id = None
                results = data.get("d", {}).get("results", [])
                if results:
                    proxy_endpoints_id = results[0].get("id")

                if proxy_endpoints_id:
                    conn = http.client.HTTPSConnection("eu10apiportal.cfapps.eu10.hana.ondemand.com")
                    conn.request(
                        method="GET",
                        url=f"/apiportal/api/1.0/Management.svc/APIProxyEndPoints('{proxy_endpoints_id}')/apiResources",
                        headers={"Authorization": token, "Accept": "application/json"},
                    )

                    with conn.getresponse() as res2:
                        if res2.status == 200:
                            data = res2.read()
                            data = json.loads(data)

                            for endpoint in data.get("d", {}).get("results", []):
                                path = endpoint["resource_path"]
                                if path != "SWAGGER_JSON":
                                    top_path = path.split("/")[0]
                                    if top_path not in result:
                                        result[top_path] = []
                                    result[top_path].append({"path": path, "id": endpoint["id"]})
                        elif res2.status == 404:
                            pass
                        else:
                            raise Exception(f"get_top_endpoint_list ERROR {res2.status} {res2.reason}\n{res2.read().decode('utf-8')}")
            elif res.status == 404:
                pass
            else:
                raise Exception(f"get_top_endpoint_list ERROR {res.status} {res.reason}\n{res.read().decode('utf-8')}")

        return result


def update_or_create_product(token: str, api_proxy_name: str, product_name: str, endpoints: List[dict]):
    # check if the product already exist or not
    conn = http.client.HTTPSConnection("eu10apiportal.cfapps.eu10.hana.ondemand.com")
    conn.request(
        method="GET",
        url=f"/apiportal/api/1.0/Management.svc/APIProducts(name='{product_name}')",
        headers={"Authorization": token},
    )
    with conn.getresponse() as res:
        if res.status == 404:
            create_product(token, api_proxy_name, product_name, endpoints)
        elif res.status == 200:
            update_product(token, api_proxy_name, product_name, endpoints)
        else:
            raise Exception(f"get_product ERROR {res.status} {res.reason}\n{res.read().decode('utf-8')}")


def update_product(token: str, api_proxy_name: str, product_name: str, endpoints: List[dict]):
    with LogTime(f"update_product {api_proxy_name=} {product_name=}"):
        add_endpoints = "\n".join(
            f"""--changeset_cicd
Content-Type: application/http
Content-Transfer-Encoding: binary

POST APIProducts(name='{product_name}')/$links/apiResources HTTP/1.1
Content-Type: application/json

{{"uri":"APIResources('{endpoint["id"]}')"}}
    """
            for endpoint in endpoints
        )

        conn = http.client.HTTPSConnection("eu10apiportal.cfapps.eu10.hana.ondemand.com")
        conn.request(
            method="POST",
            url="/apiportal/api/1.0/Management.svc/$batch",
            headers={
                "Content-Type": "multipart/mixed; boundary=batch_cicd",
                "Authorization": token,
            },
            body=f"""--batch_cicd
Content-Type: multipart/mixed; boundary=changeset_cicd

--changeset_cicd
Content-Type: application/http
Content-Transfer-Encoding: binary

PATCH APIProducts(name='{product_name}') HTTP/1.1
Content-Type: application/json

{{"name":"{product_name}"}}
--changeset_cicd
Content-Type: application/http
Content-Transfer-Encoding: binary

POST APIProducts(name='{product_name}')/$links/apiProxies HTTP/1.1
Content-Type: application/json

{{"uri":"APIProxies('{api_proxy_name}')"}}
--changeset_cicd
Content-Type: application/http
Content-Transfer-Encoding: binary

POST APIProxies(name='{api_proxy_name}')/$links/apiProducts HTTP/1.1
Content-Type: application/json

{{"uri":"APIProducts('{product_name}')"}}
{add_endpoints}
--changeset_cicd--

--batch_cicd--""".replace("\n", "\r\n"),  # the API expect Windows like new lines
        )
        with conn.getresponse() as res:
            if res.status >= 300:
                raise Exception(f"update_product ERROR {res.status} {res.reason}\n{res.read().decode('utf-8')}")


def remove_proxy_from_product(token: str, api_proxy_name: str, product_name: str, endpoints: List[dict]):
    with LogTime(f"remove_proxy_from_product {api_proxy_name=} {product_name=}"):
        conn = http.client.HTTPSConnection("eu10apiportal.cfapps.eu10.hana.ondemand.com")

        # check if the proxy already exist in the product
        conn.request(
            method="GET",
            url=f"/apiportal/api/1.0/Management.svc/APIProducts(name='{product_name}')/apiProxies('{api_proxy_name}')",
            headers={"Authorization": token},
        )
        with conn.getresponse() as res:
            if res.status == 404:
                return
            elif res.status == 200:
                remove_endpoints = "\n".join(
                    f"""--changeset_cicd
Content-Type: application/http
Content-Transfer-Encoding: binary

DELETE APIProducts(name='{product_name}')/$links/apiResources('{endpoint["id"]}') HTTP/1.1


"""
                    for endpoint in endpoints
                )

                conn = http.client.HTTPSConnection("eu10apiportal.cfapps.eu10.hana.ondemand.com")
                conn.request(
                    method="POST",
                    url="/apiportal/api/1.0/Management.svc/$batch",
                    headers={
                        "Content-Type": "multipart/mixed; boundary=batch_cicd",
                        "Authorization": token,
                    },
                    body=f"""--batch_cicd
Content-Type: multipart/mixed; boundary=changeset_cicd

--changeset_cicd
Content-Type: application/http
Content-Transfer-Encoding: binary

PATCH APIProducts(name='{product_name}') HTTP/1.1
Content-Type: application/json

{{"name":"{product_name}"}}
--changeset_cicd
Content-Type: application/http
Content-Transfer-Encoding: binary

DELETE APIProducts(name='{product_name}')/$links/apiProxies('{api_proxy_name}') HTTP/1.1


--changeset_cicd
Content-Type: application/http
Content-Transfer-Encoding: binary

DELETE APIProxies(name='{api_proxy_name}')/$links/apiProducts('{product_name}') HTTP/1.1


{remove_endpoints}
--changeset_cicd--

--batch_cicd--""".replace("\n", "\r\n"),  # the API expect Windows like new lines
                )
                with conn.getresponse() as res:
                    data = res.read().decode("utf-8")
                    if res.status == 400 and "APIPROXY_NOT_LINKED_ERROR" in data:
                        delete_product(token, product_name)
                    elif res.status >= 300:
                        print(f"remove_from_product ERROR {res.status} {res.reason}\n{data}")
            else:
                raise Exception(f"get_product ERROR {res.status} {res.reason}\n{res.read().decode('utf-8')}")


def create_product(token: str, api_proxy_name: str, product_name: str, endpoints: List[dict]):
    with LogTime(f"create_product {api_proxy_name=} {product_name=}"):
        conn = http.client.HTTPSConnection("eu10apiportal.cfapps.eu10.hana.ondemand.com")
        conn.request(
            method="POST",
            url="/apiportal/api/1.0/Management.svc/APIProducts",
            headers={"Content-Type": "application/json", "Authorization": token},
            body=json.dumps(
                {
                    "name": product_name,
                    "isPublished": True,
                    "status_code": "PUBLISHED",
                    "title": product_name,
                    "apiProxies": [{"__metadata": {"uri": f"APIProxies(name='{api_proxy_name}')"}}],
                    "apiResources": [{"__metadata": {"uri": f"APIResources('{endpoint['id']}')"}} for endpoint in endpoints],
                }
            ),
        )
        with conn.getresponse() as res:
            if res.status >= 300:
                raise Exception(f"create_product ERROR {res.status} {res.reason}\n{res.read().decode('utf-8')}")


def get_config_values(env_name: str) -> dict:
    # Create a Secrets Manager client
    session = boto3.session.Session()
    client = session.client(service_name="secretsmanager", region_name="eu-west-1")

    get_secret_value_response = client.get_secret_value(SecretId=f"MyResaAPI/SapApiDeploy/{env_name}")

    return json.loads(get_secret_value_response["SecretString"])


def get_product_linked_to_api(api_proxy_name: str):
    with LogTime(f"get_product_linked_to_api {api_proxy_name=}"):
        # check if the product already exist or not
        conn = http.client.HTTPSConnection("eu10apiportal.cfapps.eu10.hana.ondemand.com")
        conn.request(
            method="GET",
            url=f"/apiportal/api/1.0/Management.svc/APIProxies(name='{api_proxy_name}')/apiProducts",
            headers={"Authorization": token, "Accept": "application/json"},
        )

        with conn.getresponse() as res:
            data = res.read().decode("utf-8")
            if res.status >= 300:
                raise Exception(f"get_product_linked_to_api ERROR {res.status} {res.reason}\n{data}")
            else:
                json_data = json.loads(data)
                products_name = [elem["name"] for elem in json_data["d"]["results"]]
                return products_name


if __name__ == "__main__":
    """
    Usage : ./deploySapApiProxy.py [environment] [version] [alias]

    This script will create an ApiProxy and deploy it on SAP.
    The proxy endpoint on SAP will look like this :
        https://myresaapi-dev.prod.apimanagement.eu10.hana.ondemand.com/0.1.66/sandbox-david/jobs
        |------------------------- host_alias ------------------------|version|--- slug ----|endpoint|
        DETAILS :
        host_alias -> configured in SAP API Portal, change for each environments
        version -> api version, with '_' instead of '.' and preceded by a 'v' (this is the format SAP want)
        api_slug -> always null excepted for sandbox where user is added (ex: sandbox-david)
        endpoint -> the endpoint to call on the AWS API Gateway
    """
    if len(sys.argv) < 3:
        raise Exception(
            "The environment and version are mandatory as positionals arguments\nUsage : ./deploySapApiProxy.py [environment] [version] [alias|optional] [target|optional]"
        )

    # Set configuration variables
    env = sys.argv[1]
    version = sys.argv[2]
    alias = sys.argv[3] if len(sys.argv) > 3 else None
    target = sys.argv[4] if len(sys.argv) > 4 else None
    env_name = env

    if env not in ("dev", "qta", "qla", "production"):
        env_name = "sandbox"

    config_values = get_config_values(env_name)

    deploy_config = {
        # ---- API config ----
        "api_name": f"MyResaApiProxy_{alias}" if alias else "MyResaApiProxy",
        "api_base_path": alias or version,
        "api_version": f"v_{version.replace('.', '').replace('-', '').replace('hotfix', 'hf')}",
        "api_slug": "",
        "host_alias": config_values["host_alias"],
        "provider_id": config_values["provider_id"],
        "target_endpoint": f"/{(target or version).replace('.', '-')}",
        # ---- Product config ----
        "product_name": "MyResaApi",
    }

    if env_name == "sandbox":
        deploy_config["provider_id"] += env
        deploy_config["api_name"] += f"_{env}"
        deploy_config["api_slug"] += f"sandbox-{env}"
        deploy_config["product_name"] += f"_{env}"

    if env_name in ("dev", "sandbox"):
        deploy_config["target_endpoint"] = "/v0"

    # start creation and deploy
    zip_data = fill_template_to_zip(deploy_config, env_name)
    token = get_oauth_token(
        config_values["client_id"],
        config_values["client_secret"],
        config_values["token_host"],
    )

    replace_or_create_api_proxy(
        token,
        f"{deploy_config.get('api_name')}_{deploy_config.get('api_version')}",
        zip_data,
    )
    try:
        api_proxy_name = f"{deploy_config.get('api_name')}_{deploy_config.get('api_version')}"
        endpoints = get_endpoint_list(token, f"{deploy_config.get('api_name')}_{deploy_config.get('api_version')}")

        products_name_linked = get_product_linked_to_api(api_proxy_name)
        total_endpoints = len(endpoints.items())
        for i, (top_path, sub_endpoints) in enumerate(endpoints.items()):
            product_name = f"{deploy_config.get('product_name')}_{top_path}"

            if product_name in products_name_linked:
                print(f"[{i + 1}/{total_endpoints}] - Product {product_name} already exist in {api_proxy_name}, good !")
            else:
                print(f"[{i + 1}/{total_endpoints}] - Product {product_name} not found in {api_proxy_name}, linking...")
                update_or_create_product(token, api_proxy_name, product_name, sub_endpoints)
    except Exception as e:
        # Exception is expected for sandbox since their version number may not be increased
        # In this case, the link to product cause a 500 error
        if env_name == "sandbox" and "update_product ERROR 500" in str(e):
            print(e)
        else:
            raise e
