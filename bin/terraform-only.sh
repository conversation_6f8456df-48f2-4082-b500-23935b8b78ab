#!/bin/bash
# Usage : ./terraform-only.sh [profile] [workspace=dev] [parallelism=10]

fail() {
    printf '%s\n' "$1" >&2
    exit "${2-1}"
}

PROFILE=$1
ENV=$(echo "$2" | tr '[:upper:]' '[:lower:]')
PARALLELISM=${3:-"10"}

if [ -n "${PROFILE}" ]; then
    export AWS_PROFILE=$PROFILE
fi

version=$(./bin/get_version.sh "$ENV")
echo "Deploy version $version with workspace $ENV and $PROFILE AWS profile"

cd ./terraform

terraform init -force-copy

terraform workspace select $ENV || terraform workspace new $ENV
terraform apply -parallelism=$PARALLELISM -auto-approve -refresh=true -var="version_stage=$version"