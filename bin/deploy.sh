#!/bin/bash
# Usage : ./deploy.sh [profile] [workspace=dev]

fail() {
    printf '%s\n' "$1" >&2
    exit "${2-1}"
}

# --- set variables ---

LAMBDAS=$(cat ./lambdas)
SAP_API_DEPLOY=false
POSITIONAL_ARGS=()

while [[ $# -gt 0 ]]; do
  case $1 in
    --sap_api)
      SAP_API_DEPLOY=true
      shift # past argument
      ;;
    *)
      POSITIONAL_ARGS+=("$1") # save positional arg
      shift # past argument
      ;;
  esac
done

set -- "${POSITIONAL_ARGS[@]}" # restore positional parameters

PROFILE=$1
ENV=$(echo "$2" | tr '[:upper:]' '[:lower:]')
REGION="eu-west-1"

echo "PROFILE=$PROFILE, ENV=$ENV, SAP_API_DEPLOY: $SAP_API_DEPLOY"
export PYTHONPATH="${PYTHONPATH}:${PWD}/utils"

# --- END set variables ---


if [ -n "${PROFILE}" ]; then
    export AWS_PROFILE=$PROFILE
fi

# Create base file bucket if not exist
S3_BUCKET="my-resa-api-$ENV"
if aws s3api head-bucket --bucket "$S3_BUCKET" 2>/dev/null; then
  echo bucket "$S3_BUCKET" already exist
else
  aws s3api create-bucket --bucket "$S3_BUCKET" --region $REGION --create-bucket-configuration LocationConstraint=$REGION
  echo bucket "$S3_BUCKET" created
fi

./bin/updateResources.sh $ENV || fail "resource upload failed"
./bin/deploy-lambdas.py $ENV || fail "lambda code upload failed"
./bin/terraform-only.sh "$PROFILE" "$ENV" || fail "terraform deployment failed"

if [ $SAP_API_DEPLOY = true ]; then
  echo "Deploy SAP API Proxy"
  version=$(./bin/get_version.sh "$ENV")
  ./bin/deploySapApiProxy.py $ENV $version || fail "cannot deploy SAP API Proxy"
  if [ $ENV == "qla" ] || [ $ENV == "production" ]; then
    ./bin/updateSapApiProxyAlias.py $ENV latest $version || fail "cannot update SAP API Proxy latest"
  fi
fi

./bin/gen_PostmanApi.py || fail  "Postman upload failed"