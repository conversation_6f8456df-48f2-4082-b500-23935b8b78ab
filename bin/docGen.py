#!/usr/bin/env python3
import argparse
import json
import os
import subprocess


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--sandbox",
        default=False,
        action="store_true",
        help="add sandbox environnements to the doc",
    )
    parser.add_argument("--only", help="generate doc only for these endpoints")
    parser.add_argument("-e", "--env", required=True, help="get name for current env")
    args = parser.parse_args()
    if args.sandbox:
        os.environ["DEV"] = "true"

    commande = ["./bin/genOpenapi.py"]
    if args.env:
        commande += ["--env", args.env]
    if args.only:
        commande += ["--only", args.only]
    my_env = os.environ.copy()
    result = subprocess.run(commande, check=True, env=my_env, capture_output=True, text=True)
    print("Sortie standard:", result.stdout)
    print("Sortie d'erreur:", result.stderr)

    f = open("resources/myresaapi_openapi_doc.json", "r")
    openapi = f.read()
    f.close()
    out = generate_openapi_html_documentation(openapi)

    f = open("documentation.html", "w+")
    f.write(out)
    f.close()

    f = open("resources/documentation.html", "w+")
    f.write(out)
    f.close()


def get(dict_, key, default=None, err=None):
    """get item from dict if exist"""
    if err and (key not in dict_):
        raise err
    return (dict_[key] or default) if key in dict_ else default


def generate_openapi_html_documentation(openapi):
    openapi = json.loads(openapi)
    for path in openapi["paths"]:
        for method in openapi["paths"][path]:
            endpoint = openapi["paths"][path][method]
            security = (get(endpoint, "security", [])[0:1] or [{}])[0].keys()
            parameters = get(openapi["paths"][path][method], "parameters", [])
            if "tokenAuthorizer" in security:
                parameters.insert(
                    0,
                    {
                        "name": "Authorization",
                        "in": "header",
                        "description": "Bearer token to log in as authenticated user  (This field doesn't work on the `try it out`, use the lock instead)",
                        "required": False,
                        "schema": {"type": "string", "format": "^Bearer <id_token>$"},
                    },
                )
            if "ghostAuthorizer" in security:
                parameters.insert(
                    0,
                    {
                        "name": "SessionId",
                        "in": "header",
                        "description": "SessionId to log in as ghost user",
                        "required": False,
                        "schema": {
                            "type": "string",
                        },
                    },
                )
            openapi["paths"][path][method]["parameters"] = parameters

    openapi = json.dumps(openapi)

    f = open("resources/templates/swagger_template.html", "r")
    template = f.read()
    f.close()

    out = template.replace('"#openapi#"', openapi)
    return out


if __name__ == "__main__":
    main()
