#!/usr/bin/env python3
import json

import requests

from utils.aws_utils import get_secret


class PostmanCollectionManager:
    """
    This class manages Postman collections, allowing for creating and updating collections and requests within a specified workspace.
    """

    def __init__(self, api_key: str, workspace_id: str):
        """
        Initialize the PostmanCollectionManager with API key and workspace ID.
        """
        self.api_key = api_key
        self.workspace_id = workspace_id
        self.headers = {
            "X-Api-Key": api_key,
            "Content-Type": "application/json",
        }
        self.collection_id = None

    def create_collection(self, openapi_file_path: str):
        """
        Create a Postman collection from an OpenAPI file.
        """
        url = f"https://api.getpostman.com/import/openapi?workspace={self.workspace_id}"
        with open(openapi_file_path, "r") as file:
            openapi_data = json.load(file)
            openapi_data["paths"] = dict(sorted(openapi_data["paths"].items()))

        collection_data = {
            "type": "json",
            "input": openapi_data,
            "options": {
                "collapseFolders": False,
                "folderStrategy": "Paths",
                "parametersResolution": "Example",
            },
        }

        response = requests.post(url, json=collection_data, headers=self.headers)
        if response.status_code == 200:
            print("Collection successfully created")
            collection_data = response.json()
            print(f"Name of collection : {collection_data['collections'][0]['name']}")
            self.collection_id = collection_data["collections"][0]["id"]
        else:
            raise Exception(f"Error creating collection: {response.status_code}\n{response.text}")

    def update_collection_with_pre_request_script(self, pre_request_script: str):
        """
        Update the collection with a pre-request script.
        """
        url = f"https://api.getpostman.com/collections/{self.collection_id}"
        update_data = {
            "collection": {
                "events": [
                    {
                        "listen": "prerequest",
                        "script": {
                            "type": "text/javascript",
                            "exec": pre_request_script.split("\n"),
                            "id": "set_api_key",
                        },
                    }
                ]
            }
        }

        response = requests.patch(url, json=update_data, headers=self.headers)
        if response.status_code != 200:
            raise Exception(f"Error updating collection: {response.status_code}\n{response.text}")
        return response.json()

    def update_request_with_script(self, script: str, request_id: str):
        """
        Update a specific request within the collection with a given script.
        """
        url = f"https://api.getpostman.com/collections/{self.collection_id}/requests/{request_id}"
        update_data = {
            "events": [
                {
                    "listen": "test",
                    "script": {
                        "type": "text/javascript",
                        "exec": script.split("\n"),
                        "id": f"auth_{request_id}",
                    },
                }
            ]
        }

        response = requests.put(url, json=update_data, headers=self.headers)
        if response.status_code != 200:
            raise Exception(f"Error updating request: {response.status_code}\n{response.text}")
        else:
            print(f"Script added to : {response.json()['data']['name']}")

    def get_id_with_name(self, path_name: str):
        """
        Get the ID of an item in the collection by its path name.
        """
        url = f"https://api.getpostman.com/collections/{self.collection_id}"
        response = requests.get(url, headers=self.headers)
        response_data = self.find_item_by_path(response.json()["collection"]["item"], path_name)
        return response_data.get("id", None) if response_data else None

    def find_item_by_path(self, item_list: list, path: str):
        """
        Find an item by its path in the collection's item list.
        Note: The path is based on the documentation. In the documentation, the summary must contain:
        WSXXX : Description of the WS (number of Xs not limited, but with spaces two dots at the end).
        """
        if not path or not item_list:
            return None
        path_parts = path.split("/")
        for item in item_list:
            if item["name"].startswith(path_parts[0]):
                if len(path_parts) == 1:
                    return item
                else:
                    return self.find_item_by_path(item.get("item", []), "/".join(path_parts[1:]))
        return None

    def get_all_collection(self, workspaceId: str):
        """
        Get the ID of an item in the collection by its path name.
        """
        url = f"https://api.getpostman.com/collections?workspace={workspaceId}"
        response = requests.get(url, headers=self.headers)
        return response.json()

    def delete_oldest_duplicate_collections(self, collections):
        collection_prefixes = {}

        for collection in collections:
            name_parts = collection["name"].split()[:2]
            if len(name_parts) < 2:
                continue
            prefix = " ".join(name_parts)

            if prefix not in collection_prefixes:
                collection_prefixes[prefix] = []
            collection_prefixes[prefix].append(collection)

        for prefix, collections in collection_prefixes.items():
            if len(collections) > 1:
                oldest_collection = min(collections, key=lambda x: x["updatedAt"])
                self.delete_collection(oldest_collection["id"])

    def delete_collection(self, collection_id: str):
        url = f"https://api.getpostman.com/collections/{collection_id}"
        response = requests.delete(url, headers=self.headers)
        if response.status_code == 200:
            print(f"Collection {collection_id} successfully deleted")
        else:
            raise Exception(f"Error deleting collection {collection_id}: {response.status_code}\n{response.text}")


if __name__ == "__main__":
    # Global variable setup
    api_key = get_secret("MyResaAPI/PostMan")["ApiKey"]
    workspace_id = "6987128d-56aa-4c75-b3fd-e6a4120d51dc"
    openapi_file_path = "./resources/myresaapi_openapi_doc.json"

    # /////////////Script zone\\\\\\\\\\\\\\
    script_authenticate = """
pm.test("Response status code is 200", function () {
    pm.response.to.have.status(200);
});

// Save the IdToken as a global variable for reuse
var idToken = pm.response.json().IdToken;
pm.globals.set("bearerToken", idToken);
""".strip()

    pre_request_script = """
let apiKey = pm.environment.get("apiKey");
pm.request.headers.add({ key: 'X-Api-Key', value: apiKey });
""".strip()
    # /////////////end of script zone\\\\\\\\\\\\\\

    # Create and manage Postman collections
    manager = PostmanCollectionManager(api_key, workspace_id)
    manager.create_collection(openapi_file_path)

    dict_endpoint_script = {"utilisateurs/authenticate/WS16 :": script_authenticate}

    if manager.collection_id:
        update_result = manager.update_collection_with_pre_request_script(pre_request_script)
        for path, script in dict_endpoint_script.items():
            id_endpoint = manager.get_id_with_name(path)
            if id_endpoint:
                manager.update_request_with_script(script, id_endpoint)
                manager.delete_oldest_duplicate_collections(manager.get_all_collection(workspace_id)["collections"])
            else:
                raise Exception(f"Endpoint not found for path: {path}")
