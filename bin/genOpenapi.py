#!/usr/bin/env python3
import argparse
import json
import os
import re
import subprocess

import yaml


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("-o", "--only", nargs="+", help="generate doc only for these endpoints")
    parser.add_argument("-e", "--env", required=True, help="get name for current env")
    args = parser.parse_args()
    only = args.only
    environment = args.env.upper()
    print(f"{environment=}\n{only=}")

    version = subprocess.run(["./bin/get_version.sh", environment], stdout=subprocess.PIPE).stdout.decode("utf-8").replace("\n", "")

    openapi = gen_file(version, only, environment)
    with open("resources/myresaapi_openapi_doc.json", "w+", encoding="utf8") as f:
        f.write(json.dumps(openapi, indent=2, separators=(",", ": "), ensure_ascii=False))

    with open("resources/myresaapi_openapi_doc.yaml", "w+", encoding="utf8") as f:
        f.write(yaml.safe_dump(openapi, sort_keys=False))


unauthorized_response = {
    "description": "Unauthorized error response",
    "content": {
        "application/json": {
            "schema": {
                "type": "object",
                "properties": {
                    "Error": {"type": "integer", "example": 401},
                    "Message": {"type": "string", "example": "Unauthorized"},
                },
            }
        }
    },
}
invalid_token_response = {
    "description": "Invalid token error response",
    "content": {
        "application/json": {
            "schema": {
                "type": "object",
                "properties": {
                    "Error": {"type": "integer", "example": 401},
                    "Message": {"type": "string", "example": "Token de connexion invalide"},
                    "ErrorCode": {"type": "string", "example": "INVALID_TOKEN"},
                },
            }
        }
    },
}
forbidden_response = {
    "description": "Forbidden error response",
    "content": {
        "application/json": {
            "schema": {
                "type": "object",
                "properties": {
                    "Error": {"type": "integer", "example": 403},
                    "Message": {"type": "string", "example": "User is not authorized to access this resource with an explicit deny"},
                },
            }
        }
    },
}


def add_auth_errors(path: dict) -> dict:
    """
    Add authentication-related error responses to the given API path.

    Args:
       path (dict): A dictionary representing the API path, where keys are HTTP methods
           and values are endpoint configurations.

    Returns:
       dict: The updated API path with authentication-related error responses added.
    """
    for method, endpoint in path.items():
        if "security" in endpoint:
            responses = {}
            for security in endpoint["security"]:
                if "tokenAuthorizer" in security:
                    responses.update(
                        {
                            "401 Unauthorized": unauthorized_response,
                            "403 Forbidden": forbidden_response,
                            "401 INVALID_TOKEN": invalid_token_response,
                        }
                    )

            endpoint["responses"] = {**endpoint.get("responses", {}), **responses}

    return path


def gen_file(version, only, environment):
    # Getting the current work directory (cwd)

    thisdir = os.getcwd()
    ext = (".json", ".yaml")

    paths = {}
    for r, d, f in os.walk(os.path.join(thisdir, "resources", "openapi", "paths")):
        for file in f:
            if file.endswith(ext):
                endpoint = "/" + file[: file.rfind(".")].replace(".", "/")
                if not only or any(re.match(pattern, endpoint) for pattern in only):
                    with open(os.path.join(r, file), "r") as f:
                        if file.endswith(".json"):
                            path_content = json.loads(f.read())
                        elif file.endswith(".yaml"):
                            path_content = yaml.safe_load(f.read())
                    paths[endpoint] = add_auth_errors(path_content)

    components = {}
    for r, d, f in os.walk(os.path.join(thisdir, "resources", "openapi", "components")):
        for file in f:
            if file.endswith(ext):
                component = file[: file.rfind(".")].replace(".", "/")
                with open(os.path.join(r, file), "r") as f:
                    if file.endswith(".json"):
                        component_content = json.loads(f.read())
                    elif file.endswith(".yaml"):
                        component_content = yaml.safe_load(f.read())
                components[component] = component_content

    envs = {
        "qta/acceptance": "https://api-acceptance.resa.be/latest",
        "qla": "https://api-qla.resa.be/latest",
        "production": "https://api.resa.be/latest",
    }
    dev_envs = {
        "development": "https://pqx8lp3l98.execute-api.eu-west-1.amazonaws.com/v0",
        "antonin": "https://slk9gfimga.execute-api.eu-west-1.amazonaws.com/v0",
    }
    if os.environ.get("DEV", None):
        envs.update(dev_envs)
    openapi = {
        "openapi": "3.1.0",
        "info": {
            "title": f"MyResaAPI {environment} {version} ",
            "description": "API donnant accès aux data assets de Resa pour l'application MyResa",
            "version": str(version),
        },
        "servers": [{"description": desc, "url": url} for (desc, url) in envs.items()],
        "paths": paths,
        "components": {
            "securitySchemes": {
                "tokenAuthorizer": {
                    "description": "User need to be activated",
                    "type": "http",
                    "scheme": "bearer",
                    "x-amazon-apigateway-authtype": "custom",
                },
                "ghostAuthorizer": {
                    "description": "User account is not activated",
                    "type": "apiKey",
                    "in": "header",
                    "name": "SessionId",
                },
                "basicAuthorizer": {"type": "http", "scheme": "basic"},
                "apiKeyAuthorizer": {
                    "type": "apiKey",
                    "in": "header",
                    "name": "X-API-Key",
                },
            },
            "schemas": components,
        },
        "security": [{"apiKeyAuthorizer": []}],
    }
    return openapi


if __name__ == "__main__":
    main()
