import ldap3

from utils.aws_utils import get_secret

cred = get_secret("MyResaAPI/ActiveDirectory/qta")
host = "localhost"
port = 9636
login = cred["login"]
password = cred["password"]
folder_dn = "OU=TestAccounts,OU=ORG,DC=resaclients,DC=intra"  # UsersAccounts #QlaAccounts #TestAccounts
print(folder_dn)

url = "ldaps://" + host + ":" + str(port)
server = ldap3.Server(url, get_info=all, use_ssl=True)
conn = ldap3.Connection(server, user=login, password=password, auto_bind=True)

entry_generator = conn.extend.standard.paged_search(
    search_base=folder_dn,
    search_filter="(CN=Tes.User*)",
    search_scope=ldap3.SUBTREE,
    attributes="*",
    paged_size=1000,
    generator=True,
)
entries = []
for user in entry_generator:
    # print(user['raw_dn'])
    # conn.delete(user_dn)
    entries = entries + [user]

count = len(entries)
print("tests : ", count)
if count > 0:
    print("-----------------------------------------")
    for test in entries:
        user_dn = test["raw_dn"].decode("utf-8")
        print(user_dn)
        # conn.delete(user_dn)
    print("users deleted")
