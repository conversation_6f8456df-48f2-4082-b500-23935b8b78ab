#!/bin/bash
# update lambda function's code

if [ -z "$2" ]; then
    echo "Usage : ./updateLambda.sh folder env"
    exit 1
fi

fail() {
    printf '%s\n' "$1" >&2
    exit "${2-1}"
}

ENV=$(echo "$2" | tr '[:upper:]' '[:lower:]')
LAMBDA_NAME=$(basename "$1")
if [[ $ENV =~ "qta_".* ]]; then
  ENV="qta"
fi
    

if [ -f ./$1/LambdaECR.Dockerfile ]; then
  VERSION=$(./bin/get_version.sh "$ENV")

  ACCOUNT_ID=$(aws sts get-caller-identity | jq -r '.Account')
  ACCOUNT_REGION=$(aws configure get region)
  ECR_IMAGE_NAME=$(sed --expression 's/\([A-Z]\)/-\L\1/g' --expression 's/^-//' <<< "my_resa_api/$LAMBDA_NAME/$ENV")
  REGISTRY_ID="$ACCOUNT_ID.dkr.ecr.$ACCOUNT_REGION.amazonaws.com/$ECR_IMAGE_NAME:$VERSION"
  
  if aws ecr describe-repositories --repository-names "$ECR_IMAGE_NAME" &> /dev/null; then
    echo ECR repository "$ECR_IMAGE_NAME" already exist
  else
    aws ecr create-repository --repository-name "$ECR_IMAGE_NAME" --image-scanning-configuration scanOnPush=true --image-tag-mutability MUTABLE
    echo ECR repository "$ECR_IMAGE_NAME" created
  fi

  docker build --platform linux/x86_64 --network host -t "$REGISTRY_ID" -f "./$LAMBDA_NAME/LambdaECR.Dockerfile" .
  aws ecr get-login-password --region $ACCOUNT_REGION | docker login --username AWS --password-stdin "$REGISTRY_ID"
  docker push "$REGISTRY_ID"
else
  BUCKET_DEPLOYMENT_PACKAGE="my-resa-api-$ENV"
  ./bin/build.sh "$1" || fail "building error"
  aws s3 cp tmp/"$1"/bundle.zip s3://"$BUCKET_DEPLOYMENT_PACKAGE"/lambdas/"$LAMBDA_NAME".zip || fail "s3 upload error"
fi
