{"get": {"summary": "WS96 : <PERSON><PERSON><PERSON><PERSON> le résultat d'une requête asynchrone", "parameters": [{"name": "Id", "in": "path", "required": true, "schema": {"type": "string"}, "example": "x8FuCHFmjkO748Up8z8InvW7PCKr2UdtWqiCa3aGmPJSqrkGuE"}], "responses": {"200": {"description": "200", "content": {"application/json": {"schema": {"type": "object", "required": ["Status", "StartAt"], "properties": {"Status": {"type": "string", "description": "Status about the process, not the request", "enum": ["IN_PROGRESS", "FAILED", "SUCCEED"]}, "StartAt": {"type": "number", "format": "timestamp"}, "FinishAt": {"type": "number", "format": "timestamp"}, "StatusCode": {"type": "integer"}, "Headers": {"type": "object"}, "Content": {"type": "string"}, "ErrorMessage": {"type": "string"}}}, "example": {"Status": "SUCCESS", "StartAt": 1600072059199, "FinishAt": 1600072159199, "StatusCode": 200, "Headers": {"Content-Type": "application/json"}, "Content": "{\"key\": \"value\"}"}}}}}}}