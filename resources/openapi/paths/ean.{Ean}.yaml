get:
  summary: "WS29 : Obtenir de l'information détaillée liée à un Ean"
  description: "Obtenir de l'information détaillée liée à un Ean. Certains attributs
    ne seront présents que en fonction du tarif et du type d'énérgie.  \nPeut être
    utilisé de façon non authentifiée et ne retourne qu'un subset de données ('SectActivite',
    'PContPrel', 'ReglPrel', 'NbPhases', 'UCodeLibelle', 'Compteurs', 'Compteurs.CptControle',
    'Compteurs.NumCpt', 'Compteurs.NRegistres', 'Compteurs.ReglPrel', 'GrpTarif', 'GrpTarifLibelle', 'ReglInje')"
  security:
    - tokenAuthorizer: [ ]
  parameters:
    - name: Ean
      in: path
      required: true
      schema:
        type: string
      example: '541460900002617502'
    - name: NumCpt
      in: query
      required: true
      schema:
        type: string
      example: '000000000005415843'
  responses:
    '200':
      description: 200 success
      headers:
        Access-Control-Allow-Origin:
          schema:
            type: string
      content:
        application/json:
          schema:
            type: object
            properties:
              StatutEan:
                type: string
              SectActivite:
                type: string
              Contrat:
                type: integer
              Langue:
                type: string
              Mi:
                type: integer
              Mo:
                type: integer
              PartenaireId:
                type: integer
              Bp:
                type: string
              BpHgz:
                type:
                  - integer
                  - 'null'
              CcHgz:
                type:
                  - integer
                  - 'null'
              BpAddress:
                type: string
              BpLocalite:
                type: string
              BpCdpostal:
                type: integer
              OrAddress:
                type: string
              OrLocalite:
                type: string
              OrCdpostal:
                type: integer
              OrNumCmpt:
                type: string
              CodeCommune:
                type: string
              Nace:
                type: integer
              Frns:
                type: string
              TypInstln:
                type: string
              Slp:
                type: string
              Frequence:
                type: string
              Usage:
                type: string
              CodeTarif:
                type: integer
              Ucode:
                type: string
              UCodeLibelle:
                type: string
              NbPhases:
                type: string
                enum:
                  - '2'
                  - '1N'
                  - '3'
                  - '3N'
              NbPhasesLibelle:
                description: Codification du champ NbPhases. null si NbPhases inconnu.
                type:
                  - 'string'
                  - 'null'
                enum:
                  - 'MONO'
                  - 'MONO_N'
                  - 'TRI'
                  - 'TRI_N'
                  - null
              ComptActi1Re:
                type: string
              ComptActi2Re:
                type: string
              ComptActi2PlusRe:
                type: string
              TotalComptAct:
                type: string
              ComptInac1Re:
                type: string
              ComptInac2Re:
                type: string
              ComptInac2PlusRe:
                type: string
              TotalComptInac:
                type: string
              Compteurs:
                type: array
                items:
                  type: object
                  required:
                    - NumCpt
                    - CptCab
                    - CptControle
                    - CptSmart
                    - CptPrepaiement
                    - NbRegistres
                  properties:
                    NumCpt:
                      type: string
                    CptCab:
                      type: boolean
                    CptControle:
                      type: boolean
                    CptSmart:
                      type: boolean
                    CptPrepaiement:
                      type: boolean
                    NbRegistres:
                      type: integer
                    FctCompt:
                      type: string
                    ReglPrel:
                      type: integer
                    ReglInje:
                      type: integer
          example:
            StatutEan: ACTIF
            SectActivite: '02'
            Contrat: 2003599892
            Langue: F
            Mi: 20180101
            Mo: 99991231
            PartenaireId: 2103315908
            Bp: Ecoles Libres de Robermont E270580
            BpHgz:
            CcHgz:
            BpAddress: Rue Des Fortifications 10 B25
            BpLocalite: Liège
            BpCdpostal: 4030
            OrAddress: Rue des Fortifications 24
            OrLocalite: Grivegnée
            OrCdpostal: 4030
            OrNumCmpt: 1 Etage 0/0
            OrIdRadRue: '152663'
            OrIdRadLocalite: '112527'
            CodeCommune: LIEGE
            Nace: 85204
            Frns: LUMINUS_G
            TypInstln: PREL
            Slp: S32
            Frequence: YMMR
            Usage: NR
            CodeTarif: 0
            Ucode: U200
            UCodeLibelle: Simple tarif
            VanneExte: Oui
            ComptCali: G16
            PressionCompt: '0.02'
            EnergieTh: '161158.74'
            EnergieTotAct: '161158.74'
            TotalComptAct: '1'
            ComptActi1Re: '1'
            ComptInac1Re: '0'
            TotalComptInac: '0'
            Compteurs:
              - NumCpt: '000000000005415843'
                CptCab: false
                CptControle: false
                CptSmart: false
                CptPrepaiement: false
                NRegistres: 1
    '500':
      description: 500 Internal Server Error
      headers:
        Access-Control-Allow-Origin:
          schema:
            type: string
      content:
        application/json:
          schema:
            "$ref": "#/components/schemas/MyResaAPI_Error"
          example:
            Error: 500
            Message: Aucunes données trouvées pour la sélection !
