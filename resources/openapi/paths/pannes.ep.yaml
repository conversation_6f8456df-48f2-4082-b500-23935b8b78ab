post:
  summary: 'WS23 : Déclarer une panne'
  parameters:
    - name: Langue
      in: header
      required: false
      schema:
        type: string
        example: FR
        default: FR
  requestBody:
    description: Données concernant la panne à déclarer
    required: true
    content:
      application/json:
        schema:
          type: object
          required:
            - Commune
            - City
            - Cp
            - Rue
            - Nom
            - Email
            - Numero
          properties:
            Commune:
              type: string
              example: Herstal
            Cp:
              type: string
              example: 4040
            City:
              type: string
              example: L-738-126
            Rue:
              type: string
              example: Rue bizette
            Ztype:
              type: string
              example: N
            Nom:
              type: string
              example: Mister test
            Email:
              type: string
              example: <EMAIL>
            Tel:
              type: string
              example: '0494253695'
            Numero:
              type: string
              example: '274'
            EquiId:
              type: string
              example: '03800'
            Commentaire:
              type: string
              example: Ceci est un test de declaration de panne
              maxLength: 1024
            IdRadRue:
              type: string
            IdRadLocalite:
              type: string
            IdRad:
              type: string
            TypePanne:
              type: string
              description: |
                | Type de panne | Valeur |
                |-|-|
                | Luminaire fonctionne de jour | 01 |
                | Luminaire en panne (plusieurs) | 02 |
                | Luminaire en panne | 03 |
                | Luminaire clignote | 04 |
                | Dégâts au luminaire | 05 |
              example: "01"
          example:
            Commune: "Herstal"
            City: "L-738-126"
            Rue: "Rue bizette"
            Ztype: "N"
            Nom: "Mister test"
            Email: "<EMAIL>"
            Tel: "0494253695"
            Numero: "274"
            EquiId: "03800"
            Commentaire: "Ceci est un test de declaration de panne"
            TypePanne: "01"
  responses:
    '200':
      description: 200 OK
      content:
        application/json:
          example:
            NumDossier: '000411066355'
            Date: '00000000'
          schema:
            type: object
            required:
              - NumDossier
              - Date
            properties:
              NumDossier:
                type: string
              Date:
                type: string
