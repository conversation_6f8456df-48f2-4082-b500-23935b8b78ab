get:
  summary: "WS130 : Obtenir la liste des EAN liés à la commune de l'utilisateur connecté."
  description: "L'utilisateur connecté doit soit avoir le rôle 'INDEX_CPT_CONSU' ou 'INDEX_CPT_GERER', ou alors être administrateur de la commune."
  security:
    - tokenAuthorizer: [ ]
  parameters:
    - name: Smart
      description: Si présent, retourne uniquement les smart si 'true' et uniquement les non-smart si 'false'
      in: query
      required: false
      schema:
        type: string
        enum:
          - 'true'
          - 'false'
        default: null
  responses:
    '200':
      description: 200 success
      content:
        application/json:
          schema:
            type: array
            items:
              type: object
              properties:
                Adresse:
                  type: object
                  properties:
                    Rue:
                      type: string
                      example: "Rue Jonfosse"
                    NumRue:
                      type: string
                      example: "80"
                    CodePostal:
                      type: string
                      example: "4000"
                    Localite:
                      type: string
                      example: "Liège"
                Eans:
                  type: object
                  properties:
                    Ean:
                      type: string
                      example: "541449012700000870"
                    Type:
                      type: string
                      example: "Elec"
                    NumCpt:
                      type: string
                      example: "10375117"
                    Smart:
                      type: boolean
                      example: false
                    Index:
                      type: array
                      items:
                        type: object
                        properties:
                          Date:
                            type: string
                            example: "2022-10-18"
                          Index:
                            type: number
                            example: 30210
                          Unite:
                            type: string
                            example: "KWH"
                          NumCpt:
                            type: string
                            example: "10375117"
                          Cadran:
                            type: string
                            example: "BAS"