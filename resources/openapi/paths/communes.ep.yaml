get:
  summary: "WS140 : Obtenir la liste des consommations EP liées au compte commune, ainsi que le nombre de led/non-led."
  description: L'utilisateur connecté doit soit avoir le rôle 'CONSO_EP', ou alors être administrateur de la commune.
  security:
    - tokenAuthorizer: [ ]
  responses:
    '200':
      description: 200 success
      content:
        application/json:
          schema:
            type: object
            properties:
              NbrEpLed:
                type: number
                example: 2027
              NbrEpNonLed:
                type: number
                example: 15036
              ConsommationAnnee:
                type: number
                example: 30208548.49
              Historique:
                type: array
                items:
                  type: object
                  properties:
                    Consommation:
                      type: number
                      example: 239392.77
                    Date:
                      type: string
                      example: "2020-11-01"