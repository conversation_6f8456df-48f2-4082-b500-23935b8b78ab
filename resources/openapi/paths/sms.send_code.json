{"post": {"summary": "Envoyer code validation", "parameters": [{"name": "Sandbox", "in": "query", "required": true, "description": "Desactivate sandbox mode if `false`", "schema": {"type": "boolean", "example": "true"}}, {"name": "<PERSON><PERSON>", "in": "header", "required": true, "description": "Langue pour le message", "schema": {"type": "string", "enum": ["fr", "nl", "end"], "example": "fr"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["Mobile"], "properties": {"Mobile": {"type": "string"}}}, "example": {"Mobile": "+32 494 18 48 78"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "required": [], "properties": {}}, "example": {}}}}}}}