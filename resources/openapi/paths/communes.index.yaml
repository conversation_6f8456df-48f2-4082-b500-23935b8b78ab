get:
  summary: "WS147 : Téléchargement du template Excel à remplir pour envoyer les derniers index."
  security:
    - tokenAuthorizer: [ ]
  parameters:
    - name: FullEan
      in: query
      description: "Si défini à True, renvoie tous les EANs. Si défini à False ou non défini, renvoie uniquement les EANs qui ont besoin d'être relevés."
      required: false
      default: false
      type: boolean
    - name: change_four
      in: query
      description: "Si défini à True, mettras X dans le flag changeFour lors de l'envoie des data vers SAP"
      required: false
      default: false
      type: boolean
  responses:
    '200':
      description: Template Excel à remplir avec les nouveaux index.
      content:
        application/vnd.openxmlformats-officedocument.spreadsheetml.sheet:
          example: "
            E<PERSON>;Compteur;Energie;Cadran;Date;IndexPrec;NouvIndex \n
            1234567890001;16328213;Gaz;TOTAL;2022-11-21;4436; \n
            1234567890002;9995;<PERSON>az;TOTAL;2023-06-26;2783363; \n
            1234567890003;9278888;Gaz;TOTAL;2022-11-30;99480; \n
            1234567890004;4159997;Gaz;TOTAL;2022-11-07;56786; \n
            1234567890005;5752993;Elec;TOTAL;2022-11-07;25288;
            "
post:
  summary: "WS148 : Relevé d'index via Excel + envoie d'un mail avec le statut de chaque index."
  security:
    - tokenAuthorizer: [ ]
  requestBody:
    required: true
    content:
      application/vnd.openxmlformats-officedocument.spreadsheetml.sheet:
        schema:
          type: string
          format: binary
          description: Le contenu du fichier Excel à vérifier.
  responses:
    '204':
      description: La vérification a été initiée avec succès.
    '400 wrongHeadersError':
      description: "Les en-têtes ne correspondent pas."
      content:
        application/json:
          schema:
            type: object
            properties:
              errorCode:
                type: string
                example: "WRONG_HEADERS"
              errorMessage:
                type: string
                example: "Les en-têtes ne correspondent pas."
    '400 wrongNumberColError':
      description: "Nombre de colonnes incorrect."
      content:
        application/json:
          schema:
            type: object
            properties:
              errorCode:
                type: string
                example: "WRONG_NUMBER_COL"
              errorMessage:
                type: string
                example: "Nombre de colonnes incorrect."
    '400 invalidFileTypeError':
      description: "Fichier Excel invalide."
      content:
        application/json:
          schema:
            type: object
            properties:
              errorCode:
                type: string
                example: "INVALID_FILE_TYPE"
              errorMessage:
                type: string
                example: "Fichier Excel invalide."
    '400 processingError':
      description: "Échec du traitement du fichier."
      content:
        application/json:
          schema:
            type: object
            properties:
              errorCode:
                type: string
                example: "PROCESSING_ERROR"
              errorMessage:
                type: string
                example: "Échec du traitement du fichier."
    '400 wrongTypeError':
      description: "Type de fichier incorrect."
      content:
        application/json:
          schema:
            type: object
            properties:
              errorCode:
                type: string
                example: "WRONG_TYPE"
              errorMessage:
                type: string
                example: "Type de fichier incorrect."
