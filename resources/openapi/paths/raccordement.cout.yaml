get:
  summary: 'WS172: Calculate the cost of a connection modification'
  description: Sends a GET request to WS172 to return the cost of modifying an existing connection, computed via a SQL query.
  parameters:
    - name: puissance_actuelle
      in: query
      required: true
      schema:
        type: number
        example: 11.1
      description: The current power of the connection.
    - name: puissance_demandee
      in: query
      required: true
      schema:
        type: number
        example: 13.9
      description: The desired power for the new connection.
    - name: phase_actuelle
      in: query
      required: true
      schema:
        type: number
        example: 4
      description: The current phase of the connection.
    - name: phase_demandee
      in: query
      required: true
      schema:
        type: number
        example: 4
      description: The desired phase for the new connection.
    - name: amperage_actuelle
      in: query
      required: true
      schema:
        type: number
        example: 16
      description: The current amperage of the connection.
    - name: amperage_demandee
      in: query
      required: true
      schema:
        type: number
        example: 20
      description: The desired amperage for the new connection.
  responses:
    '200':
      description: Operation successful
      headers:
        Access-Control-Allow-Origin:
          schema:
            type: string
      content:
        application/json:
          schema:
            type: object
            properties:
              Prix:
                type: number
                description: The cost of the modification.
                example: 883.8
              ForfaitsActuel:
                type: string
                description: The current rate you are on.
                example: Essentiel
              ForfaitsPropose:
                type: string
                description: The proposed rate.
                example: Confort
    '400':
      description: Bad Request
      content:
        application/json:
          schema:
            type: object
            properties:
              Error:
                type: integer
                description: The http status code of the error.
                example: 400
              Message:
                type: string
                description: Detailed error message.
                example: 'Les paramètres amperage_actuelle et amperage_demandee sont requis dans le corps de la requête.'
              ErrorCode:
                type: string
                description: A classified error code for more specific error type.
                example: 'MISSING_INFO'
    '500':
      description: Internal Server Error
      content:
        application/json:
          schema:
            type: object
            properties:
              Error:
                type: integer
                description: The http status code of the error.
                example: 500
              Message:
                type: string
                description: Detailed error message.
                example: 'No response received from API'
              ErrorCode:
                type: string
                description: A classified error code for more specific error type.
                example: 'API_ERROR'