post:
  security: 
    - tokenAuthorizer: []
  summary: "Submit a specified set of consents the user is granting or refusing."
  description: "Submit a specified set of consent the user is granting or refusing."
  requestBody:
    content:
      application/json:
        schema:
          type: array
          items: 
            $ref: '#/components/schemas/BusinessPartnerConsentRequest'
  responses:
    "200":
      description: "The specified consents have been taken into account"
      content:
        application/json:
          schema:
            type: array
            items: 
              $ref: '#/components/schemas/BusinessPartnerConsentResponse'
    "500":
      description: "Server error"
get:
  security: 
    - tokenAuthorizer: []
  summary: "Get all consents granter/refused by this Business Partner over time"
  description: "Get all consents granter/refused by this Business Partner over time"
  responses:
    "200":
      description: "The list of consents granter or refused by this Business Partner"
      content:
        application/json:
          schema:
            type: array
            items: 
              $ref: '#/components/schemas/BusinessPartnerConsentResponse'
    "500":
      description: "Server error"