post:
  summary: 'WS01: C<PERSON>er un compte d''utilisateur invité ou ghost'
  requestBody:
    content:
      application/json:
        schema:
          type: object
          properties:
            Firstname:
              type: string
            Lastname:
              type: string
            Email:
              type: string
              format: email
            ContactEmail:
              type: string
              format: email
            Phone:
              type: string
              pattern: ^[+]*[(]{0,1}[0-9]{1,4}[)]{0,1}[-\s\./0-9]*$
            PhoneFixe:
              type: string
              pattern: ^[+]*[(]{0,1}[0-9]{1,4}[)]{0,1}[-\s\./0-9]*$
            Adresse:
              $ref: '#/components/schemas/Adresse'
  responses:
    '200':
      description: 200 success
      headers:
        Access-Control-Allow-Origin:
          schema:
            type: string
      content:
        application/json:
          schema:
            title: Session utilisateur
            description: Session utilisateur suite à demande d'enregistrement
            type: object
            properties:
              SessionId:
                type: string
            required:
              - SessionId
          example:
            SessionId: ghost_785847665
    '409':
      description: 409 Conflict
      headers:
        Access-Control-Allow-Origin:
          schema:
            type: string
      content:
        application/json:
          example:
            error: 409
            message: This username is already taken
patch:
  summary: "WS97 : Permet de modifier les données d'un utilisateur"
  security:
    - tokenAuthorizer: [ ]
      ghostAuthorizer: [ ]
  requestBody:
    content:
      application/json:
        schema:
          type: object
          properties:
            Firstname:
              type: string
            Lastname:
              type: string
            Email:
              type: string
              format: email
              description: ghost only
            ContactEmail:
              type: string
              format: email
            Phone:
              type: string
              pattern: ^[+]*[(]{0,1}[0-9]{1,4}[)]{0,1}[-\s\./0-9]*$
            PhoneFixe:
              type: string
              pattern: ^[+]*[(]{0,1}[0-9]{1,4}[)]{0,1}[-\s\./0-9]*$
            Adresse:
              $ref: '#/components/schemas/Adresse'
            SmartPortalConsent:
              type:
                - boolean
                - null
              description: Flag de consentement pour l'utilisation et visualisation des données smart
              default: null
  responses:
    '200':
      description: Success
      content:
        application/json:
          schema:
            type: object
            required:
              - Uid
              - Firstname
              - Lastname
              - Email
              - Phone
              - PhoneFixe
              - Adresse
            properties:
              Uid:
                type:
                  - 'null'
                  - string
                nullable: true
              Firstname:
                type:
                  - 'null'
                  - string
              Lastname:
                type:
                  - 'null'
                  - string
              Email:
                type:
                  - 'null'
                  - string
              ContactEmail:
                type:
                  - 'null'
                  - string
              Phone:
                type:
                  - 'null'
                  - string
              PhoneFixe:
                type:
                  - 'null'
                  - string
              Adresse:
                oneOf:
                  - type: 'null'
                  - $ref: '#/components/schemas/Adresse'