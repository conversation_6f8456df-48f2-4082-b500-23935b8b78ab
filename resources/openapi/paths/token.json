{"post": {"summary": "WS64 : Associe l'information du token PPP à l'utilisateur", "security": [{"ghostAuthorizer": [], "tokenAuthorizer": []}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": [], "properties": {}}, "example": {}}}}, "parameters": [{"name": "<PERSON><PERSON>", "in": "header", "required": false, "schema": {"type": "string", "example": "FR", "default": "FR"}}, {"name": "Token", "in": "query", "description": "Token que l'utilisateur recoit par courrier", "required": true, "schema": {"type": "string", "example": "ABCDE123"}}], "responses": {"200": {"description": "200 success", "headers": {"Access-Control-Allow-Origin": {"schema": {"type": "string"}}}, "content": {"application/json": {"schema": {}, "example": [{"Master": "JEANNE", "Root": "DARC", "Ean": 541456700000963460, "Bp": 2103182072, "BpCodePays": "BE", "BpNom": "<PERSON><PERSON><PERSON><PERSON>", "BpPrenom": "<PERSON><PERSON>", "BpRue": "Rue de Pontisse", "BpNumRue": 97, "BpCdPostal": 4683, "BpLocalite": "<PERSON><PERSON><PERSON><PERSON>", "BpEmail": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com", "EmailExist": false, "BpTel": "003242642381", "IdRadLocalite": 113412, "IdRue": "000000016134", "IdRadRue": 148535, "IdAdresse": "14979597  "}]}}}}}}