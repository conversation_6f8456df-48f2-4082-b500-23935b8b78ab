get:
  summary: "WS120 : Retrouve les infos d'un EP sur base de son id"
  parameters:
    - name: Id
      in: path
      required: true
      schema:
        type: string
        example: "1100226"
    - name: IdCommune
      in: query
      required: false
      schema:
        type: string
        example: "11"
  responses:
    "200":
      description: 200 OK
      content:
        application/json:
          schema:
            type: object
            description: Liste des EAN trouvé
            properties:
              Id:
                type: string
                example: "1100226"
              SousType:
                type: string
                example: "137"
              Type:
                type: string
                example: B
              IlluminationType:
                type: string
                example: "005"
              Adresse:
                type: object
                properties:
                  Rue:
                    type: string
                    example: Rue Halleux
                  Zipcode:
                    type: integer
                    example: 4610
                  Ville:
                    type: string
                    example: Bellaire
                  Numero:
                    type: string
                    example: "10"
                  CityCode:
                    type: string
                    example: L-711-036
                  Lat:
                    type: number
                    example: 50.644020864
                  Long:
                    type: number
                    example: 5.664508866
              Panne:
                type: object
                properties:
                  Id:
                    type: string
                    example: "000411055021"
                  DateCreation:
                    type: string
                    example: 13/03/2018
                  DateCloture:
                    type: string
                    example: 31/07/2018
                  DernierStatut:
                    type: object
                    properties:
                      Id:
                        type: string
                        example: I0072
                      Descr:
                        type: string
                        example: ACLO
                      ShortDescr:
                        type: string
                        example: Avis clôturé
                      Date:
                        type: string
                        example: 31/07/2018
                      Heure:
                        type: string
                        example: 09:01:32
    "404":
      description: 404 NOT FOUND
      content:
        application/json:
          schema:
            type: object
            properties:
              Error:
                type: number
                example: 404
              Message:
                type: string
                example: "Aucun résultat retrouvé"
