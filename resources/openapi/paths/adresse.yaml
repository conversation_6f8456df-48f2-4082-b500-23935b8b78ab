get:
  summary: "WS17 : Obtenir les rues d'un code postal, dans la langue spécifiée"
  parameters:
    - name: Langue
      in: header
      required: false
      schema:
        type: string
        example: FR
        default: FR
    - name: Cdpostal
      in: query
      required: true
      schema:
        type: string
        example: "4040"
  responses:
    "200":
      description: "200 success"
      headers:
        Access-Control-Allow-Origin:
          schema:
            type: string
      content:
        application/json:
          schema:
            type: object
            required:
              - Liste
            properties:
              Liste:
                type: array
                items:
                  type: object
                  required:
                    - IdRue
                    - Rue
                    - IdLocalite
                    - Localite
                    - IdRadRue
                    - IdRadLocalite
                    - Ville
                  properties:
                    IdRue:
                      type: string
                    Rue:
                      type: string
                    IdLocalite:
                      type: string
                    Localite:
                      type: string
                    Ville:
                      type: string
                    IdRadRue:
                      type:
                        - string
                        - integer
                        - "null"
                    IdRadLocalite:
                      type:
                        - string
                        - integer
                        - "null"
          example:
            Liste:
              - IdRue: "000000008983"
                Rue: "Chemin de la Cave"
                IdLocalite: "000000000035"
                Localite: "HUY"
                IdRadRue: 121459
                IdRadLocalite: 112088
                Ville: "Ben-Ahin"
                City: "L-740-130"
              - IdRue: "000000009035"
                Rue: "Rue Cherave"
                IdLocalite: "000000000035"
                Localite: "HUY"
                IdRadRue: 137386
                IdRadLocalite: 112088
                Ville: "Ben-Ahin"
                City: "L-740-130"
              - IdRue: "000000009038"
                Rue: "Rue de la Motte"
                IdLocalite: "000000000038"
                Localite: "HUY"
                IdRadRue: 137386
                IdRadLocalite: 112088
                Ville: "Huy (Rive Gauche)"
                City: "L-740-130"
              - IdRue: "000000009038"
                Rue: "Rue de la Motte"
                IdLocalite: "000000000038"
                Localite: "HUY"
                IdRadRue: 137386
                IdRadLocalite: 112088
                Ville: "Huy (Rive Droite)"
                City: "L-740-230"
    
    "400":
      description: "400 missing parameter"
      headers:
        Access-Control-Allow-Origin:
          schema:
            type: string
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/MyResaAPI_Error"
          example:
            Error: 400
            Message: "Missing required request parameters: [Cdpostal]"
