{"get": {"summary": "WS58 : Obt<PERSON>r la liste des dossiers liés à un EAN, ou à un IdPartenaire", "security": [{"basicAuthorizer": []}], "parameters": [{"name": "IdPartenaire", "in": "query", "description": "Recherche par Id du Partenaire", "required": false, "schema": {"type": "integer", "example": "4100377282"}}, {"name": "<PERSON><PERSON>", "in": "query", "description": "Recherche par Ean", "required": false, "schema": {"type": "integer", "example": "541456700004289144"}}, {"name": "<PERSON><PERSON>", "in": "header", "required": false, "schema": {"type": "string", "example": "FR", "default": "FR"}}], "responses": {"200": {"description": "200 OK", "content": {"application/json": {"schema": {"type": "object", "required": ["Liste", "<PERSON><PERSON><PERSON>"], "properties": {"Liste": {"type": "array", "items": {"type": "object", "required": ["Dossier", "NumDossierSup", "IdPartenaire"], "properties": {"Dossier": {"type": "string"}, "NumDossierSup": {"type": "string"}, "IdPartenaire": {"type": "string"}}}}, "Adresse": {}}}, "example": {"Liste": [{"Dossier": "2006583     ", "NumDossierSup": "2008462     ", "IdPartenaire": "4100377282  "}, {"Dossier": "2008462     ", "NumDossierSup": "            ", "IdPartenaire": "4100377282  "}, {"Dossier": "2008464     ", "NumDossierSup": "2008462     ", "IdPartenaire": "4100377282  "}, {"Dossier": "644423      ", "NumDossierSup": "            ", "IdPartenaire": "4100025136  "}], "Adresse": ""}}}}}}, "post": {"summary": "WS58 : Obt<PERSON>r la liste des dossiers liés à un objet de raccordement", "parameters": [{"name": "<PERSON><PERSON>", "in": "header", "required": false, "schema": {"type": "string", "example": "FR", "default": "FR"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["CdPostal", "Rue", "Localite", "NumRue"], "properties": {"CdPostal": {"type": "string"}, "Rue": {"type": "string"}, "Localite": {"type": "string"}, "NumRue": {"type": "string"}}}, "example": {"CdPostal": "4630", "Rue": "ChaussÃ©e du <PERSON>", "Localite": "Soumagne", "NumRue": "3"}}}}, "responses": {"200": {"description": "200 OK", "content": {"application/json": {"schema": {"type": "object", "required": ["Liste", "<PERSON><PERSON><PERSON>"], "properties": {"Liste": {"type": "array", "items": {"type": "object", "required": ["Dossier", "NumDossierSup", "IdPartenaire"], "properties": {"Dossier": {"type": "string"}, "NumDossierSup": {"type": "string"}, "IdPartenaire": {"type": "string"}}}}, "Adresse": {}}}, "example": {"Liste": [{"Dossier": "2006583     ", "NumDossierSup": "2008462     ", "IdPartenaire": "4100377282  "}, {"Dossier": "2008462     ", "NumDossierSup": "            ", "IdPartenaire": "4100377282  "}, {"Dossier": "2008464     ", "NumDossierSup": "2008462     ", "IdPartenaire": "4100377282  "}, {"Dossier": "644423      ", "NumDossierSup": "            ", "IdPartenaire": "4100025136  "}], "Adresse": ""}}}}}}}