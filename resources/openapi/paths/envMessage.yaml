post:
  summary: "WS68 : Envoyer un Email et/ou un Sms à un utilisateur."
  description: |
    Un seul input définit l'ensemble d'utilisateurs qui recevront le message, à choisir parmi: EAN, EMAIL ou MOBILE_PHONE dans la partie Header.  
    La structure du message envoyé est flexible et permet de personaliser le template pour un utilisateur (voir variables dans le template).  
    Seul les champs Langue, TEMPLATE_ID dans Header, et l'un (ou plusieurs) des trois entre EAN, EMAIL et MOBILE_PHONE sont requis.

    L'envoi du message sera effectif en fonction de 
    (1) Les préférences `Comm` des utilisateurs. 
    (2 )La disponibilité de l'addresse e-mail et du numéro GSM, 
    (3) Le type de template (certains templates sont restreint à l'envoi e-mail). 
    Si le message est possible par plusieurs cannaux, mais qu'une erreur se produit sur un canal, le message sera envoyé
    pour le cannal fonctionnel et une erreur sera affichée pour le canal non fonctionnel.
  security:
    - basicAuthorizer: [ ]
  parameters:
    - name: Sandbox
      in: query
      required: true
      description: Le endpoint n'envoie des message que si Sandbox est false
      schema:
        type: boolean
        example: 'true'
  requestBody:
    content:
      application/json:
        schema:
          $ref: '#/components/schemas/PostEnvMessagePayload'
      application/json (from SAP):
        schema:
          $ref: '#/components/schemas/PostEnvMessagePayloadSAP'
  responses:
    "200":
      description: "The message sending request have been processed"
      content:
        application/json:
         schema:
           $ref: '#/components/schemas/PostEnvMessageResponse'
    "500":
      description: "Server error"
