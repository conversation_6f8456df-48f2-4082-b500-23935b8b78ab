{"get": {"summary": "Valdier un numéro de mobile via un code recu par SMS", "parameters": [{"name": "Mobile", "in": "query", "required": true, "description": "Mobile number", "schema": {"type": "string", "example": "+32494184867"}}, {"name": "Code", "in": "query", "required": true, "description": "The code used to validate the mobile", "schema": {"type": "string", "example": "2738239492"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "required": [], "properties": {}}, "example": {}}}}}}}