get:
  summary: "WS191 : Obtenir le status de blockage d'un EAN."
  parameters:
    - name: Ean
      in: path
      required: true
      schema:
        type: string
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              LockStatus:
                type: string
                enum: [ "LOCKED", "BLOCKED" ]
              Date:
                type: string
                format: date
    '404':
      description: EAN not found
      content:
        application/json:
          schema:
            type: object
            properties:
              Error:
                type: string
                enum: [ 404 ]
              Message:
                type: string
                enum: [ "EAN not found" ]
              ErrorCode:
                type: string
                enum: [ "EAN_NOT_FOUND" ]