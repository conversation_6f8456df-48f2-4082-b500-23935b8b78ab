get:
  security:
    - tokenAuthorizer: [ ]
      ghostAuthorizer: [ ]
  summary: "WS31 : Obtenir la structure d'un compteur, si utilisé pendant le mois de relève"
  parameters:
    - name: Langue
      in: header
      required: false
      schema:
        type: string
        example: FR
        default: FR
    - name: Ean
      in: query
      required: false
      schema:
        type: string
        example: '541460900001226224'
    - name: NumCompteur
      in: query
      required: false
      schema:
        type: string
        example: '14706809'
    - name: Token
      in: query
      required: false
      schema:
        type: string
      description: Token d'enregistrement d'index. <PERSON>résent, Ean et NumCompteur ne sont pas nécessaires.
  responses:
    '200':
      description: 200 success
      content:
        application/json:
          schema:
            type: object
            properties:
              Compteurs:
                type: array
                items:
                  type: object
                  properties:
                    NumCompteur:
                      type: string
                      example: "000000000016065554"
                    NumEquip:
                      type: string
                      example: "000000000500234508"
                    SectActivite:
                      type: string
                      example: "02"
                    Registres:
                      type: array
                      items:
                        type: object
                        properties:
                          NumCadran:
                            type: string
                            example: "000000000000042264"
                          TypeCadran:
                            type: string
                            example: "TH"
                          NbChiffre:
                            type: string
                            example: "05"
                          NbDecimales:
                            type: string
                            example: "03"
                          PosCadran:
                            type: string
                            example: "TOTAL"
              Ean:
                type: integer
                example: 541460900000512366
              Messages:
                type: array
                items:
                  type: object
                  properties:
                    Type:
                      type: string
                      example: "S"
                    ID:
                      type: string
                      example: "HTTP"
                    Number:
                      type: integer
                      example: 408
                    Message:
                      type: string
                      example: "Exécution sans erreurs"
    '500':
      description: Error
      content:
        application/json:
          schema:
            type: object
            properties:
              Error:
                type: integer
                example: 500
              Message:
                type: string
                example: "Nous n'avons enregistré aucune demande de changement de fournisseur."
              ErrorCode:
                type: string
                example: "SAP-509"
    '500_SAP-503':
      description: Error SAP-503
      content:
        application/json:
          example:
            Error: 500
            ErrorCode: SAP-503
            message: L'EAN ou le compteur n'est pas renseigné (ou les 2 références)
    '500_SAP-504':
      description: Error SAP-504
      content:
        application/json:
          example:
            Error: 500
            ErrorCode: SAP-504
            message: La référence du compteur encodé n'est pas associé à l'EAN renseigné.
    '500_SAP-505':
      description: Error SAP-505
      content:
        application/json:
          example:
            Error: 500
            ErrorCode: SAP-505
            message: Impossible de déterminer l'installation pour l'EAN
    '500_SAP-506':
      description: Error SAP-506
      content:
        application/json:
          example:
            Error: 500
            ErrorCode: SAP-506
            message: L'EAN n'est pas de type annuel
    '500_SAP-507':
      description: Error SAP-507
      content:
        application/json:
          example:
            Error: 500
            ErrorCode: SAP-507
            message: Il n'y a pas de contrat affecté à l'EAN pour la 'date du jour'
    '500_SAP-508':
      description: Error SAP-508
      content:
        application/json:
          example:
            Error: 500
            ErrorCode: SAP-508
            message: La fenêtre de temps n'est pas valide
    '500_SAP-509':
      description: Error SAP-509
      content:
        application/json:
          example:
            Error: 500
            ErrorCode: SAP-509
            message: Nous n'avons enregistré aucune demande de changement de fournisseur
    '500_SAP-527':
      description: Error SAP-527
      content:
        application/json:
          example:
            Error: 500
            ErrorCode: SAP-527
            message: Compteur &1 est communicant, donc encodage des index interdit
post:
  summary: "WS32 : Permet de mettre à jour l'index d'un compteur"
  parameters:
    - name: Token
      in: query
      required: false
      schema:
        type: string
      description: Token d'enregistrement d'index.
  requestBody:
    description: Relevé à introduire
    content:
      application/json:
        schema:
          type: object
          required:
            - Ean
            - Email
            - CodeConfirm
            - CatReleve
            - Compteurs
            - Token
          properties:
            Ean:
              type: string
              example: "541460900000512366"
            Email:
              type: string
              format: email
              example: "<EMAIL>"
            CodeConfirm:
              type: string
              example: "X"
            ChangeFour:
              type: string
              example: ""
            Informatif:
              type: string
              example: ""
            CatReleve:
              type: string
              example: "WEB"
            Compteurs:
              type: array
              items:
                type: object
                required:
                  - NumCompteur
                  - NumEquip
                  - IndexReleve
                properties:
                  NumCompteur:
                    type: string
                    example: "000000000016065554"
                  NumEquip:
                    type: string
                    example: "000000000500234508"
                  IndexReleve:
                    type: array
                    items:
                      type: object
                      required:
                        - NbChiffre
                        - NbDecimales
                        - NumCadran
                        - PosCadran
                        - TypeCadran
                        - IndexReleve
                        - isSubmitted
                        - error
                      properties:
                        NbChiffre:
                          type: string
                          example: "05"
                        NbDecimales:
                          type: string
                          example: "03"
                        NumCadran:
                          type: string
                          example: "000000000000042264"
                        PosCadran:
                          type: string
                          example: "TOTAL"
                        TypeCadran:
                          type: string
                          example: "TH"
                        IndexReleve:
                          type: string
                          example: "07911.044"
            Token:
              type: string
              example: "9664272366"
  responses:
    '200':
      description: 200 success
      headers:
        Access-Control-Allow-Origin:
          schema:
            type: string
      content:
        application/json:
          schema:
            type: object
            required:
              - Compteurs
              - Messages
            properties:
              Compteurs:
                type: array
                items:
                  type: object
                  required:
                    - NumCompteur
                    - NumEquip
                  properties:
                    NumCompteur:
                      type: string
                      example: "000000000016065554"
                    NumEquip:
                      type: string
                      example: "000000000500234508"
              Messages:
                type: array
                items:
                  type: object
                  required:
                    - Type
                    - ID
                    - Number
                    - Message
                  properties:
                    Type:
                      type: string
                      example: "S"
                    ID:
                      type: string
                      example: "HTTP"
                    Number:
                      type: integer
                      example: 435
                    Message:
                      type: string
                      example: "Votre/vos index ont bien été enregistré(s)"
    '500':
      description: Erreur
      content:
        application/json:
          schema:
            type: object
            required:
              - Error
              - Message
              - ErrorCode
            properties:
              Error:
                type: integer
                example: 500
              Message:
                type: string
                example: "Erreur imprévue lors du chargement de vos index."
              ErrorCode:
                type: string
                example: "SAP-534"