get:
  summary: "WS190 : <PERSON><PERSON><PERSON><PERSON> le swagger avec les infos sur les calls API."
  parameters:
    - name: SubPath
      description: <PERSON><PERSON>ne uniquement les appels commençant par sub_path
      in: query
      required: false
      schema:
        type: string
        example: "/utilisateurs"
  responses:
    '200':
      description: 200 success
    '400':
      description: "Bad Request"
      content:
        application/json:
          schema:
            "$ref": "#/components/schemas/MyResaAPI_Error"
          example:
            Error: 400
            Message: If queryStringParameters it should include 'sub_path'
    '406':
      description: "Not Acceptable"
      content:
        application/json:
          schema:
            "$ref": "#/components/schemas/MyResaAPI_Error"
          example:
            Error: 406
            Message: "The accept header is not in the right format"