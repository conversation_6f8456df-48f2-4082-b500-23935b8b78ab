get:
  summary: 'WS25 : Obtenir la liste des dépannages non planifiés (obligation légale)'
  description: |
    Retourne par défaut les pannes active sur un an et les pannes cloturée sur un mois.  
    Les inputs possibles sont [CdPostal, Localite, Rue] ou bien [IdLocalite,
    IdRue], ou bien [CdPostal] ou bien [IdLocalite, Rue] ou bien [Lat0, Lat1, Long0,
    Long1] ou bien aucun input
  parameters:
  - name: Langue
    in: header
    required: false
    schema:
      type: string
      example: FR
      default: FR
  - name: IdRue
    in: query
    required: false
    schema:
      type: integer
  - name: IdRadRue
    in: query
    required: false
    schema:
      type: integer
  - name: IdRadLocalite
    in: query
    required: false
    schema:
      type: integer
  - name: IdLocalite
    in: query
    required: false
    schema:
      type: integer
  - name: CdPostal
    in: query
    required: false
    schema:
      type: integer
      example: 4040
  - name: Localite
    in: query
    required: false
    schema:
      type: string
      example: Herstal
  - name: DateDebut
    in: query
    required: false
    schema:
      type: integer
  - name: DateFin
    in: query
    required: false
    schema:
      type: integer
  - name: Rue
    in: query
    required: false
    schema:
      type: string
      example: Rue Emile Tilman
  - name: Lat0
    in: query
    description: La plus petite latitude que peut avoir une interruption dans la réponse.
    required: false
    schema:
      type: number
      minimum: -90
      maximum: 90
      example: 50
  - name: Lat1
    in: query
    description: La plus grande latitude que peut avoir une interruption dans la réponse.
    required: false
    schema:
      type: number
      minimum: -90
      maximum: 90
      example: 51
  - name: Long0
    in: query
    description: La plus petite longitude que peut avoir une interruption dans la
      réponse.
    required: false
    schema:
      type: number
      minimum: -180
      maximum: 180
      example: 5
  - name: Long1
    in: query
    description: La plus grande longitude que peut avoir une interruption dans la
      réponse.
    required: false
    schema:
      type: number
      minimum: -180
      maximum: 180
      example: 6
  - name: PageSize
    description: Nombre d'élément max par page
    in: query
    schema:
      type: integer
    example: 20
  - name: Page
    description: Page à récupérer (pagination)
    in: query
    schema:
      type: integer
    example: 0
  responses:
    '200':
      description: OK
      content:
        application/json:
          example:
            Srid: '4326'
            Data:
            - Localite: Herstal
              CdPostal: 4040
              Rue: Rue Emile Tilman
              NumRue: '48'
              DatePanne: 20200708
              HeurePriseCharge: '10:34:53'
              Cause: Défaillance(s) technique(s)
              StatutPanne: Panne résolue
              Id: '000411110401'
              GPS:
                Lat: 50.669940998957124
                Long: 5.635794102630699
            Pagination:
              PageSize: 20
              CurrentPage: 0
              NbPage: 1
          schema:
            type: object
            required:
              - Srid
              - Data
            properties:
              Strid:
                type: integer
              Data:
                type: array
                items:
                  type: object
                  required:
                  - DatePanne
                  - Rue
                  - CdPostal
                  - Localite
                  - HeurePriseCharge
                  - StatutPanne
                  - Id
                  - Cause
                  properties:
                    DatePanne:
                      type: integer
                    Rue:
                      type: string
                    RueNum:
                      type: string
                    CdPostal:
                      type: integer
                    Localite:
                      type: string
                    HeurePriseCharge:
                      type: string
                    StatutPanne:
                      type: string
                    Id:
                      type: string
                    DureePanne:
                      type: string
                    Cause:
                      type: string
                    GPS:
                      type: object
                      properties:
                        Lat:
                          type: number
                        Long:
                          type: number
