get:
  summary: 'WS129 : Obtenir une liste de bornes de recharges sur le territoire Belge'
  responses:
    '200':
      description: 200 OK
      content:
        application/json:
          schema:
            type: array
            items:
              type: object
              properties:
                Id:
                  type: string
                  example: '9b46e1a0-4e72-11e8-a144-42010a840002'
                Address:
                  type: object
                  properties:
                    Road:
                      type: string
                      example: 'Rue du Bureau 69'
                    City:
                      type: string
                      example: 'Fleron'
                    PostalCode:
                      type: string
                      example: '4620'
                    Country:
                      type: string
                      example: 'BEL'
                    Coordinates:
                      type: object
                      properties:
                        Latitude:
                          type: string
                          example: '50.613569'
                        Longitude:
                          type: string
                          example: '5.694841'
                LastUpdated:
                  type: string
                  example: '2022-03-29T09:43:35Z'
                TwentyFourSeven:
                  type: boolean
                  example: true
                Evses:
                  type: array
                  items:
                    type: object
                    properties:
                      Uid: 
                        type: string
                        example: 'BEBCAEBCA107A*002'
                      Status: 
                        type: string
                        example: 'AVAILABLE'
                      Connectors:
                        type: array
                        items:
                          type: object
                          properties:
                            Id:
                              type: string
                              example: 'BEBCAEBCA107A*002_0'
                            Standard:
                              type: string
                              example: 'IEC_62196_T2'
                            Format:
                              type: string
                              example: 'SOCKET'
                            PowerType:
                              type: string
                              example: 'AC_3_PHASE'
                            Pricing:
                              type: string
                              example: null
                            Power:
                              type: number
                              example: 7360
post:
  summary: "WS153 : Déclarer une borne de recharge"
  parameters: [ ]
  requestBody:
    content:
      application/json:
        schema:
          type: object
          required:
            - TypeDemande
            - Demandeur
            - Borne
          properties:
            TypeDemande:
              type: object
              required:
                - Libelle
                - Valeur
              properties:
                Libelle:
                  type: string
                Valeur:
                  type: string
                  example: "Activate / Deactivate"
            Demandeur:
              type: object
              required:
                - Nom
                - Prenom
                - Email
                - Telephone
              properties:
                Nom:
                  type: string
                Prenom:
                  type: string
                Email:
                  type: string
                Telephone:
                  type: string
            Installateur:
              type: object
              properties:
                Nom:
                  type: string
                Email:
                  type: string
                Telephone:
                  type: string
            Entreprise:
              type: object
              properties:
                Numero:
                  type: string
                Nom:
                  type: string
                Acronyme:
                  type: string
                FormeJuridique:
                  type: string
            Borne:
              type: object
              required:
                - Adresse
                - Ean
                - Date
                - Marque
                - Modele
                - Puissance
                - Utilisation
                - Bidirectionnelle
              properties:
                Adresse:
                  type: object
                  required:
                    - Rue
                    - Numero
                    - CodePostal
                    - Commune
                    - Pays
                  properties:
                    Rue:
                      type: string
                    Numero:
                      type: string
                    CodePostal:
                      type: string
                    Commune:
                      type: string
                    Pays:
                      type: string
                Ean:
                  type: string
                Date:
                  type: string
                  description: Date de mise en service au format ISO 8601
                  example: "2022-10-17"
                Marque:
                  type: string
                Modele:
                  type: string
                Puissance:
                  type: number
                Utilisation:
                  type: object
                  required:
                    - Libelle
                    - Valeur
                  properties:
                    Libelle:
                      type: string
                    Valeur:
                      type: string
                Bidirectionnelle:
                  type: boolean
                Serial:
                  type: string
                Photo:
                  type: string
                  description: Lien GET S3 pré-signé obtenu sur le WS112
  responses:
    '201':
      description: 201 CREATED
      content:
        application/json:
          schema:
            type: object
            required:
              - TypeDemande
              - Demandeur
              - Borne
            properties:
              TypeDemande:
                type: string
                enum:
                  - Ajout
                  - Suppression
              Demandeur:
                type: object
                required:
                  - Nom
                  - Prenom
                  - Email
                  - Telephone
                properties:
                  Nom:
                    type: string
                  Prenom:
                    type: string
                  Email:
                    type: string
                  Telephone:
                    type: string
              Installateur:
                type: object
                properties:
                  Nom:
                    type: string
                  Email:
                    type: string
                  Telephone:
                    type: string
              Entreprise:
                type: object
                properties:
                  Numero:
                    type: string
                  Nom:
                    type: string
                  Acronyme:
                    type: string
                  FormeJuridique:
                    type: string
              Borne:
                type: object
                required:
                  - Adresse
                  - Ean
                  - MiseEnService
                  - Marque
                  - Modele
                  - Puissance
                  - Utilisation
                  - Bidirectionnelle
                properties:
                  Adresse:
                    type: object
                    required:
                      - Rue
                      - Numero
                      - CodePostal
                      - Commune
                      - Pays
                    properties:
                      Rue:
                        type: string
                      Numero:
                        type: string
                      CodePostal:
                        type: string
                      Commune:
                        type: string
                      Pays:
                        type: string
                  Ean:
                    type: string
                  MiseEnService:
                    type: string
                    description: Date de mise en service au format ISO 8601
                    example: "2022-10-17"
                  Marque:
                    type: string
                  Modele:
                    type: string
                  Puissance:
                    type: number
                  Utilisation:
                    type: string
                    enum:
                      - Privée
                      - Publique
                  Bidirectionnelle:
                    type: boolean
                  Serial:
                    type: string
                  Photo:
                    type: string
                    description: Lien GET S3 pré-signé obtenu sur le WS112
              DateCreation:
                type: string
                description: Date de création de la demande au format ISO 8601 en UTC
                example: "2022-10-17T12:43:10"
