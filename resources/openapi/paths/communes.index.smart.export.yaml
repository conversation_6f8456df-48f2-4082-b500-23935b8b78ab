get:
  summary: "WS222 : Export des données de consommation smart pour une commune"
  description: >
    Lance un export des données de consommation smart pour tous les compteurs de la commune liée à l'utilisateur connecté.
    Une fois l'export terminé, un lien de téléchargement est envoyé à l'utilisateur par e-mail. Ce lien est valide pendant 48h.
  parameters:
    - name: "Authorization"
      in: "header"
      required: true
      schema:
        type: "string"
  responses:
    '200':
      description: "Données de consommation récupérées avec succès."
      content:
        application/json:
          schema:
            type: object
            properties:
              ExportId:
                type: string
                example: "2f32c92f-1d55-4b6a-b3d9-167934f8fc50"
                description: "Identifiant unique de l'export."
               