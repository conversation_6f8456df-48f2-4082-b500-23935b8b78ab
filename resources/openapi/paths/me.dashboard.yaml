get:
  summary: "WS14 : Obtenir l'ensemble des données concernant un utilisateur authentifié"
  parameters:
    - name: Quick
      description: quick mode have data less enriched but load faster
      in: query
      required: false
      schema:
        type: string
        enum:
          - 'true'
          - 'false'
        default: 'false'
  security:
    - tokenAuthorizer: [ ]
  responses:
    '200':
      description: 200 success
      headers:
        Access-Control-Allow-Origin:
          schema:
            type: string
      content:
        application/json:
          schema:
            type: object
            properties:
              Uid:
                type: string
              Permissions:
                type: array
                items:
                  type: string
              Email:
                type: string
                format: email
                nullable: true
              Bp:
                type: number
              Master:
                type:
                  - string
                  - null
              Ppp:
                type: boolean
              SmartPortal:
                type: boolean
              SmartPortalConsent:
                type:
                  - boolean
                  - null
                description: Flag de consentement pour l'utilisation et visualisation des données smart
              ValidPhone:
                type: boolean
              ValidContactEmail:
                type: boolean
              Contact:
                type: object
                properties:
                  ContactEmail:
                    type: string
                    format: email
                  Phone:
                    type: string
                  PhoneFixe:
                    type: string
                  Adresse:
                    type: object
                    properties:
                      Localite:
                        type: string
                      NumRue:
                        type: string
                      Cdpostal:
                        type: string
                      Rue:
                        type: string
                      CodePays:
                        type: string
              Commune:
                type:
                  - object
                  - 'null'
                properties:
                  Id:
                    type: string
                    example: '000'
                  Localite:
                    type: string
                    example: 'Liège'
                  CodePostaux:
                    type: array
                    items:
                      type: number
                      example: '4000'
                  Fonction:
                    type: string
                    example: 'Fonction'
                  Departement:
                    type: string
                    example: 'Departement'
                  Admin:
                    type: boolean
                    example: 'Fonction'
                  Roles:
                    type: array
                    items:
                      type: string
                      example: 'RACC_READ'
              Firstname:
                type: string
              Lastname:
                type: string
              ListeEan:
                type: array
                items:
                  type: object
                  properties:
                    Ean:
                      type: string
                    Adresse:
                      type: object
                      properties:
                        CdPostal:
                          type: string
                        Localite:
                          type: string
                        Rue:
                          type: string
                        NumRue:
                          type: string
                    AddressHash:
                      type: string
                      nullable: true
                    Status:
                      type: string
                    SectActivite:
                      type: string
                    NumeroContrat:
                      type: array
                      items:
                        type: number
                    HaugazelId:
                      type: array
                      items:
                        type: number
                    DateFrom:
                      type: string
                      format: date
                    DateTo:
                      type: string
                      format: date
                    Cpt:
                      type: array
                      items:
                        type: string
                    Meters:
                      type: array
                      items:
                        type: object
                        properties:
                          Id:
                            type: string
                          Rates:
                            type: array
                            items:
                              type: string
                          NightOnly:
                            type: boolean
                    Profile:
                      type: object
                      properties:
                        SmartMeter:
                          type: boolean
                        Social:
                          type: boolean
                    History:
                      type: array
                      items:
                        type: object
                        properties:
                          Ean:
                            type: string
                          Adresse:
                            type: object
                            properties:
                              CdPostal:
                                type: string
                              Localite:
                                type: string
                              Rue:
                                type: string
                              NumRue:
                                type: string
                          Status:
                            type: string
                          SectActivite:
                            type: string
                          PartenaireId:
                            type: string
                          NumeroContrat:
                            type: number
                          HaugazelId:
                            type: number
                          DateFrom:
                            type: string
                            format: date
                          DateTo:
                            type: string
                            format: date
                          Cpt:
                            type: string
                          Rates:
                            type: array
                            items:
                              type: string
                          NightOnly:
                            type: boolean
                          Profile:
                            type: object
                            properties:
                              SmartMeter:
                                type: boolean
                              Social:
                                type: boolean
              Dossiers:
                type: array
                items:
                  type: object
                description: Currently empty
              Notifications:
                type: array
                items:
                  type: object
                description: Currently empty
              Preferences:
                type: object
                additionalProperties:
                  type: boolean