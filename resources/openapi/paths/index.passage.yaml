get:
  summary: |
    WS33 : Obtenir les dates de passage de l'indexier et la période d'encodage
    d'un EAN, d'un compteur ou les deux.  
    Les mêmes informations sont disponibles dans la réponse du WS27 /ean
  description: Dans le cas ou EAN et compteur sont tous les deux fournis, il faut
    que le compteur corresponde effectivement a l'EAN
  parameters:
  - name: Langue
    in: header
    required: false
    schema:
      type: string
      example: FR
      default: FR
  - name: Ean
    in: query
    schema:
      type: string
      example: '541449020710607019'
  - name: NumCompteur
    in: query
    schema:
      type: string
      example: 1SAG1100078049
  responses:
    '200':
      description: 200 success
      headers:
        Access-Control-Allow-Origin:
          schema:
            type: string
      content:
        application/json:
          schema:
            type: object
            required:
            - DateDebut
            - DateFin
            - CptSmart
            properties:
              StatutEan:
                type: string
              DateDebut:
                type:
                - integer
                - string
              DateFin:
                type:
                - integer
                - string
              DtLimiDebut:
                type:
                - integer
                - string
              DtLimiFin:
                type:
                - integer
                - string
              CptSmart:
                type: boolean
          example:
            StautEan: ACTIF
            DateDebut: 20210901
            DateFin: 20210902
            DtLimiDebut: 20210818
            DtLimiFin: 20211015
            CptSmart: X
