get:
  summary: "WS117 : Retrouve des EAN selon des critères de recherche"
  description: |
    Les parametres Phone, PhoneFixe, Email, ContactEmail sont optionnels, mais il est nécéssaire de fournir au minimum
    un des 4 afin d'obtenir un résultat.
  security:
    - basicAuthorizer: []
  parameters:
    - name: Lastname
      in: query
      required: true
      schema:
        type: string
        example: John
    - name: Firstname
      in: query
      required: true
      schema:
        type: string
        example: Doo
    - name: Phone
      in: query
      required: false
      schema:
        type: string
        example: +32496123456
        default: null
    - name: PhoneFixe
      in: query
      required: false
      schema:
        type: string
        example: +328712356
        default: null
    - name: Email
      in: query
      required: false
      schema:
        type: string
        example: <EMAIL>
        default: null
    - name: ContactEmail
      in: query
      required: false
      schema:
        type: string
        example: <EMAIL>
        default: null
  responses:
    "200":
      description: 200 OK
      content:
        application/json:
          schema:
            type: array
            description: Liste des EAN trouvé
            items:
              type: string
              example: "541456700000601506"
    "404":
      description: 404 NOT FOUND
      content:
        application/json:
          schema:
            type: object
            properties:
              Error:
                type: number
                example: 404
              Message:
                type: string
                example: "Aucun résultat retrouvé"
