{"get": {"summary": "WS20: O<PERSON><PERSON>r les services énergétiques présents dans une rue à partir d'une adresse", "parameters": [{"name": "Localite", "in": "query", "required": false, "schema": {"type": "string", "example": "Verviers"}}, {"name": "Rue", "in": "query", "required": false, "schema": {"type": "string", "example": "Rue <PERSON>"}}, {"name": "Numero", "in": "query", "description": "Numéro de rue. Utilisé uniquement dans la recherche de la disponibilité et distance du gaz. Si null, le premier numéro de la rue est pris par défaut.", "required": false, "schema": {"type": "string", "example": "1"}}, {"name": "Cdpostal", "in": "query", "required": false, "schema": {"type": "string", "example": "4800"}}, {"name": "IdCommune", "in": "query", "required": false, "schema": {"type": "string", "example": "62063"}}], "responses": {"200": {"description": "200 success", "headers": {"Access-Control-Allow-Origin": {"schema": {"type": "string"}}}, "content": {"application/json": {"schema": {"type": "object", "required": ["Elec", "Gaz", "IsGRDElec", "IsGRDGaz", "Ep", "DistGaz", "DispoGaz"], "properties": {"Elec": {"type": "string", "description": "'X' si Resa est le GRD dans la rue/ville."}, "Gaz": {"type": "string", "description": "'X' si Resa est le GRD dans la rue/ville."}, "IsGRDElec": {"type": "string", "description": "'X' si Resa est le GRD dans la ville."}, "IsGRDGaz": {"type": "string", "description": "'X' si Resa est le GRD dans la ville."}, "Ep": {"type": "string", "description": "'X' si Resa gère l'éclairage public ville."}, "DistGaz": {"type": "number", "description": "Distance entre l'habitation et la canalisation."}, "DispoGaz": {"type": "string", "description": "'YES|MAYBE|NO' selon la distance DistGaz."}}}, "example": {"Elec": "", "Gaz": "X", "IsGRDElec": "", "IsGRDGaz": "X", "Ep": "", "DistGaz": 0.7190118961409092, "DispoGaz": "YES"}}}}, "400": {"description": "400 missing parameter", "headers": {"Access-Control-Allow-Origin": {"schema": {"type": "string"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MyResaAPI_Error"}, "example": {"Error": 400, "Message": "Missing required request parameters: [Localite]"}}}}, "500": {"description": "500 Internal Server Error", "headers": {"Access-Control-Allow-Origin": {"schema": {"type": "string"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MyResaAPI_Error"}, "example": {"Error": 500, "Message": "Pas inforamtion disponible !"}}}}}}}