post:
  summary: "WS32.1 : Permet de mettre à jour l'index de plusieurs compteur"
  parameters:
    - name: Token
      in: query
      required: false
      schema:
        type: string
      description: Token d'enregistrement d'index.
    - name: MessageId
      in: query
      required: false
      schema:
        type: string
      description: Job id injecté par le /asynchrone
  requestBody:
    description: Relevé à introduire
    content:
      application/json:
        schema:
          type: array
          items:
            type: object
            required:
              - Ean
              - Email
              - CodeConfirm
              - CatReleve
              - Compteurs
              - Token
            properties:
              Ean:
                type: string
                example: "541460900000512366"
              Email:
                type: string
                format: email
                example: "<EMAIL>"
              CodeConfirm:
                type: string
                example: "X"
              ChangeFour:
                type: string
                example: ""
              Informatif:
                type: string
                example: ""
              CatReleve:
                type: string
                example: "WEB"
              Token:
                type: string
                example: "some-token-value"
              Compteurs:
                type: array
                items:
                  type: object
                  required:
                    - NumCompteur
                    - NumEquip
                    - IndexReleve
                  properties:
                    NumCompteur:
                      type: string
                      example: "000000000016065554"
                    NumEquip:
                      type: string
                      example: "000000000500234508"
                    IndexReleve:
                      type: array
                      items:
                        type: object
                        required:
                          - NbChiffre
                          - NbDecimales
                          - NumCadran
                          - PosCadran
                          - TypeCadran
                          - IndexReleve
                          - isSubmitted
                          - error
                        properties:
                          NbChiffre:
                            type: string
                            example: "05"
                          NbDecimales:
                            type: string
                            example: "03"
                          NumCadran:
                            type: string
                            example: "000000000000042264"
                          PosCadran:
                            type: string
                            example: "TOTAL"
                          TypeCadran:
                            type: string
                            example: "TH"
                          IndexReleve:
                            type: string
                            example: "07911.044"
                          isSubmitted:
                            type: boolean
                            example: false
                          error:
                            type: string
                            example: ""
        example:
          - Ean: "541460900000512366"
            Email: "<EMAIL>"
            CodeConfirm: "X"
            ChangeFour: ""
            Informatif: ""
            CatReleve: "WEB"
            Token: "some-token-value"
            Compteurs:
              - NumCompteur: "000000000016065554"
                NumEquip: "000000000500234508"
                IndexReleve:
                  - NbChiffre: "05"
                    NbDecimales: "03"
                    NumCadran: "000000000000042264"
                    PosCadran: "TOTAL"
                    TypeCadran: "TH"
                    IndexReleve: "07911.044"
                    isSubmitted: false
                    error: ""
          - Ean: "541460900000512367"
            Email: "<EMAIL>"
            CodeConfirm: "Y"
            ChangeFour: "some-change"
            Informatif: "some-info"
            CatReleve: "MOBILE"
            Token: "another-token-value"
            Compteurs:
              - NumCompteur: "000000000016065555"
                NumEquip: "000000000500234509"
                IndexReleve:
                  - NbChiffre: "06"
                    NbDecimales: "02"
                    NumCadran: "000000000000042265"
                    PosCadran: "BOTTOM"
                    TypeCadran: "LO"
                    IndexReleve: "05432.010"
                    isSubmitted: true
                    error: "some-error"
  
  responses:
    '200':
      description: 200 success
      headers:
        Access-Control-Allow-Origin:
          schema:
            type: string
      content:
        application/json:
          schema:
            type: object
            required:
              - Compteurs
              - Messages
            properties:
              Compteurs:
                type: array
                items:
                  type: object
                  required:
                    - NumCompteur
                    - NumEquip
                  properties:
                    NumCompteur:
                      type: string
                      example: "000000000016065554"
                    NumEquip:
                      type: string
                      example: "000000000500234508"
              Messages:
                type: array
                items:
                  type: object
                  required:
                    - Type
                    - ID
                    - Number
                    - Message
                  properties:
                    Type:
                      type: string
                      example: "S"
                    ID:
                      type: string
                      example: "HTTP"
                    Number:
                      type: integer
                      example: 435
                    Message:
                      type: string
                      example: "Votre/vos index ont bien été enregistré(s)"
    '500':
      description: Erreur
      content:
        application/json:
          schema:
            type: object
            required:
              - Error
              - Message
              - ErrorCode
            properties:
              Error:
                type: integer
                example: 500
              Message:
                type: string
                example: "Erreur imprévue lors du chargement de vos index."
              ErrorCode:
                type: string
                example: "SAP-534"