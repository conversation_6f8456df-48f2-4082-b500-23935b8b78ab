get:
  summary: "Génère un UUID v1 ou une liste d'UUIDs v1."
  description: |
    Utilise également un paramètre de requête `num` pour déterminer combien d'UUIDs v1 générer. Si `num` est présent et valide, le service renvoie une liste de UUIDs v1. <PERSON>n, il renvoie un seul UUID v1.
  parameters:
    - name: Num
      in: query
      required: false
      schema:
        type: integer
      description: "Le nombre d'UUIDs à générer. Si non spécifié, un seul UUID est généré."
  responses:
    '200 single_uuid':
      description: "OK, l'opération a réussi."
      content:
        application/json:
          schema:
            type: string
            format: uuid
            example: "4f4f8b8f-29a9-470d-b4f8-031a9e0e9d0f"
    '200 multiple_uuid':
      description: "OK, l'opération a réussi."
      content:
        application/json:
          schema:
            type: array
            items:
              type: string
              format: uuid
            example: [ "4f4f8b8f-29a9-470d-b4f8-031a9e0e9d0f", "4f0f8b8f-29a9-470d-b4f8-031a9e0e9d0f" ]
    '400 NUM_INVALID':
      description: "BAD REQUEST, NUM_INVALID."
      content:
        application/json:
          schema:
            type: object
            properties:
              Error:
                type: integer
                example: 400
              Message:
                type: string
                example: "Num must be an integer > 1"
              Error_code:
                type: string
                example: NUM_INVALID
    '400 NUM_MISSING':
      description: "BAD REQUEST, NUM_MISSING."
      content:
        application/json:
          schema:
            type: object
            properties:
              Error:
                type: integer
                example: 400
              Message:
                type: string
                example: "Invalid params in queryStringParameters"
              Error_code:
                type: string
                example: NUM_MISSING
                
