get:
  summary: 'WS111 : Obtenir liste de pays'
  parameters:
    - name: Langue
      in: header
      required: false
      schema:
        type: string
        example: FR
        default: FR
  responses:
    '200':
      description: 200 success
      headers:
        Access-Control-Allow-Origin:
          schema:
            type: string
      content:
        application/json:
          schema:
            type: object
            required:
              - Liste
            properties:
              Liste:
                type: array
                items:
                  type: object
                  required:
                    - CodePays
                    - NomPays
                  properties:
                    CodePays:
                      type: string
                    NomPays:
                      type: string
          example:
            Liste:
              - CodePays: 'AD'
                NomPays: "Principauté d'Andorre"
              - CodePays: 'AE'
                NomPays: "Emirats arabes unis"
