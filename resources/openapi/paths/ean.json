{"get": {"summary": "WS27 : Obtenir des EANs liés à un EAN sachant un de ses NumCpt. Si credentials fournies, on peut se passer du NumCpt ou fournir un objet de raccordement au lieu d'un EAN, l'output est alors enrichi", "description": "Trois cas d'utilisation sont servis par cet appel. Cas 1: on fournir un EAN et un de ses NumCpt. On récupère l;es EAN liés au même point de consommation. Cas 2 (utilisateur authentifié): quand un objet de raccordement est fourni: permet d'obtenr la liste des EAN sur le même objet de raccordement. Cas 3 (utilisateur authentifié): quand un EAN est fourni, permet d'obtenir l'information des EANs présents sur l'objet de raccordement correspondant à l'EAN fourni", "security": [{"basicAuthorizer": []}], "parameters": [{"name": "<PERSON><PERSON>", "in": "header", "required": false, "schema": {"type": "string", "example": "FR", "default": "FR"}}, {"name": "Localite", "in": "query", "description": "Localté où on veut chercher des EAN (requis dans le cas de recherche par addresse)", "schema": {"type": "string"}}, {"name": "Rue", "in": "query", "description": "Rue où on veut chercher des EAN (requis dans le cas de recherche par addresse)", "schema": {"type": "string"}}, {"name": "Cdpostal", "in": "query", "description": "Code postal où on veut chercher des EAN (requis dans le cas de recherche par addresse)", "schema": {"type": "string"}}, {"name": "NumRue", "in": "query", "description": "Numero de rue où on veut chercher des EAN (requis dans le cas de recherche par addresse)", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON>", "in": "query", "description": "EAN dont on veut obtenir des infos (requis dans le cas de recherche par EAN - l'exemple fournit ne correspond pas à la recherche par adresse.)", "schema": {"type": "string", "example": "541456700000601506"}}, {"name": "NumCpt", "in": "query", "description": "Compteur lié à l'EAN. Requis dans le cas d'une recherche par EAN", "schema": {"type": "string", "example": "37668055"}}], "responses": {"200": {"description": "200 success", "headers": {"Access-Control-Allow-Origin": {"schema": {"type": "string"}}}, "content": {"application/json": {"schema": {"type": "object", "required": ["ListeEan"], "properties": {"ListeEan": {"type": "array", "items": {"type": "object", "required": ["<PERSON><PERSON>", "SectActivite", "DateDebut", "DateFin", "DtLimiDebut", "DtLimiFin", "CptSmart", "NumCpt"], "properties": {"Ean": {"type": ["integer", "string"]}, "SectActivite": {"type": "string"}, "DateDebut": {"type": ["integer", "string"]}, "DateFin": {"type": ["integer", "string"]}, "CptSmart": {"type": "string"}, "NumCpt": {"type": "string"}, "NumCpt2": {"type": "string"}, "NumCpt3": {"type": "string"}}}}}}, "example": {"ListeEan": [{"Ean": "541460900000575415", "SectActivite": "02", "DateDebut": 20210201, "DateFin": 20210218, "CptSmart": "", "NumCpt": "21451579"}, {"Ean": "541456700000601506", "SectActivite": "01", "DateDebut": 20210101, "DateFin": 20220120, "CptSmart": "", "NumCpt": "37668055"}]}}}}, "500": {"description": "500 Internal Server Error", "headers": {"Access-Control-Allow-Origin": {"schema": {"type": "string"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MyResaAPI_Error"}, "example": {"Error": 500, "Message": "Aucunes données trouvées pour la sélection !"}}}}}}}