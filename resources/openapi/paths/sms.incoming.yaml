post:
  summary: 'WS124 : <PERSON><PERSON><PERSON> les SMS entrant'
  description: Endpoint ayant pour but de gérer les SMS entrant depuis RingRing ou autre service.
  requestBody:
    description: Peut ne pas avoir le même format selon le service, l'exemple suivant étant pour RingRing.
    content:
      application/json:
        schema:
          type: object
          properties:
            MessageId:
              type: string
              example: "UNIQUE-MESSAGE-ID"
            Reference:
              type: string
              example: null
            From:
              type: string
              example: "32496123456"
            To:
              type: string
              example: "8810"
            Message:
              type: string
              example: "RESA SP 123456789"
            Country:
              type: string
              example: "BE"
            TimeReceived:
              type: string
              example: "2016/07/04 14:21:05"
  responses:
    '204':
      description: 204 NO CONTENT
