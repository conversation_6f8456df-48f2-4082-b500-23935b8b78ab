post:
  summary: "WS199 : Soumission d'une demande de raccordement"
  description: |
    Ce Endpoint permet de soumettre une demande de raccordement avec les informations du demandeur, de l'adresse, du contact, de la facturation et des compteurs.
    Il va envoyer une demande asynchrone sur demande_travaux/demande, et récupérera un process_id.
    Ensuite va uploader un fichier sur notre S3 avec comme nom de dossier :{process_id}_{sectActivitte}.
    Cela permettra de le récupérer lors du callback et d'envoyer le fichier sur la bonne valise SharePoint.    
  requestBody:
    required: true
    content:
      application/json:
        schema:
          type: object
          required:
            - EnergyType
            - WorkType
            - ApplicantType
            - ActAs
            - Name
            - Firstname
            - Email
            - Phone
            - Address
            - Meters
          properties:
            EnergyType:
              type: string
              description: Type d'énergie
              example: "ELEC"
            WorkType:
              type: string
              description: Type de travaux
              example: "MODI_SMART"
            ApplicantType:
              type: string
              description: Type de demandeur (particulier ou société)
              example: "particulier|societe"
            ActAs:
              type: string
              description: Statut d'action (propriétaire ou locataire)
              example: "PROP|LOC"
            Name:
              type: string
              description: Nom de famille du demandeur
              example: "Doe"
            Firstname:
              type: string
              description: Prénom du demandeur
              example: "John"
            Email:
              type: string
              description: Email du demandeur
              example: "<EMAIL>"
            Phone:
              type: string
              description: Numéro de téléphone du demandeur
              example: "+32 11 22 33 44"
            Address:
              type: object
              description: Adresse du demandeur
              required:
                - Street
                - Number
                - Postcode
                - City
              properties:
                Street:
                  type: string
                  example: "avenue des sillons"
                Number:
                  type: string
                  example: "302"
                Box:
                  type: string
                  example: "A"
                Postcode:
                  type: string
                  example: "4100"
                City:
                  type: string
                  example: "Boncelles"
            Contact:
              type: object
              description: Adresse de contact
              required:
                - Street
                - Number
                - Postcode
                - City
                - Country
              properties:
                Street:
                  type: string
                  example: "avenue des sillons"
                Number:
                  type: string
                  example: "302"
                Box:
                  type: string
                  example: "A"
                Postcode:
                  type: string
                  example: "4100"
                City:
                  type: string
                  example: "Boncelles"
                Country:
                  type: string
                  example: "Belgique"
            Billing:
              type: object
              description: Adresse de facturation
              required:
                - Street
                - Number
                - Postcode
                - City
                - Country
              properties:
                Street:
                  type: string
                  example: "avenue des sillons"
                Number:
                  type: string
                  example: "302"
                Box:
                  type: string
                  example: "A"
                Postcode:
                  type: string
                  example: "4100"
                City:
                  type: string
                  example: "Boncelles"
                Country:
                  type: string
                  example: "Belgique"
            Company:
              type: object
              description: Informations de l'entreprise
              required:
                - Name
                - LegalStatus
                - Tva
              properties:
                Name:
                  type: string
                  example: "RESA"
                LegalStatus:
                  type: string
                  example: "SA"
                Tva:
                  type: string
                  example: "xxxxx"
            Meters:
              type: array
              description: Liste des compteurs
              items:
                type: object
                required:
                  - Ean
                  - Number
                properties:
                  Ean:
                    type: string
                    description: Code EAN
                    example: "5414789632156"
                  Number:
                    type: string
                    example: "4017894"
                  Photo:
                    type: string
                    example: "https://photo.url.com"
  responses:
    '204':
      description: "No contents"
    '400 MISSING_FIELDS':
      description: MISSING_FIELDS
      content:
        application/json:
          schema:
            type: object
            properties:
              Message:
                type: string
                example: "Missing fields in body : ['Energytype']"
              Error:
                type: string
                example: "400"
              ErrorCode:
                type: string
                example: "MISSING_FIELDS"
    '500 DOWNLOAD_ERROR':
      description: Erreur serveur
      content:
        application/json:
          schema:
            type: object
            properties:
              Message:
                type: string
                example: "Erreur lors du téléchargement du fichier"
              Error:
                type: string
                example: "500"
              ErrorCode:
                type: string
                example: "DOWNLOAD_ERROR"
    '500 UPLOAD_ERROR':
      description: Erreur serveur
      content:
        application/json:
          schema:
            type: object
            properties:
              Message:
                type: string
                example: "Erreur lors de l'upload du fichier"
              Error:
                type: string
                example: "500"
              ErrorCode:
                type: string
                example: "UPLOAD_ERROR"
