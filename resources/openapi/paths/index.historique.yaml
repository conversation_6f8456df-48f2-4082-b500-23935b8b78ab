get:
  summary: "WS34 : Obtenir historique des relevés d'index à partir de l'Ean et/ou du NumCompteur"
  description: |
    Les inputs possibles sont :  
    - [Ean] (authentifié): Retourne l'historique des relevés de tous les contrats liés à l'utilisateur connecté  
    - [Ean + NumCompteur] (non-authentifié) : Retourne l'historique des relevés de tous les contrats lié à la même personne (dernier contrat actif, via nom et prénom)
  security:
    - tokenAuthorizer: [ ]
  parameters:
    - name: Ean
      in: query
      required: true
      schema:
        type: string
        example: '541460900000495706'
    - name: NumCompteur
      in: query
      required: false
      schema:
        type: string
        example: '000000000097207997'
    - name: Langue
      in: header
      required: false
      schema:
        type: string
        example: FR
        default: FR
  responses:
    '200':
      description: 200 success
      headers:
        Access-Control-Allow-Origin:
          schema:
            type: string
      content:
        application/json:
          schema:
            type: object
            required:
              - Contrat
            properties:
              NomContrat:
                type: integer
              DetailContrat:
                type: array
                items:
                  required:
                    - DateRel
                    - DetailCpt
                    - Tarif
                  properties:
                    DateRel:
                      type: integer
                    DetailCpt:
                      type: array
                      items:
                        required:
                          - NumCompteur
                          - Compteur
                        properties:
                          NumCompteur:
                            type: integer
                          Compteur:
                            type: object
                            required:
                              - Cadran
                            properties:
                              Cadran:
                                type: object
                                required:
                                  - CatCadran
                                  - Index
                                  - IndexUnit
                                  - IndexQual
                                properties:
                                  CatCadran:
                                    type: string
                                  Index:
                                    type: string
                                  IndexUnit:
                                    type: string
                                  IndexQual:
                                    type: string
                                  Cadran:
                                    type: string
                    Tarif:
                      type: array
                      items:
                        type: object
                        required:
                          - CatTarif
                          - Qtt
                          - QttUnit
                        properties:
                          CatTarif:
                            type: string
                          Qtt:
                            type: string
                          QttUnit:
                            type: string
                          Cadran:
                            type: string
          example:
            Contrat:
              - NomContrat: 2000604374
                DetailContrat:
                  - DateRel: 20150302
                    DetailCpt:
                      - NumCompteur: 000000000097207997
                        Compteur:
                          - Cadran:
                              - CatCadran: TH
                                Index: 40517
                                IndexUnit: M3
                                IndexQual: 01
                    Tarif:
                      - CatTarif: TH
                        Qtt: '18786.6401925'
                        QttUnit: KWH
                  - DateRel: 20160301
                    DetailCpt:
                      - NumCompteur: 000000000097207997
                        Compteur:
                          - Cadran:
                              - CatCadran: TH
                                Index: 42499
                                IndexUnit: M3
                                IndexQual: 01
                    Tarif:
                      - CatTarif: TH
                        Qtt: '22085.6349659'
                        QttUnit: KWH
                  - DateRel: 20170307
                    DetailCpt:
                      - NumCompteur: 000000000097207997
                        Compteur:
                          - Cadran:
                              - CatCadran: TH
                                Index: 44753
                                IndexUnit: M3
                                IndexQual: 02
                    Tarif:
                      - CatTarif: TH
                        Qtt: '25194.8200192'
                        QttUnit: KWH
                  - DateRel: 20171213
                    DetailCpt:
                      - NumCompteur: 000000000097207997
                        Compteur:
                          - Cadran:
                              - CatCadran: TH
                                Index: 45843
                                IndexUnit: M3
                                IndexQual: 02
                    Tarif:
                      - CatTarif: TH
                        Qtt: '12179.0060616'
                        QttUnit: KWH
              - NomContrat: 2003584651
                DetailContrat:
                  - DateRel: 20180305
                    DetailCpt:
                      - NumCompteur: 000000000097207997
                        Compteur:
                          - Cadran:
                              - CatCadran: TH
                                Index: 46678
                                IndexUnit: M3
                                IndexQual: 01
                    Tarif:
                      - CatTarif: TH
                        Qtt: '9327.1730161'
                        QttUnit: KWH
                  - DateRel: 20180731
                    DetailCpt:
                      - NumCompteur: 000000000097207997
                        Compteur:
                          - Cadran:
                              - CatCadran: TH
                                Index: 47106
                                IndexUnit: M3
                                IndexQual: 02
                    Tarif:
                      - CatTarif: TH
                        Qtt: '4764.4832174'
                        QttUnit: KWH
              - NomContrat: 2003765887
                DetailContrat:
                  - DateRel: 20180928
                    DetailCpt:
                      - NumCompteur: 000000000097207997
                        Compteur:
                          - Cadran:
                              - CatCadran: TH
                                Index: 47184
                                IndexUnit: M3
                                IndexQual: 02
                    Tarif:
                      - CatTarif: TH
                        Qtt: '875.5026547'
                        QttUnit: KWH
              - NomContrat: 2003811771
                DetailContrat:
                  - DateRel: 20190310
                    DetailCpt:
                      - NumCompteur: 000000000097207997
                        Compteur:
                          - Cadran:
                              - CatCadran: TH
                                Index: 48440
                                IndexUnit: M3
                                IndexQual: 02
                    Tarif:
                      - CatTarif: TH
                        Qtt: '14011.8059005'
                        QttUnit: KWH
                  - DateRel: 20200226
                    DetailCpt:
                      - NumCompteur: 000000000097207997
                        Compteur:
                          - Cadran:
                              - CatCadran: TH
                                Index: 50135
                                IndexUnit: M3
                                IndexQual: 02
                    Tarif:
                      - CatTarif: TH
                        Qtt: '18963.6953450'
                        QttUnit: KWH
            Consommation:
              - Datecons: 201603
                QttAnn: '22085.63'
                DetailConsommation:
                  - QttCadran: '22085.63'
                    CatCadran: TH
              - Datecons: 201703
                QttAnn: '25194.82'
                DetailConsommation:
                  - QttCadran: '25194.82'
                    CatCadran: TH
              - Datecons: 201803
                QttAnn: '21506.18'
                DetailConsommation:
                  - QttCadran: '21506.18'
                    CatCadran: TH
              - Datecons: 201903
                QttAnn: '19651.79'
                DetailConsommation:
                  - QttCadran: '19651.79'
                    CatCadran: TH
              - Datecons: 202002
                QttAnn: '18963.70'
                DetailConsommation:
                  - QttCadran: '18963.70'
                    CatCadran: TH
    '403 INVALID_EAN_FOR_USER':
      description: 403 Forbidden
      content:
        application/json:
          schema:
            type: object
            properties:
              Error:
                type: integer
                example: 403
              Message:
                type: string
                example: "This EAN doesn't belong to the given user."
              ErrorCode:
                type: string
                example: "INVALID_EAN_FOR_USER"
    '400 INVALID_INPUT':
      description: 400 Bad Request
      content:
        application/json:
          schema:
            type: object
            properties:
              Error:
                type: integer
                example: 400
              Message:
                type: string
                example: "Les possibles inputs sont [Ean] ou [Ean, NumCompteur]"
              ErrorCode:
                type: string
                example: "INVALID_INPUT"
    '400 EAN_CPT_NO_MATCH':
      description: 400 Bad Request
      content:
        application/json:
          schema:
            type: object
            properties:
              Error:
                type: integer
                example: 400
              Message:
                type: string
                example: "Le NumCpt fourni n'est pas associé à l'EAN fourni"
              ErrorCode:
                type: string
                example: "EAN_CPT_NO_MATCH"