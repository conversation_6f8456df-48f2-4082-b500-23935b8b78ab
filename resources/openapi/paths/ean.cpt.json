{"get": {"summary": "WS28 : <PERSON><PERSON><PERSON>r l'EAN associé à un compteur", "description": "WS28 : <PERSON><PERSON><PERSON><PERSON> l'EAN associé à un compteur. Dans le cas des clients éléc valorisateur 2 EAN peuvent correspondre à un seul compteur.", "parameters": [{"name": "<PERSON><PERSON>", "in": "header", "required": false, "schema": {"type": "string", "example": "FR", "default": "FR"}}, {"name": "NumCompteur", "in": "query", "required": true, "schema": {"type": "string", "example": "99821240"}}, {"name": "Mobile", "in": "query", "required": true, "schema": {"type": "string", "example": "+32494184967"}}, {"name": "ValidationCode", "in": "query", "required": true, "schema": {"type": "string", "example": "123456"}}], "responses": {"200": {"description": "200 success", "headers": {"Access-Control-Allow-Origin": {"schema": {"type": "string"}}}, "content": {"application/json": {"schema": {"type": "object", "required": ["ListeEan"], "properties": {"ListeEan": {"type": "array", "items": {"type": "object", "required": ["<PERSON><PERSON>"], "properties": {"Ean": {"type": ["integer", "string"]}}}}}}, "example": {"ListeEan": [{"Ean": 541456700002682240}, {"Ean": 541456700003900300}]}}}}, "500": {"description": "500 Internal Server Error", "headers": {"Access-Control-Allow-Origin": {"schema": {"type": "string"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MyResaAPI_Error"}, "example": {"Error": 500, "Message": "Aucunes données trouvées pour la sélection !"}}}}}}}