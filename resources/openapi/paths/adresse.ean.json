{"get": {"summary": "WS19 : O<PERSON><PERSON><PERSON> l'addresse du point de consommation énérgétique via son EAN", "parameters": [{"name": "<PERSON><PERSON>", "in": "header", "required": false, "schema": {"type": "string", "example": "FR", "default": "FR"}}, {"name": "<PERSON><PERSON>", "in": "query", "required": true, "schema": {"type": "string", "example": "541460900002617502"}}], "responses": {"200": {"description": "200 success", "headers": {"Access-Control-Allow-Origin": {"schema": {"type": "string"}}}, "content": {"application/json": {"schema": {"type": "object", "required": ["<PERSON><PERSON><PERSON>", "Localite", "Cdpostal", "Rue", "NumRue", "NumCompl", "IdRue", "IdLocalite", "IdAdresse", "IdRadRue", "IdRadLocalite"], "properties": {"Adresse": {"type": "string"}, "Localite": {"type": "string"}, "Cdpostal": {"type": "string"}, "Rue": {"type": "string"}, "NumRue": {"type": "integer"}, "NumCompl": {"type": "string"}, "IdRue": {"type": "string"}, "IdLocalite": {"type": "string"}, "IdAdresse": {"type": "string"}, "IdRadRue": {"type": "integer"}, "IdRadLocalite": {"type": "integer"}}}, "example": {"Adresse": "<PERSON><PERSON><PERSON>, Rue de Geer 17/F", "Localite": "<PERSON><PERSON><PERSON>", "Cdpostal": "4360", "Rue": "<PERSON>", "NumRue": 17, "NumCompl": "F", "IdRue": "", "IdLocalite": "", "IdAdresse": "", "IdRadRue": 140228, "IdRadLocalite": 112090}}}}, "400": {"description": "400 missing parameter", "headers": {"Access-Control-Allow-Origin": {"schema": {"type": "string"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MyResaAPI_Error"}, "example": {"Error": 400, "Message": "Missing required request parameters: [Ean]"}}}}}}}