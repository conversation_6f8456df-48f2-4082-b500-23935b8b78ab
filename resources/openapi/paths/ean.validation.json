{"get": {"summary": "WS107: Valider le format et checksum d'un EAN", "description": "", "parameters": [{"name": "<PERSON><PERSON>", "in": "query", "description": "L'EAN à valider", "schema": {"type": "string", "example": "541456700000601506"}}], "responses": {"200": {"description": "200 success", "headers": {"Access-Control-Allow-Origin": {"schema": {"type": "string"}}}, "content": {"application/json": {"schema": {"type": "object", "required": ["Valide"], "properties": {"Valide": {"type": "boolean"}}}, "example": {"Valide": true}}}}}}}