get:
  summary: "WS60 : Obt<PERSON>r le statut et les informations du port P1 d'un smart meter"
  parameters:
    - name: Langue
      in: header
      required: false
      schema:
        type: string
        example: FR
        default: FR
    - name: Ean
      in: query
      required: false
      schema:
        type: string
        example: '541456700003298574'
    - name: NumCpt
      in: query
      required: false
      schema:
        type: string
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            type: object
            required:
              - Liste
            properties:
              Liste:
                type: array
                items:
                  type: object
                  properties:
                    NumCpt:
                      type: string
                    Resultat:
                      type: string
                    SmartCom:
                      type: string
                    ActionAmi:
                      type: sting
                    Statut:
                      type: sting
                    NbEncours:
                      type: sting
                    NbExecute:
                      type: sting
          example:
            Liste:
              - NumCpt: 1SAG1100029734
                Resultat: Disable
                SmartCom: OUI
                ActionAmi: "STATUT_P1"
                Statut: Actif
                NbEncours: '0'
                NbExecute: '0'