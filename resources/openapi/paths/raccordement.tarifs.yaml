get:
  summary: 'WS158 : Retrieve the rates for non-periodic connections'
  description: Sends a GET request to WS158 to return rate information retrieved from a SQL query.
  parameters:
    - name: Accept-Language
      in: header
      required: false
      schema:
        type: string
      description: Language for the response, auto-injected by SQL-driven Lambda.
  responses:
    '200':
      description: Operation successful
      headers:
        Access-Control-Allow-Origin:
          schema:
            type: string
      content:
        application/json:
          schema:
            type: array
            items:
              type: object
              properties:
                Id:
                  type: string
                  example: "EB110"
                Label:
                  type: string
                  example: "Racc.simple flux "
                Tarif:
                  type: number
                  example: 123
