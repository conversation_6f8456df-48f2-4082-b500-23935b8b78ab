{"post": {"summary": "Corrige les fautes de frappe d'une adresse", "description": "Permet de trouver la ou les adresses correspondantes pour autocompléter l'adresse entrée", "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Adresse"}, {}]}, "example": {"NumRue": "1", "Rue": "Rue de l'Arbre-Courte-Joie", "Cdpostal": "4000", "Localite": "Liège"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "required": ["Pagination", "Data"], "properties": {"Pagination": {"$ref": "#/components/schemas/Pagination"}, "Data": {"type": "array", "items": {"type": "object", "required": ["MatchingScore", "<PERSON><PERSON><PERSON>", "GPS"], "properties": {"MatchingScore": {"type": "number", "description": "A value used to sort and identify best results"}, "Adresse": {"$ref": "#/components/schemas/Adresse"}, "GPS": {"type": "object", "description": "GPS location about the adresse - warning : if adresses are aggregated these values represent an average", "required": ["ProjectionFacade", "MilieuBatiment"], "properties": {"ProjectionFacade": {"type": "object", "required": ["<PERSON>", "Lat"], "properties": {"Long": {"type": "number"}, "Lat": {"type": "number"}}}, "MilieuBatiment": {"type": "object", "required": ["<PERSON>", "Lat"], "properties": {"Long": {"type": "number"}, "Lat": {"type": "number"}}}}}}}}}}}}}}}}