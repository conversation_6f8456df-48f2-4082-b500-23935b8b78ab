get:
  summary: 'WS125 : Récupérer une liste de pannes'
  description: Retourne par défaut les pannes active sur un an et les pannes cloturée sur un mois.
  parameters:
    - name: DateDebut
      in: query
      required: false
      description: Filtre les pannes sur la de date dy dernier evenement survenu
      schema:
        type:
          - string
          - 'null'
        example: null
        default: null
    - name: DateFin
      in: query
      required: false
      description: Filtre les pannes sur la de date dy dernier evenement survenu
      schema:
        type:
          - string
          - 'null'
        example: null
        default: null
    - name: Rue
      in: query
      required: false
      schema:
        type: string
        example: Rue bizette
        default: null
    - name: Zipcode
      in: query
      required: false
      schema:
        type: string
        example: 4610
        default: null
    - name: Ville
      in: query
      required: false
      schema:
        type: string
        example: Vottem
        default: null
    - name: Numero
      in: query
      required: false
      schema:
        type: string
        example: 528
        default: null
    - name: EP
      in: query
      required: false
      schema:
        type: boolean
        example: false
        default: false
    - name: EnCours
      in: query
      required: false
      description: |
        si absent : pas de filtre
        si true : uniquement panne en cours
        si false : uniquement panne cloturée
      schema:
        type: boolean
        example: false
        default: false
    - name: Lat0
      in: query
      required: false
      schema:
        type: string
        example: null
        default: null
    - name: Lat1
      in: query
      required: false
      schema:
        type: string
        example: null
        default: null
    - name: Long0
      in: query
      required: false
      schema:
        type: string
        example: null
        default: null
    - name: Long1
      in: query
      required: false
      schema:
        type: string
        example: null
        default: null
    - name: Page
      in: query
      required: false
      description: "Pagination: Nombre d'éléments à récupérer par page"
      schema:
        type: string
        example: 0
        default: null
    - name: PageSize
      in: query
      required: false
      description: "Pagination: Page à récupérer"
      schema:
        type: string
        example: 20
        default: null
  responses:
    '200':
      description: 200 OK
      content:
        application/json:
          schema:
            type: object
            properties:
              Srid: 
                type: string
                example: "4326"
              Data:
                type: array
                items:
                  type: object
                  properties:
                    Srid:
                      type: string
                    Data:
                      type: object
                      properties:
                        Id:
                          example: "000411130605"
                          type: string
                        Ep:
                          example: false
                          type: boolean
                        DateCreation:
                          example: "27/03/2022"
                          type: string
                        DateCloture:
                          example: ""
                          type: string
                        SousType:
                          example: null
                          type: string
                        Type:
                          example: null
                          type: string
                        IlluminationType:
                          example: null
                          type: string
                        Adresse:
                          type: object
                          properties:
                            Rue:
                              example: "Rue bizette"
                              type: string
                            Zipcode:
                              example: null
                              type: integer
                            Numero:
                              example: 528
                              type: string
                            Ville:
                              example: "Vottem"
                              type: string
                            Lat:
                              example: null
                              type: number
                            Long:
                              example: null
                              type: number
                        EquipementEp:
                          type: object
                          properties:
                            Id:
                              example: ""
                              type: string
                            Type:
                              example: null
                              type: string
                            SousType:
                              example: null
                              type: string
                            IlluminationType:
                              example: null
                              type: string
                        HistoriqueStatut:
                          type: array
                          items:
                            type: object
                            properties:
                              Id:
                                example: "I0068"
                                type: string
                              Descr:
                                example: "AOUV"
                                type: string
                              ShortDescr:
                                example: "Avis ouvert"
                                type: string
                              DateHeure:
                                example: "2022-03-27 15:07:50"
                                type: string
                              Date:
                                example: "27/03/2022"
                                type: string
                              Heure:
                                example: "15:07:50"
                                type: string
              Pagination: 
                type: object
                properties:
                  PageSize:
                   type: integer
                   example: 1
                  CurrentPage:
                   type: integer
                   example: 0
                  NbPage:
                   type: integer
                   example: 2
