get:
  summary: "WS211 : Check TAD incident and service by postal code"
  description: "This endpoint checks for TAD incidents and service based on a provided postal code."
  operationId: WS211
  parameters:
    - name: PostalCode
      in: query
      required: true
      schema:
        type: string
        description: "The postal code to check incidents and service."
        example: "4000"
  responses:
    '200':
      description: "Successful response with incident and reservation information."
      content:
        application/json:
          schema:
            type: object
            properties:
              ResaElec:
                type: boolean
                description: "Indicates if <PERSON><PERSON> is the electricity GRD on this locality."
              ResaGas:
                type: boolean
                description: "Indicates if <PERSON><PERSON> is the gas GRD on this locality."
              Incident:
                type: boolean
                description: "Indicates if there is an active incident on this locality."