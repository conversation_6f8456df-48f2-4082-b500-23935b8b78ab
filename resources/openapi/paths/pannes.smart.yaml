post:
  summary: "WS183 : permet d'envoyer le statut de panne sur la chaine smart"
  requestBody:
    content:
      application/json:
        schema:
          type: object
          properties:
            Id:
              type: string
              example: "123456"
              required: true
            Actif:
              type: bool
              example: true
              required: true
get:
  summary: "WS184 : donne le statut de panne smart en cours et son ID si actif"
  responses:
    '200':
      description: 200 OK
      content:
        application/json:
          schema:
            type: object
            properties:
              Id:
                type: string
                example: "123456"
                required: false
              Actif:
                type: bool
                example: true
                required: true
              Date:
                type: string
                description: Date à laquelle la panne a débuté. En UTC et au format ISO8601
                example: "2023-11-10T17:27:44.810195"
                required: true