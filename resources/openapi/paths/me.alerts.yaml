post:
  summary: "WS214 : Enregistrer ou mettre à jour les params d'alertes d'un compteur"
  description: |
    Enregistre ou met à jour les params d'alertes d'un compteur pour un EAN / compteur donné.
    Si Daily et Monthly sont à Null alors alerts sont supprimée.
  parameters:
    - name: Ean
      in: query
      required: true
      description: Identifiant EAN de l'utilisateur.
      schema:
        type: string
    - name: Meter
      in: query
      required: true
      description: Numero de compteur.
      schema:
        type: string
  requestBody:
    required: true
    content:
      application/json:
        schema:
          type: object
          properties:
            Daily:
              type: number
              nullable: true
              description: "Seuil d'alerte quotidienne de consommation énergétique (en kWh)."
              example: 0.5
            Monthly:
              type: number
              nullable: true
              description: "Seuil d'alerte mensuelle de consommation énergétique (en kWh)."
              example: 1.0
            Consent:
              type: bool
              nullable: False
              description: "Verification du consentements"
              example: True
  responses:
    200:
      description: Succès, données enregistrées ou mises à jour.
      content:
        application/json:
          schema:
            type: object
            properties:
              Uid:
                type: string
                description: Identifiant utilisateur.
              Ean:
                type: string
                description: Identifiant EAN.
              Meter:
                type: string
                description: Numéro du compteur.
              Daily:
                type:
                  - number
                  - null
                descriptions: Seuil d'alertes journalier.
              Monthly:
                type:
                  - number
                  - null
                descriptions: Seuil d'alertes mensuel.
    403:
      description: Accès interdit si l'EAN ne correspond pas à l'utilisateur.
    404:
      description: Address hash not found in user account.

get:
  summary: "WS215 : Récupérer les informations d'alerts"
  description: Récupère les informations d'alerts pour un compteurs et un EAN donné.
  parameters:
    - name: Ean
      in: query
      required: true
      description: Identifiant EAN de l'utilisateur.
      schema:
        type: string
    - name: Meter
      in: query
      required: true
      description: Numero de compteur.
      schema:
        type: string
  responses:
    200:
      description: Succès, retourne les données de consommation énergétique.
      content:
        application/json:
          schema:
            type: object
            properties:
              Uid:
                type: string
                description: Identifiant utilisateur.
              Ean:
                type: string
                description: Identifiant EAN.
              Meter:
                type: string
                description: Numéro du compteur.
              Daily:
                type: number
                descriptions: seuil d'alerts journalier.
              Monthly:
                type: number
                descriptions: seuil d'alerts mensuel.
    403:
      description: Accès interdit si l'EAN ne correspond pas à l'utilisateur.
    404:
      description: Alerts Data not found
