get:
  summary: 'WS24 : Obtenir la liste des interruptions planifiées à une adresse'
  description: |
    __Les inputs possibles sont :__ 
    * _Aucun : pas de filtre appliqué_ 
    * CdPostal, Localite, Rue 
    * IdLocalite, IdRue 
    * CdPostal 
    * IdLocalite, Rue 
    * IdRadLocalite, IdRadRue 
    * IdRue 
    * Lat0, Long0, Lat1, Long1 : Le filtre sur les coordonnées GPS concerne les PositionsObjRacc de l'output
  parameters:
  - name: Langue
    in: header
    required: false
    schema:
      type: string
      example: FR
      default: FR
  - name: IdRue
    in: query
    required: false
    schema:
      type: integer
  - name: IdRadRue
    in: query
    required: false
    schema:
      type: integer
  - name: IdRadLocalite
    in: query
    required: false
    schema:
      type: integer
  - name: IdLocalite
    in: query
    required: false
    schema:
      type: integer
  - name: CdPostal
    in: query
    required: false
    schema:
      type: integer
      example: 4630
  - name: Localite
    in: query
    required: false
    schema:
      type: string
      example: Soumagne
  - name: Rue
    in: query
    required: false
    schema:
      type: string
      example: Rue du Peuple
  - name: Lat0
    in: query
    description: La plus petite latitude que peut avoir une interruption dans la réponse.
    required: false
    schema:
      type: number
      minimum: -90
      maximum: 90
      example: 50
  - name: Lat1
    in: query
    description: La plus grande latitude que peut avoir une interruption dans la réponse.
    required: false
    schema:
      type: number
      minimum: -90
      maximum: 90
      example: 51
  - name: Long0
    in: query
    description: La plus petite longitude que peut avoir une interruption dans la
      réponse.
    required: false
    schema:
      type: number
      minimum: -180
      maximum: 180
      example: 5
  - name: Long1
    in: query
    description: La plus grande longitude que peut avoir une interruption dans la
      réponse.
    required: false
    schema:
      type: number
      minimum: -180
      maximum: 180
      example: 6
  - name: PageSize
    description: Nombre d'élément max par page
    in: query
    schema:
      type: integer
    example: 20
  - name: Page
    description: Page à récupérer (pagination)
    in: query
    schema:
      type: integer
    example: 0
  responses:
    '200':
      description: 200 OK
      content:
        application/json:
          example:
            Srid: '4326'
            Data:
            - CdPostal: 4540
              DateDebut: 17-12-2020
              HeureDebut: '08:00:00'
              DateFin: 17-12-2020
              HeureFin: '12:00:00'
              Localite: Ampsin
              Rue: Rue Waloppe
              CoupureOuGElectrogene: Coupure
              GPS:
                AvgLat: 50.53755071884215
                AvgLong: 5.290302341810365
                PositionsObjRacc:
                - Lat: 50.53755071884215
                  Long: 50.53755071884215
            - CdPostal: 4540
              DateDebut: 17-12-2020
              HeureDebut: '08:00:00'
              DateFin: 17-12-2020
              HeureFin: '09:00:00'
              Localite: Ampsin
              Rue: Rue Waloppe
              CoupureOuGElectrogene: Coupure
              GPS:
                AvgLat: 50.53755071884215
                AvgLong: 5.290302341810365
                PositionsObjRacc:
                - Lat: 50.53755071884215
                  Long: 50.53755071884215
            - CdPostal: 4540
              DateDebut: 17-12-2020
              HeureDebut: '09:00:00'
              DateFin: 17-12-2020
              HeureFin: '11:00:00'
              Localite: Ampsin
              Rue: Rue Waloppe
              CoupureOuGElectrogene: Coupure
              GPS:
                AvgLat: 50.53755071884215
                AvgLong: 5.290302341810365
                PositionsObjRacc:
                - Lat: 50.53755071884215
                  Long: 50.53755071884215
            - CdPostal: 4540
              DateDebut: 17-12-2020
              HeureDebut: '06:00:00'
              DateFin: 17-12-2020
              HeureFin: '12:00:00'
              Localite: Ampsin
              Rue: Rue Waloppe
              CoupureOuGElectrogene: Coupure
              GPS:
                AvgLat: 50.53755071884215
                AvgLong: 5.290302341810365
                PositionsObjRacc:
                - Lat: 50.53755071884215
                  Long: 50.53755071884215
            - CdPostal: 4540
              DateDebut: 17-12-2020
              HeureDebut: '01:00:00'
              DateFin: 17-12-2020
              HeureFin: '22:00:00'
              Localite: Ampsin
              Rue: Rue Waloppe
              CoupureOuGElectrogene: Coupure
              GPS:
                AvgLat: 50.53755071884215
                AvgLong: 5.290302341810365
                PositionsObjRacc:
                - Lat: 50.53755071884215
                  Long: 50.53755071884215
            - CdPostal: 4540
              DateDebut: 17-12-2020
              HeureDebut: '08:00:00'
              DateFin: 17-12-2020
              HeureFin: '10:00:00'
              Localite: Ampsin
              Rue: Rue Waloppe
              CoupureOuGElectrogene: Coupure
              GPS:
                AvgLat: 50.53755071884215
                AvgLong: 5.290302341810365
                PositionsObjRacc:
                - Lat: 50.53755071884215
                  Long: 50.53755071884215
            - CdPostal: 4540
              DateDebut: 17-12-2020
              HeureDebut: '14:00:00'
              DateFin: 17-12-2020
              HeureFin: '18:00:00'
              Localite: Ampsin
              Rue: Rue Waloppe
              CoupureOuGElectrogene: Coupure
              GPS:
                AvgLat: 50.53755071884215
                AvgLong: 5.290302341810365
                PositionsObjRacc:
                - Lat: 50.53755071884215
                  Long: 50.53755071884215
            Pagination:
              PageSize: 20
              CurrentPage: 0
              NbPage: 1
          schema:
            type: object
            required:
              - Data
              - Srid
            properties:
              Srid:
                type: integer
              Data:
                type: array
                items:
                  type: object
                  required:
                  - Rue
                  - CdPostal
                  - Localite
                  - CoupureOuGElectrogene
                  - DateDebut
                  - HeureDebut
                  - DateFin
                  - HeureFin
                  properties:
                    Id:
                      type: string
                    Rue:
                      type: string
                    CdPostal:
                      type: integer
                    Localite:
                      type: string
                    CoupureOuGElectrogene:
                      type: string
                    DateDebut:
                      type: string
                    HeureDebut:
                      type: string
                    DateFin:
                      type: string
                    HeureFin:
                      type: string
                    GPS:
                      type: object
                      required:
                      - AvgLat
                      - AvgLong
                      - PositionsObjRacc
                      properties:
                        AvgLat:
                          type: number
                        AvgLong:
                          type: number
                        PositionsObjRacc:
                          type: object
                          required:
                          - Lat
                          - Long
                          properties:
                            Lat:
                              type: number
                            Long:
                              type: number
