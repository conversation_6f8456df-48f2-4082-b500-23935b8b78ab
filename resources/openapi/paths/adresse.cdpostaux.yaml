get:
  summary: 'WS106: Obtenir la liste des localité où Resa est actif'
  parameters:   
  - name: grdElec
    description: Filtre pour ne récupérer que les communes où Resa est GRD elec
    in: query
    required: false
    schema:
      type: string
      example: "True"
  - name: grdGaz
    description: Filtre pour ne récupérer que les communes où Resa est GRD gaz
    in: query
    required: false
    schema:
      type: string
      example: "True"
  responses:
    '200':
      description: 200 success
      headers:
        Access-Control-Allow-Origin:
          schema:
            type: string
      content:
        application/json:
          schema:
            type: object
            required:
            - Liste
            properties:
              Liste:
                type: array
                items:
                  type: object
                  properties:
                    Localite:
                      type: string
                    CodePostal:
                      type: string
                    GrdGaz:
                      type: string
                    GrdElec:
                      type: string
                    GrdEp:
                      type: string