get:
  summary: "WS126 : Recherche si une panne active existe sur un EP (par EquiId) ou sur une adresse"
  parameters:
    - name: EquiId
      in: query
      required: false
      schema:
        type: string
        example: 1100226
        default: null
    - name: Rue
      in: query
      required: false
      schema:
        type: string
        example: null
        default: null
    - name: Zipcode
      in: query
      required: false
      schema:
        type: string
        example: null
        default: null
    - name: Ville
      in: query
      required: false
      schema:
        type: string
        example: null
        default: null
    - name: Numero
      in: query
      required: false
      schema:
        type: string
        example: null
        default: null
  responses:
    '200':
      description: 200 OK
      content:
        application/json:
          schema:
            type: object
            properties:
              PanneId:
                type: string
                example: "000411130603"
    '404':
      description: 404 NOT FOUND
      content:
        application/json:
          schema:
            type: object
            properties:
              Error:
                type: number
                example: 404
              Message:
                type: string
                example: "Not Found"