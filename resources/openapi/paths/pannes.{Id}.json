{"get": {"summary": "WS26 : <PERSON><PERSON><PERSON><PERSON> les détails d'une panne, étant donné son identifiant", "description": "L'historique des statut est trié par ordre décroissant", "parameters": [{"name": "Id", "in": "path", "required": true, "schema": {"type": "integer", "example": 411046606}, "description": "Identifiant de la panne"}], "responses": {"200": {"description": "200 success", "headers": {"Access-Control-Allow-Origin": {"schema": {"type": "string"}}}, "content": {"application/json": {"schema": {"title": "Panne d'éclairage public", "description": "Panne d'éclairage public", "type": "object", "required": ["<PERSON><PERSON>", "Data"], "properties": {"Srid": {"type": "string"}, "Data": {"type": "object", "required": ["DateCreation", "Id"], "properties": {"Id": {"type": "string"}, "DateCreation": {"type": "string", "pattern": "^([0-2][0-9]|(3)[0-1])(/)(((0)[0-9])|((1)[0-2]))(/)\\d{4}$"}, "DateCloture": {"type": ["string", "null"]}, "SousType": {"type": "string"}, "Type": {"type": "string"}, "IlluminationType": {"type": "string"}, "HistoriqueStatut": {"type": "array", "items": {"type": "object", "properties": {"Id": {"type": "string"}, "Descr": {"type": "string"}, "ShortDescr": {"type": "string"}, "DateHeure": {"type": "string"}, "Date": {"type": "string"}, "Heure": {"type": "string"}}}}, "Adresse": {"type": "object", "properties": {"Rue": {"type": "string"}, "Zipcode": {"type": "integer"}, "Ville": {"type": "string"}, "Lat": {"type": "number"}, "Long": {"type": "number"}}}, "EquipementEp": {"type": "object", "properties": {"Id": {"type": "string"}, "Type": {"type": "string"}, "SousType": {"type": "string"}, "IlluminationType": {"type": "string"}}}}}}}, "example": {"Srid": "4326", "Data": {"Id": "000411046606", "Ep": true, "DateCreation": "28/11/2017", "DateCloture": "12/12/2017", "SousType": "275", "Type": "C", "IlluminationType": "011", "Adresse": {"Rue": "<PERSON><PERSON><PERSON><PERSON>", "Zipcode": 4020, "Ville": "Liège", "Lat": 50.618065964563506, "Long": 5.595996765761245}, "Equipement": {"Id": "000000000300252689", "Type": "C", "SousType": "275", "IlluminationType": "011"}, "HistoriqueStatut": [{"Id": "I0072", "Descr": "ACLO", "ShortDescr": "<PERSON><PERSON>", "DateHeure": "2017-12-12 11:36:49", "Date": "12/12/2017", "Heure": "11:36:49"}, {"Id": "I0071", "Descr": "I0071", "ShortDescr": "I0071", "DateHeure": "2017-12-04 07:10:24", "Date": "04/12/2017", "Heure": "07:10:24"}, {"Id": "I0070", "Descr": "AENC", "ShortDescr": "Avis en cours", "DateHeure": "2017-11-29 10:06:31", "Date": "29/11/2017", "Heure": "10:06:31"}, {"Id": "I0068", "Descr": "I0068", "ShortDescr": "I0068", "DateHeure": "2017-11-28 20:28:54", "Date": "28/11/2017", "Heure": "20:28:54"}]}}}}}, "400": {"description": "400 Bad request error", "headers": {"Access-Control-Allow-Origin": {"schema": {"type": "string"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MyResaAPI_Error"}, "example": {"Error": 400, "Message": "Le paramètre id est requis"}}}}, "404": {"description": "404 Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MyResaAPI_Error"}}}}}}}