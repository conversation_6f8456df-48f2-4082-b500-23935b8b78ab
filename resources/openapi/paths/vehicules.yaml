get:
  summary: 'Récupère les informations sur les véhicules électriques et hybrides.'
  description: |
    Retourne un tableau d'objets JSON contenant des informations sur divers véhicules électriques et hybrides.
  responses:
    '200':
      description: Opération réussie
      content:
        application/json:
          schema:
            type: array
            items:
              type: object
              properties:
                Marque:
                  type: string
                  example: "Tesla"
                Modele:
                  type: string
                  example: "Model 3"
                Version:
                  type: string
                  example: "Standard Range Plus"
                  nullable: true
                CapaciteBatterie:
                  type: number
                  example: 55.5
                Type:
                  type: string
                  example: "Électrique"
                Autonomie:
                  type: number
                  example: 400
                Consommation:
                  type: number
                  example: 20
                  nullable: true
                PuissanceChargeMax:
                  type: number
                  example: 7.4
                  nullable: true
    '400':
      description: BAD REQUEST
      content:
        application/json:
          schema:
            type: object
            properties:
              Error:
                type: integer
                example: 400
              Message:
                type: string
                example: "Paramètres invalides ou manquants"