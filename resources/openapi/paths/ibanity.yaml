post:
  summary: 'WS130: Passthrough vers API Ibanity'
  parameters:
    - name: Langue
      in: header
      required: false
      schema:
        type: string
        example: FR
        default: FR
  requestBody:
      content:
        application/json:
          schema:
            type: object
            required:
              - Endpoint
              - Method
            properties:
              Endpoint:
                type: string
                description: Endpoint à joindre
                example: "/isabel-connect/oauth2/token"
              Method:
                type: string
                description: Methode à utiliser
                example: POST
              Body:
                type: object
                description: Body à envoyer vers le endpoint
                example:
                  grant_type: authorization_code
                  code: valid_authorization_code
                  redirect_uri: https://fake-tpp.com
              Headers:
                type: object
                description: Headers à envoyer vers le endpoint
                example:
                  Content-Type: application/x-www-form-urlencoded
                  Accept: application/vnd.api+json;version=2
                  Authorization: Basic NTQyY2JjN2EtNzk2OC00MjZjLTgzMGYtZjlkYzFjM2MzNThhOnZhbGlkX3NlY3JldA==
                  Ibanity-Idempotency-Key: a8568fde-406c-4042-85b8-25dc10d498d3
              Security:
                type: object
                description: Element de sécurité pour les requêtes nécessitant un certificat client
                properties:
                  Cert:
                    type: string
                    example: -----BEGIN CERTIFICATE-----\nMIIFVzCCAz+gAwIBAgIJAM...iQorD0Py3doKCsZ\n-----END CERTIFICATE-----
                  Key:
                    type: string
                    example: -----BEGIN PRIVATE KEY-----\nMIIJQgIBADANBgkqhkiG9w...A6KJanyzqXj5Q==\n-----END PRIVATE KEY-----
                  Id:
                    type: string
                    description: Id du certificat à utiliser pour les communication cryptée
                    example: CERT_ID
              