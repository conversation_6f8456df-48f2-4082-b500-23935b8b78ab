post:
  summary: "WS157 : Ajoute un nouveau véhicule à l'utilisateur."
  security:
    - tokenAuthorizer: [ ]
  description: |
    Prend un objet JSON représentant un véhicule et l'ajoute à la table DynamoDB.
  requestBody:
    content:
      application/json:
        schema:
          type: object
          properties:
            Ean:
              type: string
              example: "9781234567897"
            Compteur:
              type: string
              example: "001122334455"
            Borne:
              type: object
              properties:
                Modele:
                  type: string
                  example: "ModèleBorne1"
                ModifPuissance:
                  type: boolean
                  example: true
                PuissanceDepart:
                  type: number
                  format: float
                  example: 50.0
                PuissanceCible:
                  type: number
                  format: float
                  example: 120.0
                ModifTypeRaccordement:
                  type: boolean
                  example: false
                TypeRaccordementDepart:
                  type: string
                  example: "Type1"
                TypeRaccordementCible:
                  type: string
                  example: "Type2"
            Vehicule:
              type: object
              properties:
                Marque:
                  type: string
                  example: "MarqueVehicule"
                Modele:
                  type: string
                  example: "ModèleVehicule"
                Version:
                  type: string
                  example: "Version2024"
                CapaciteBatterie:
                  type: number
                  format: float
                  example: 75.0
                Type:
                  type: string
                  example: "Electrique"
                Autonomie:
                  type: number
                  format: float
                  example: 300.0
                Consommation:
                  type: number
                  format: float
                  example: 15.0
                PuissanceChargeMax:
                  type: number
                  format: float
                  example: 150.0
                Capacite:
                  type: integer
                  example: 5
                Utilisation:
                  type: object
                  properties:
                    DistanceType:
                      type: number
                      format: float
                      example: 20.0
                    PeriodeRecharge:
                      type: string
                      example: "Hebdomadaire"
                    BranchementMoyen:
                      type: number
                      format: float
                      example: 22.0
                    ParkingElecPro:
                      type: boolean
                      example: true
                    DistancePro:
                      type: number
                      format: float
                      example: 10.0
          required:
            - Ean
            - Compteur
            - Borne
            - Vehicule
  responses:
    '201':
      description: Création réussie
      content:
        application/json:
          schema:
            type: object
            properties:
              Vehicule:
                type: object
                properties:
                  Marque:
                    type: string
                  Modele:
                    type: string
                  Version:
                    type: string
                  CapaciteBatterie:
                    type: number
                    format: float
                  Type:
                    type: string
                  Autonomie:
                    type: number
                    format: float
                  Consommation:
                    type: number
                    format: float
                  PuissanceChargeMax:
                    type: number
                    format: float
                  Capacite:
                    type: integer
                  Utilisation:
                    type: object
                    properties:
                      DistanceType:
                        type: number
                        format: float
                      PeriodeRecharge:
                        type: string
                      BranchementMoyen:
                        type: number
                        format: float
                      ParkingElecPro:
                        type: boolean
                      DistancePro:
                        type: number
                        format: float
              Ean:
                type: string
              Compteur:
                type: string
              Borne:
                type: object
                properties:
                  Modele:
                    type: string
                  ModifPuissance:
                    type: boolean
                  PuissanceDepart:
                    type: number
                    format: float
                  PuissanceCible:
                    type: number
                    format: float
                  ModifTypeRaccordement:
                    type: boolean
                  TypeRaccordementDepart:
                    type: string
                  TypeRaccordementCible:
                    type: string
                  Cout:
                    type: number
                    format: float
                    nullable: true
              UID:
                type: string
              UUID:
                type: string
              Date:
                type: string
    
    '400':
      description: BAD REQUEST
      content:
        application/json:
          schema:
            type: object
            properties:
              Error:
                type: integer
                example: 400
              Message:
                type: string
                example: "Données du véhicule invalides ou manquantes"
    '500':
      description: INTERNAL SERVER ERROR
      content:
        application/json:
          schema:
            type: object
            properties:
              Error:
                type: integer
                example: 500
              Message:
                type: string
                example: "Erreur interne du serveur"
get:
  summary: "WS156 : Récupérer les véhicules de l'utilisateur."
  security:
    - tokenAuthorizer: [ ]
  description: |
    Prend un UID en tant que paramètre et retourne les objets JSON représentant les véhicules associés à cet UID dans la table DynamoDB.
  parameters:
    - name: UID
      in: query
      description: L'identifiant unique de l'utilisateur
      required: true
      schema:
        type: string
        example: "unique-uid"
  responses:
    '200':
      description: Opération réussie
      content:
        application/json:
          schema:
            type: array
            items:
              type: object
              properties:
                Vehicule:
                  type: object
                  properties:
                    Autonomie:
                      type: number
                      format: float
                      example: 300.0
                    Marque:
                      type: string
                      example: "MarqueVehicule"
                    Version:
                      type: string
                      example: "Version2024"
                    Utilisation:
                      type: object
                      properties:
                        DistancePro:
                          type: number
                          format: float
                          example: 10.0
                        BranchementMoyen:
                          type: number
                          format: float
                          example: 22.0
                        DistanceType:
                          type: number
                          format: float
                          example: 20.0
                        ParkingElecPro:
                          type: boolean
                          example: true
                        PeriodeRecharge:
                          type: string
                          example: "Hebdomadaire"
                    Capacite:
                      type: number
                      format: float
                      example: 5.0
                    PuissanceChargeMax:
                      type: number
                      format: float
                      example: 150.0
                    Modele:
                      type: string
                      example: "ModèleVehicule"
                    Consommation:
                      type: number
                      format: float
                      example: 15.0
                    CapaciteBatterie:
                      type: number
                      format: float
                      example: 75.0
                    VehicleType:
                      type: string
                      example: "Electrique"
                Ean:
                  type: string
                  example: "9781234567897"
                Compteur:
                  type: string
                  example: "001122334455"
                Borne:
                  type: object
                  properties:
                    ModifPuissance:
                      type: boolean
                      example: true
                    TypeRaccordementCible:
                      type: string
                      example: "Type2"
                    ModifTypeRaccordement:
                      type: boolean
                      example: false
                    PuissanceCible:
                      type: number
                      format: float
                      example: 120.0
                    TypeRaccordementDepart:
                      type: string
                      example: "Type1"
                    Cout:
                      type: number
                      format: float
                      nullable: true
                    Modele:
                      type: string
                      example: "ModèleBorne1"
                    PuissanceDepart:
                      type: number
                      format: float
                      example: 50.0
                UID:
                  type: string
                  example: "Acc.Test.Dae9z"
                UUID:
                  type: string
                  example: "fcdc572d-1d99-4a2f-92f9-646d7d636d9c"
                Date:
                  type: string
                  example: "04-01-2024,08:20"
    
    '400':
      description: BAD REQUEST
      content:
        application/json:
          schema:
            type: object
            properties:
              Error:
                type: integer
                example: 400
              Message:
                type: string
                example: "NO_VEHICLES"