get:
  summary: "WS209 : WS to get cpt num based on ean / PostCode & street"
  parameters:
    - name: Ean
      in: path
      required: true
      schema:
        type: string
      example: '541460900002617502'
    - name: Street
      in: query
      required: true
      schema:
        type: string
      example: '<PERSON>emplaire'
    - name: PostCode
      in: query
      required: true
      schema:
        type: string
      example: '4020'
  responses:
    '200':
      description: 200 success
      content:
        application/json:
          schema:
            type: array
            items:
              type: object
              properties:
                Ean:
                  type: string
                MeterNumber:
                  type: string
            example:
              - Ean: '541460900000214871'
                MeterNumber: '000000000006138630'
              - Ean: '541460900000214871'
                MeterNumber: '000000000027367618'