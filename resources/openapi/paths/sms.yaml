get:
  security:
    - basicAuthorizer: [ ]
  summary: "WS69.1 : <PERSON><PERSON><PERSON><PERSON> le statut d'un SMS"
  parameters:
    - name: Sandbox
      in: query
      required: true
      description: Desactivate sandbox mode if `false`
      schema:
        type: boolean
        example: 'true'
    - name: MessageId
      in: query
      required: true
      description: The id of the message to retrieve
      schema:
        type: string
        example: 0121DBB7-FA90-4FCC-9FB3-C24D5307F86F
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            type: object
            required:
              - MessageId
              - StatusCode
              - StatusDescription
              - TimeScheduled
              - TimeSubmitted
              - DeliveryTime
              - ResultCode
              - ResultDescription
              - From
              - To
              - Message
              - MessageEncoding
              - NumberOfParts
              - NumberOfChars
            properties:
              MessageId:
                type: string
              Reference:
                type: 'null'
              StatusCode:
                type: string
              StatusDescription:
                type: string
              TimeScheduled:
                type: string
                format: date-time
              TimeSubmitted:
                type: string
                format: date-time
              DeliveryTime:
                type: string
                format: date-time
              Country:
                type: 'null'
              ResultCode:
                type: integer
              ResultDescription:
                type: string
              From:
                type: string
              To:
                type: string
              Message:
                type: string
              MessageEncoding:
                type: string
              NumberOfParts:
                type: integer
              NumberOfChars:
                type: integer
          example:
            MessageId: 0121dbb7-fa90-4fcc-9fb3-c24d5307f86f
            Reference:
            StatusCode: '200'
            StatusDescription: Delivered
            TimeScheduled: 2020/04/23 10:43:05
            TimeSubmitted: 2020/04/23 10:43:05
            DeliveryTime: 2020/04/23 10:43:05
            Country:
            ResultCode: 0
            ResultDescription: Success
            From: '8810'
            To: '32489311126'
            Message: Send messages in text
            MessageEncoding: TEXT
            NumberOfParts: 1
            NumberOfChars: 21
post:
  security:
    - basicAuthorizer: [ ]
  summary: 'WS69 : Envoyer un SMS'
  externalDocs:
    url: https://developers.ringring.be/#operation/MessageApiSmsRequest_Post
  parameters:
    - name: Sandbox
      in: query
      required: true
      description: Send the message only if `false`
      schema:
        type: boolean
        example: 'true'
  requestBody:
    required: true
    content:
      application/json:
        schema:
          required:
            - to
            - message
          properties:
            to:
              type: string
            message:
              type: string
        example:
          to: "+32489123456"
          message: This is a test
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            type: object
            required:
              - MessageCount
              - Messages
              - ResultCode
              - ResultDescription
              - MessageEncoding
              - NumberOfParts
              - NumberOfChars
            properties:
              MessageCount:
                type: integer
              Reference:
                type: 'null'
              Messages:
                type: array
                items:
                  type: object
                  required:
                    - MessageId
                    - To
                    - ResultCode
                    - ResultDescription
                  properties:
                    MessageId:
                      type: string
                    To:
                      type: string
                    ResultCode:
                      type: integer
                    ResultDescription:
                      type: string
              ResultCode:
                type: integer
              ResultDescription:
                type: string
              NumberOfParts:
                type: integer
              NumberOfChars:
                type: integer
              MessageEncoding:
                type: string
          example:
            MessageCount: 1
            Reference:
            Messages:
              - MessageId: 520495CD-4E3D-44F9-80CB-27A147F07BE1
                To: '32489311126'
                ResultCode: 0
                ResultDescription: Success
            ResultCode: 0
            ResultDescription: Success
            NumberOfParts: 1
            NumberOfChars: 21
            MessageEncoding: TEXT
    '400 LockedPhoneNumber':
      description: "BAD REQUEST, LockedPhoneNumber."
      content:
        application/json:
          schema:
            type: object
            properties:
              Error:
                type: integer
                example: 400
              Message:
                type: string
                example: "The provided phone number is in the locked phone list"
              Error_code:
                type: string
                example: LockedPhoneNumber