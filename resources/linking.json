{"/adresse": {"GET": {"type": "sqlDriven", "secret": "{HANA_SECRET}", "controller": "ws17", "sql_template": "sqlStatements/ws17.sql", "return": {"type": "list", "container": {"Liste": "{LAMBDA_RESPONSE}"}, "pagination": false}, "params": {"Cdpostal": {"source": "queryStringParameters", "name": "Cdpostal", "validation": {"type": "int"}}}}}, "/adresse/validate": {"POST": {"type": "passThrough", "URL": "https://{SAP}/RESTAdapter/OS_APIRESA_ISU_WS18", "transform": [{"function": "auth:auth", "type": "request", "params": {"secret": "{BASICAUTH}"}}, {"function": "paramMapping:mapParameters", "type": "request", "params": {"input": {"source": "headers", "name": "Accept-Language", "default": "FR"}, "output": {"source": "params", "name": "<PERSON><PERSON>", "case": "upper"}}}, {"function": "errorHandler:resa<PERSON><PERSON>r", "type": "response"}, {"function": "capitalizeJson:capitalizeJson", "type": "response"}]}}, "/adresse/match": {"POST": {"type": "sqlDriven", "secret": "{RESA_GEO_SECRET}", "sql_template": "sqlStatements/adresse_validation.sql", "return": {"type": "list", "container": {"Data": "{LAMBDA_RESPONSE}"}}, "params": {"Rue": {"source": "body", "name": "Rue", "default": null}, "Localite": {"source": "body", "name": "Localite", "default": null}, "Cdpostal": {"source": "body", "name": "Cdpostal", "default": null}, "NumRue": {"source": "body", "name": "NumRue", "default": null}}}}, "/adresse/distCourte": {"POST": {"type": "sqlDriven", "controller": "DistCourte", "secret": "{RESA_GEO_SECRET}", "sql_template": {"get_dist_courte": "sqlStatements/adresse_distCourte.sql", "ws71_enrich_adress": "sqlStatements/ws71_enrich_adress.sql"}, "return": {"type": "list", "container": {"Data": "{LAMBDA_RESPONSE}"}}, "params": {"Source": {"source": "body", "name": "Source"}, "Destinations": {"source": "body", "name": "Destinations"}}}}, "/adresse/ean": {"GET": {"type": "sqlDriven", "controller": "ws19", "secret": "{HANA_SECRET}", "sql_template": {"get_installation": "sqlStatements/ws29_get_energy_type.sql", "get_adress_info": "sqlStatements/ws29_get_adress_info.sql"}, "return": {"type": "object"}, "params": {"Ean": {"source": "queryStringParameters", "name": "<PERSON><PERSON>", "validation": {"type": "int"}}}}}, "/adresse/services": {"GET": {"type": "sqlDriven", "controller": "Controller", "secret": "{HANA_SECRET}", "sql_template": "sqlStatements/ws20.sql", "params": {"Localite": {"source": "queryStringParameters", "name": "Localite", "default": null}, "Rue": {"source": "queryStringParameters", "name": "Rue", "default": null}, "IdCommune": {"source": "queryStringParameters", "name": "IdCommune", "default": null}, "Numero": {"source": "queryStringParameters", "name": "Numero", "default": null}, "Cdpostal": {"source": "queryStringParameters", "name": "Cdpostal", "default": null}}, "return": {"type": "object"}}}, "/adresse/cdpostaux": {"GET": {"id": "WS106", "type": "sqlDriven", "secret": "{HANA_SECRET}", "controller": "NoPaginationPascalCaseController", "sql_template": "sqlStatements/active_resa_zip_code.sql", "params": {"grdElec": {"source": "queryStringParameters", "name": "grdElec", "default": null}, "grdGaz": {"source": "queryStringParameters", "name": "grdGaz", "default": null}}, "return": {"type": "list", "container": {"Liste": "{LAMBDA_RESPONSE}"}}}}, "/adresse/pays": {"GET": {"type": "sqlDriven", "controller": "NoPagination", "secret": "{HANA_SECRET}", "sql_template": "sqlStatements/get_country_list.sql", "params": {}, "return": {"type": "list", "container": {"Liste": "{LAMBDA_RESPONSE}"}}}}, "/bp/{BP}/myre": {"GET": {"type": "sqlDriven", "secret": "{HANA_SECRET}", "sql_template": "sqlStatements/id_HGZ_to_id_MyRE.sql", "params": {"BP": {"name": "BP", "source": "pathParameters"}}, "return": {"type": "object"}}}, "/delestage/pour_adresse": {"GET": {"type": "sqlDriven", "secret": "{HANA_SECRET}", "controller": "ws43", "sql_template": "sqlStatements/ws43_get_message_and_tranche.sql", "return": {"type": "object", "container": {"Liste": "{LAMBDA_RESPONSE}"}}, "params": {"Cdpostal": {"source": "queryStringParameters", "name": "Cdpostal", "validation": {"type": "int"}}, "Localite": {"source": "queryStringParameters", "name": "Localite"}, "Rue": {"source": "queryStringParameters", "name": "Rue"}}}}, "/ean": {"GET": {"type": "sqlDriven", "secret": "{HANA_SECRET}", "controller": "ws27", "sql_template": {"get_conso_from_geo": "sqlStatements/ws27_get_conso_from_geo.sql", "get_ean_from_conso": "sqlStatements/ws27_get_ean_from_conso.sql", "get_conso_from_ean": "sqlStatements/ws29_get_energy_type.sql", "get_business_info": "sqlStatements/ws29_get_business_info.sql", "get_adress_info": "sqlStatements/ws29_get_adress_info.sql", "get_hgz_info": "sqlStatements/ws29_get_hgz_info.sql", "get_counters_from_installation": "sqlStatements/ws29_get_counter_info.sql", "get_periodes_passage": "sqlStatements/ws33_get_periodes_passage.sql", "get_periodes_encodage": "sqlStatements/ws33_get_periodes_encodage.sql", "get_installation_property": "sqlStatements/all_ws_get_installation_property.sql"}, "return": {"type": "object"}, "params": {"Ean": {"source": "queryStringParameters", "name": "<PERSON><PERSON>", "validation": {"type": "int"}, "default": null}, "Cdpostal": {"source": "queryStringParameters", "name": "Cdpostal", "validation": {"type": "int"}, "default": null}, "Localite": {"source": "queryStringParameters", "name": "Localite", "default": null}, "Rue": {"source": "queryStringParameters", "name": "Rue", "default": null}, "NumRue": {"source": "queryStringParameters", "name": "NumRue", "default": null}, "NumCpt": {"source": "queryStringParameters", "name": "NumCpt", "default": null}}}}, "/ean/{Ean}": {"GET": {"type": "sqlDriven", "secret": "{HANA_SECRET}", "controller": "ws29", "sql_template": {"get_energy_type": "sqlStatements/ws29_get_energy_type.sql", "get_energy_measures": "sqlStatements/ws29_get_energy_measures.sql", "get_installation_properties": "sqlStatements/ws29_get_installation_properties.sql", "get_business_info": "sqlStatements/ws29_get_business_info.sql", "get_hgz_info": "sqlStatements/ws29_get_hgz_info.sql", "get_counter_info": "sqlStatements/ws29_get_counter_info.sql", "get_adress_info": "sqlStatements/ws29_get_adress_info.sql", "get_liaison_info_E": "sqlStatements/ws29_get_liaison_info_E.sql", "get_liaison_info_G": "sqlStatements/ws29_get_liaison_info_G.sql", "get_protection_info": "sqlStatements/ws29_get_protection_info.sql", "get_label_horaire": "sqlStatements/ws29_get_label_horaire.sql", "get_direct_characteristic": "sqlStatements/ws29_get_direct_characteristic.sql", "get_characteristic_by_names_list": "sqlStatements/ws29_get_characteristic_by_names_list.sql", "get_characteristic_by_name": "sqlStatements/ws29_get_characteristic_by_name.sql"}, "return": {"type": "object"}, "params": {"Ean": {"source": "pathParameters", "name": "<PERSON><PERSON>", "validation": {"type": "int"}}, "NumCpt": {"source": "queryStringParameters", "name": "NumCpt", "default": null}}}}, "/ean/{Ean}/grd": {"GET": {"type": "sqlDriven", "secret": "{HANA_SECRET}", "controller": "Controller", "sql_template": "sqlStatements/WS196_is_EAN_owned_by_resa.sql", "return": {"type": "object"}, "params": {"Ean": {"source": "pathParameters", "name": "<PERSON><PERSON>"}}}}, "/ean/{Ean}/history": {"GET": {"type": "sqlDriven", "secret": "{HANA_SECRET}", "controller": "ws219_histo_index", "sql_template": "sqlStatements/ws219_histo_index.sql", "return": {"type": "object"}, "params": {"Ean": {"source": "pathParameters", "name": "<PERSON><PERSON>"}}}}, "/ean/{Ean}/lockStatus": {"GET": {"id": "WS191", "type": "getDynamoDB", "function": "check_ean_status:get_ean_lock_status"}}, "/ean/{Ean}/meter": {"GET": {"id": "WS209", "type": "sqlDriven", "secret": "{HANA_SECRET}", "controller": "NoPagination", "sql_template": "sqlStatements/ws209_get_cpt_based_on_ean_cp_street.sql", "return": {"type": "object"}, "params": {"Ean": {"source": "pathParameters", "name": "<PERSON><PERSON>", "default": null}, "PostCode": {"source": "queryStringParameters", "name": "PostCode", "default": null}, "Street": {"source": "queryStringParameters", "name": "Street", "default": null}}}}, "/me/alerts": {"POST": {"id": "WS214", "type": "getDynamoDB", "function": "smartConso_alerts:ws214"}, "GET": {"id": "WS215", "type": "getDynamoDB", "function": "smartConso_alerts:ws215"}}, "/me/alerts/historic": {"GET": {"id": "WS218", "type": "getDynamoDB", "function": "historic_alerts:ws218"}}, "/ean/meter": {"GET": {"id": "WS200", "type": "sqlDriven", "secret": "{HANA_SECRET}", "controller": "ws200", "sql_template": "sqlStatements/ws200_ean_meter_info.sql", "params": {"Ean": {"source": "queryStringParameters", "name": "<PERSON><PERSON>", "default": null}, "MeterId": {"source": "queryStringParameters", "name": "MeterId", "default": null}}}}, "/ean/recherche": {"GET": {"type": "sqlDriven", "secret": "{HANA_SECRET}", "controller": "ws117", "authorizer": "Basic", "authorizerSecrets": ["MyResaAPI/AWS/BasicAuthPassword"], "sql_template": "sqlStatements/ws117.sql", "return": {"container": {"Liste": "{LAMBDA_RESPONSE}"}}, "params": {"Lastname": {"source": "queryStringParameters", "name": "Lastname", "default": null}, "Firstname": {"source": "queryStringParameters", "name": "Firstname", "default": null}, "Phone": {"source": "queryStringParameters", "name": "Phone", "default": null}, "PhoneFixe": {"source": "queryStringParameters", "name": "PhoneFixe", "default": null}, "Email": {"source": "queryStringParameters", "name": "Email", "default": null}, "ContactEmail": {"source": "queryStringParameters", "name": "ContactEmail", "default": null}}}}, "/index": {"GET": {"type": "passThrough", "URL": "https://{SAP}/RESTAdapter/OS_APIRESA_ISU_WS31", "transform": [{"function": "auth:auth", "type": "request", "params": {"secret": "{BASICAUTH}"}}, {"function": "paramMapping:mapParameters", "type": "request", "params": {"input": {"source": "headers", "name": "Accept-Language", "default": "FR"}, "output": {"source": "params", "name": "<PERSON><PERSON>", "case": "upper"}}}, {"function": "errorHandler:resa<PERSON><PERSON>r", "type": "response"}, {"function": "capitalizeJson:capitalizeJson", "type": "response"}]}, "POST": {"type": "passThrough", "URL": "https://{SAP}/RESTAdapter/OS_APIRESA_ISU_WS32", "transform": [{"function": "auth:auth", "type": "request", "params": {"secret": "{BASICAUTH}"}}, {"function": "body:notEmpty", "type": "request"}, {"function": "paramMapping:mapParameters", "type": "request", "params": {"input": {"source": "headers", "name": "Accept-Language", "default": "FR"}, "output": {"source": "params", "name": "<PERSON><PERSON>", "case": "upper"}}}, {"function": "errorHandler:resa<PERSON><PERSON>r", "type": "response"}, {"function": "capitalizeJson:capitalizeJson", "type": "response"}]}}, "/index/smart": {"GET": {"id": "WS205", "type": "sqlDriven", "secret": "{REDSHIFT_SECRET}", "controller": "ws205", "sql_template": {"monthly": "sqlStatements/ws205_get_monthly_conso.sql", "daily": "sqlStatements/ws205_get_daily_conso.sql"}, "params": {"Type": {"source": "queryStringParameters", "name": "Type", "default": null}, "StartDate": {"source": "queryStringParameters", "name": "StartDate", "default": null}, "EndDate": {"source": "queryStringParameters", "name": "EndDate", "default": null}, "Ean": {"source": "queryStringParameters", "name": "<PERSON><PERSON>", "default": null}, "MeterId": {"source": "queryStringParameters", "name": "MeterId", "default": null}}}}, "/index/smart/export": {"GET": {"id": "WS221", "type": "simpleCompute", "function": "export_ean_data:start_export"}}, "/index/preparer_encodage": {"GET": {"type": "getDynamoDB", "function": "encodage_index:preparer_encodage"}}, "/index/bulk": {"POST": {"type": "passThrough", "URL": "https://{SAP}/RESTAdapter/OA_APIRESA_ISU_WS32", "transform": [{"function": "auth:auth", "type": "request", "params": {"secret": "{BASICAUTH}"}}, {"function": "body:notEmpty", "type": "request"}, {"function": "paramMapping:mapParameters", "type": "request", "params": {"input": {"source": "headers", "name": "Accept-Language", "default": "FR"}, "output": {"source": "params", "name": "<PERSON><PERSON>", "case": "upper"}}}, {"function": "errorHandler:resa<PERSON><PERSON>r", "type": "response"}, {"function": "capitalizeJson:capitalizeJson", "type": "response"}]}}, "/index/passage": {"GET": {"type": "sqlDriven", "secret": "{HANA_SECRET}", "controller": "ws33", "sql_template": {"get_installation_from_ean": "sqlStatements/ws33_get_installation_from_ean.sql", "get_counters_from_installation": "sqlStatements/ws29_get_counter_info.sql", "get_periodes_passage": "sqlStatements/ws33_get_periodes_passage.sql", "get_periodes_encodage": "sqlStatements/ws33_get_periodes_encodage.sql", "get_installation_property": "sqlStatements/all_ws_get_installation_property.sql", "get_ean_from_counter": "sqlStatements/ws28_get_ean_from_counter.sql"}, "return": {"type": "object"}, "params": {"Ean": {"source": "queryStringParameters", "name": "<PERSON><PERSON>", "default": null, "validation": {"type": "int"}}, "NumCompteur": {"source": "queryStringParameters", "name": "NumCompteur", "default": null}}}}, "/index/historique": {"GET": {"type": "sqlDriven", "secret": "{HANA_SECRET}", "controller": "ws34", "sql_template": {"get_historique_conso_from_ean": "sqlStatements/ws34_get_historique_conso_from_ean.sql", "get_historique_conso_from_ean_noauth": "sqlStatements/ws34_get_historique_conso_from_ean_noauth.sql", "get_ean_from_counter": "sqlStatements/ws28_get_ean_from_counter.sql"}, "return": {"type": "object"}, "params": {"Ean": {"source": "queryStringParameters", "name": "<PERSON><PERSON>", "default": null, "validation": {"type": "int"}}, "NumCompteur": {"source": "queryStringParameters", "name": "NumCompteur", "default": null}}}}, "/jobs": {"GET": {"type": "passThrough", "URL": "https://resa1.recruitee.com/api/offers/", "transform": [{"function": "headers:headers", "type": "request", "params": {"headers": {"Host": "resa1.recruitee.com"}}}, {"function": "headers:remove_apigw_headers", "type": "request"}, {"function": "capitalizeJson:capitalizeJson", "type": "response"}]}}, "/facturation/attestations": {"GET": {"type": "passThrough", "URL": "http://{HAUGAZEL}/Haugazel/rest/services/{HAUGAZEL_TECTEO}/api/customers/v1/{{Id}}/", "transform": [{"function": "haugazel:injectPartenaireId", "type": "request", "params": {"path": "Id", "transform": [{"function": "xmlToJson:xmlToJson", "type": "response", "params": {}}, {"function": "capitalizeJson:capitalizeJson", "type": "response"}]}}, {"function": "headers:headers", "type": "request", "params": {"headers": {"Host": "sdvma-gazel01.resa.intra", "Accept": "application/xml"}}}, {"function": "auth:auth", "type": "request", "params": {"secret": "MyResaAPI/Haugazel"}}, {"function": "capitalizeJson:capitalizeJson", "type": "response"}]}}, "/facturation/factures": {"GET": {"type": "passThrough", "URL": "https://{SAP}/RESTAdapter/OS_APIRESA_FICA_WS46", "transform": [{"function": "auth:validate_user_ean", "type": "request"}, {"function": "ean:get_cc_hgz", "params": {"set": "ContractAccount"}, "type": "request"}, {"function": "auth:auth", "type": "request", "params": {"secret": "{BASICAUTH}"}}, {"function": "paramMapping:mapParameters", "type": "request", "params": {"input": {"source": "headers", "name": "Accept-Language", "default": "FR"}, "output": {"source": "params", "name": "<PERSON><PERSON>", "case": "upper"}}}, {"function": "errorHandler:resa<PERSON><PERSON>r", "type": "response"}, {"function": "text:remove<PERSON><PERSON>", "type": "response", "params": {"key": "Solde", "char": "-"}}]}}, "/facturation/factures/hgz": {"GET": {"type": "passThrough", "URL": "http://{HAUGAZEL}/Haugazel/rest/services/{HAUGAZEL_TECTEO}/api/contracts/v1/{{Id}}/customer", "transform": [{"function": "haugazel:injectNumeroContrat", "type": "request", "params": {"path": "Id", "transform": [{"function": "xmlToJson:xmlToJson", "type": "response", "params": {}}, {"function": "capitalizeJson:capitalizeJson", "type": "response"}]}}, {"function": "headers:headers", "type": "request", "params": {"headers": {"Host": "sdvma-gazel01.resa.intra", "Accept": "application/xml"}}}, {"function": "auth:auth", "type": "request", "params": {"secret": "MyResaAPI/Haugazel"}}, {"function": "capitalizeJson:capitalizeJson", "type": "response"}]}}, "/facturation/contrats": {"GET": {"type": "passThrough", "URL": "http://{HAUGAZEL}/Haugazel/rest/services/{HAUGAZEL_TECTEO}/api/customers/v1/{{Id}}/contracts", "transform": [{"function": "haugazel:injectPartenaireId", "type": "request", "params": {"path": "Id", "transform": [{"function": "xmlToJson:xmlToJson", "type": "response", "params": {}}, {"function": "capitalizeJson:capitalizeJson", "type": "response"}]}}, {"function": "headers:headers", "type": "request", "params": {"headers": {"Host": "sdvma-gazel01.resa.intra", "Accept": "application/xml"}}}, {"function": "auth:auth", "type": "request", "params": {"secret": "MyResaAPI/Haugazel"}}, {"function": "capitalizeJson:capitalizeJson", "type": "response"}]}}, "/facturation/contrats/{Id}": {"GET": {"type": "passThrough", "URL": "http://{HAUGAZEL}/Haugazel/rest/services/{HAUGAZEL_TECTEO}/api/contracts/v1/{{Id}}", "transform": [{"function": "headers:headers", "type": "request", "params": {"headers": {"Host": "sdvma-gazel01.resa.intra", "Accept": "application/xml"}}}, {"function": "auth:auth", "type": "request", "params": {"secret": "MyResaAPI/Haugazel"}}, {"function": "xmlToJson:xmlToJson", "type": "response", "params": {"removeKeys": ["@xmlns", "@nil"]}}, {"function": "errorHandler:ha<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "response"}, {"function": "capitalizeJson:capitalizeJson", "type": "response"}, {"function": "ws55:validate_user_ean", "type": "response"}]}}, "/facturation/devis": {"GET": {"type": "passThrough", "URL": "https://{SAP}/RESTAdapter/OS_APIRESA_FICA_WS56", "transform": [{"function": "auth:validate_user_num_dossier", "type": "request"}, {"function": "dossier:get_id_partenaire", "params": {"set": "IdPartenaire"}, "type": "request"}, {"function": "auth:auth", "type": "request", "params": {"secret": "{BASICAUTH}"}}, {"function": "paramMapping:mapParameters", "type": "request", "params": {"input": {"source": "headers", "name": "Accept-Language", "default": "FR"}, "output": {"source": "params", "name": "<PERSON><PERSON>", "case": "upper"}}}, {"function": "errorHandler:resa<PERSON><PERSON>r", "type": "response"}, {"function": "capitalizeJson:capitalizeJson", "type": "response"}]}}, "/facturation/pdf": {"GET": {"type": "passThrough", "URL": "https://{SAP}/RESTAdapter/OS_APIRESA_FICA_WS62", "transform": [{"function": "auth:validate_user_ean", "type": "request"}, {"function": "ean:get_bp_hgz", "params": {"set": "Buspartner"}, "type": "request"}, {"function": "auth:auth", "type": "request", "params": {"secret": "{BASICAUTH}"}}, {"function": "paramMapping:mapParameters", "type": "request", "params": {"input": {"source": "headers", "name": "Accept-Language", "default": "FR"}, "output": {"source": "params", "name": "<PERSON><PERSON>", "case": "upper"}}}, {"function": "errorHandler:resa<PERSON><PERSON>r", "type": "response"}, {"function": "payload:convertToPdf", "type": "response"}, {"function": "capitalizeJson:capitalizeJson", "type": "response"}]}}, "/facturation/balance": {"GET": {"type": "passThrough", "URL": "https://{SAP}/RESTAdapter/OS_APIRESA_FICA_WS45", "transform": [{"function": "auth:validate_user_ean", "type": "request"}, {"function": "ean:get_cc_hgz", "params": {"set": "ContractAccount"}, "type": "request"}, {"function": "auth:auth", "type": "request", "params": {"secret": "{BASICAUTH}"}}, {"function": "paramMapping:mapParameters", "type": "request", "params": {"input": {"source": "headers", "name": "Accept-Language", "default": "FR"}, "output": {"source": "params", "name": "<PERSON><PERSON>", "case": "upper"}}}, {"function": "errorHandler:resa<PERSON><PERSON>r", "type": "response"}, {"function": "capitalizeJson:capitalizeJson", "type": "response"}, {"function": "text:remove<PERSON><PERSON>", "type": "response", "params": {"key": "Solde", "char": "-"}}]}}, "/tarifs": {"POST": {"id": "WS63", "type": "sqlDriven", "secret": "{HANA_SECRET}", "controller": "ws63", "sql_template": "sqlStatements/WS158_get_non_std_racc_prices.sql", "params": {"Liste": {"source": "body", "name": "Liste"}}}}, "/sms": {"GET": {"id": "WS69.1", "type": "passThrough", "URL": "https://api.ringring.be/sms/{{Sandbox}}/statusmessage", "authorizerSecrets": ["MyResaAPI/chatbot/BasicAuthPassword", "MyResaAPI/AWS/BasicAuthPassword", "MyResaAPI/SAP_User/BasicAuthPassword"], "transform": [{"function": "paramMapping:mapParameters", "type": "request", "params": {"input": {"source": "params", "name": "Sandbox"}, "output": {"source": "pathParameters", "name": "Sandbox"}}}, {"function": "paramMapping:mapParameters", "type": "request", "params": {"input": {"source": "params", "name": "MessageId"}, "output": {"source": "params", "name": "messageId"}}}, {"function": "paramMapping:mapValue", "type": "request", "params": {"input": {"source": "pathParameters", "name": "Sandbox"}, "map": {"false": "v1"}, "default": "sandbox"}}, {"function": "auth:insert<PERSON><PERSON><PERSON>", "type": "request", "params": {"output": {"source": "params", "name": "<PERSON><PERSON><PERSON><PERSON>"}, "secret": "MyResaAPI/RingRing"}}, {"function": "capitalizeJson:capitalizeJson", "type": "response"}]}, "POST": {"id": "WS69", "type": "passThrough", "URL": "https://api.ringring.be/sms/{{Sandbox}}/message", "authorizerSecrets": ["MyResaAPI/chatbot/BasicAuthPassword", "MyResaAPI/AWS/BasicAuthPassword", "MyResaAPI/SAP_User/BasicAuthPassword"], "transform": [{"function": "paramMapping:mapParameters", "type": "request", "params": {"input": {"source": "params", "name": "Sandbox"}, "output": {"source": "pathParameters", "name": "Sandbox"}}}, {"function": "ws69:check_locked_number", "type": "request"}, {"function": "paramMapping:mapValue", "type": "request", "params": {"input": {"source": "pathParameters", "name": "Sandbox"}, "map": {"false": "v1"}, "default": "sandbox"}}, {"function": "auth:insert<PERSON><PERSON><PERSON>", "type": "request", "params": {"output": {"source": "data", "name": "<PERSON><PERSON><PERSON><PERSON>"}, "secret": "MyResaAPI/RingRing"}}, {"function": "headers:headers", "type": "request", "params": {"headers": {"Content-Type": "application/json"}}}, {"function": "capitalizeJson:capitalizeJson", "type": "response"}]}}, "/sms/send_code": {"POST": {"type": "getDynamoDB", "function": "sms_code:envoyer"}}, "/sms/check_code": {"GET": {"type": "getDynamoDB", "function": "sms_code:verifier"}}, "/demande_travaux": {"GET": {"type": "passThrough", "URL": "https://{SAP}/RESTAdapter/OS_APIRESA_RAC_WS58", "authorizer": "Basic", "authorizerSecrets": ["MyResaAPI/SAP_User/BasicAuthPassword"], "transform": [{"function": "auth:auth", "type": "request", "params": {"secret": "{BASICAUTH}"}}, {"function": "paramMapping:mapParameters", "type": "request", "params": {"input": {"source": "headers", "name": "Accept-Language", "default": "FR"}, "output": {"source": "params", "name": "<PERSON><PERSON>", "case": "upper"}}}, {"function": "errorHandler:resa<PERSON><PERSON>r", "type": "response"}, {"function": "capitalizeJson:capitalizeJson", "type": "response"}]}, "POST": {"type": "passThrough", "URL": "https://{SAP}/RESTAdapter/OS_APIRESA_RAC_WS58", "transform": [{"function": "auth:auth", "type": "request", "params": {"secret": "{BASICAUTH}"}}, {"function": "paramMapping:mapParameters", "type": "request", "params": {"input": {"source": "headers", "name": "Accept-Language", "default": "FR"}, "output": {"source": "params", "name": "<PERSON><PERSON>", "case": "upper"}}}, {"function": "errorHandler:resa<PERSON><PERSON>r", "type": "response"}, {"function": "capitalizeJson:capitalizeJson", "type": "response"}]}}, "/demande_travaux/puissance/forains": {"GET": {"id": "WS114", "type": "sqlDriven", "controller": "NoPagination", "secret": "{HANA_SECRET}", "sql_template": "sqlStatements/ws114_puissance_forain.sql", "return": {}}}, "/demande_travaux/documents": {"GET": {"type": "sqlDriven", "secret": "{HANA_SECRET}", "controller": "ws116", "sql_template": "sqlStatements/ws116.sql", "return": {"type": "list", "container": {"Liste": "{LAMBDA_RESPONSE}"}}, "params": {"NumDossier": {"source": "queryStringParameters", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}}}, "/demande_travaux/dossier": {"POST": {"type": "sqlDriven", "secret": "{HANA_SECRET}", "controller": "ws59", "sql_template": {"get_config_dosrac": "sqlStatements/ws59/zpmfct_get_config_dosrac.sql", "partenaire_to_dossier": "sqlStatements/ws59/get_dossiers_from_partenaire.sql", "ean_to_dossier_0": "sqlStatements/ws59/get_dossiers_from_ean_asset.sql", "ean_to_dossier_1": "sqlStatements/ws59/get_dossiers_from_ean_install.sql", "adresse_to_dossier_0": "sqlStatements/ws59/get_dossiers_from_install_tech.sql", "adresse_to_dossier_1": "sqlStatements/ws59/get_dossiers_from_obj_racc.sql", "expand_dossier_list": "sqlStatements/ws59/expand_dossier_list.sql", "dossier_to_partenaire": "sqlStatements/ws59/get_partenaire_from_dossier.sql", "dossier_to_ean": "sqlStatements/ws59/get_ean_from_dossier.sql", "dossier_to_adresse": "sqlStatements/ws59/get_adresse_from_dossier.sql", "get_dossier_statut": "sqlStatements/ws59/get_dossier_statut.sql", "get_dossier_coordinator": "sqlStatements/ws59/get_dossier_coordinator.sql", "get_info_action_date_planif": "sqlStatements/ws59/get_info_action_date_planif.sql", "get_info_action_mont": "sqlStatements/ws59/get_info_action_mont.sql", "get_info_action_ctrl_conc": "sqlStatements/ws59/get_info_action_ctrl_conc.sql", "get_info_action": "sqlStatements/ws59/get_info_action.sql", "get_info_auart": "sqlStatements/ws59/get_info_auart.sql", "get_lib_status": "sqlStatements/ws59/get_lib_status.sql", "get_dossiers_from_user_info": "sqlStatements/ws59/get_dossiers_from_user_info.sql", "constants": "sqlStatements/ws59/get_constants.sql"}, "params": {"Adresse": {"source": "body", "name": "<PERSON><PERSON><PERSON>", "default": null}, "Ean": {"source": "body", "name": "<PERSON><PERSON>", "default": null}, "IdPartenaire": {"source": "body", "name": "IdPartenaire", "default": null}, "NumDossier": {"source": "body", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "default": null}}}}, "/demande_travaux/demande": {"POST": {"type": "passThrough", "URL": "https://{SAP}/RESTAdapter/OA_APIRESA_RAC_WS35", "transform": [{"function": "auth:auth", "type": "request", "params": {"secret": "{BASICAUTH}"}}, {"function": "paramMapping:mapParameters", "type": "request", "params": {"input": {"source": "headers", "name": "Accept-Language", "default": "FR"}, "output": {"source": "params", "name": "<PERSON><PERSON>", "case": "upper"}}}, {"function": "ws35:validation", "type": "request"}, {"function": "errorHandler:resa<PERSON><PERSON>r", "type": "response"}, {"function": "capitalizeJson:capitalizeJson", "type": "response"}]}}, "/demande_travaux/planification": {"POST": {"type": "passThrough", "URL": "https://{SAP}/RESTAdapter/OS_APIRESA_RAC_WS40", "transform": [{"function": "auth:auth", "type": "request", "params": {"secret": "{BASICAUTH}"}}, {"function": "paramMapping:mapParameters", "type": "request", "params": {"input": {"source": "headers", "name": "Accept-Language", "default": "FR"}, "output": {"source": "params", "name": "<PERSON><PERSON>", "case": "upper"}}}, {"function": "errorHandler:resa<PERSON><PERSON>r", "type": "response"}, {"function": "capitalizeJson:capitalizeJson", "type": "response"}]}}, "/demande_travaux/devis": {"GET": {"type": "sqlDriven", "controller": "ws39", "secret": "{HANA_SECRET}", "sql_template": "sqlStatements/ws39_get_devis.sql", "params": {"Ordre": {"source": "queryStringParameters", "name": "Ordre", "default": null}, "PartenaireId": {"source": "queryStringParameters", "name": "PartenaireId", "default": null}}, "return": {"type": "list", "container": {"Liste": "{LAMBDA_RESPONSE}"}}}}, "/demande_travaux/confirmation": {"GET": {"type": "passThrough", "URL": "https://{SAP}/RESTAdapter/OS_APIRESA_RAC_WS109", "transform": [{"function": "ws109:validate_dossier", "type": "request"}, {"function": "auth:auth", "type": "request", "params": {"secret": "{BASICAUTH}"}}, {"function": "paramMapping:mapParameters", "type": "request", "params": {"input": {"source": "headers", "name": "Accept-Language", "default": "FR"}, "output": {"source": "params", "name": "<PERSON><PERSON>", "case": "upper"}}}, {"function": "errorHandler:resa<PERSON><PERSON>r", "type": "response"}, {"function": "ws109:save_to_dynamo", "type": "response"}, {"function": "ws109:send_documents_to_sharepoint", "type": "response"}, {"function": "capitalizeJson:capitalizeJson", "type": "response"}], "params": {"Langue": {"source": "headers", "name": "Accept-Language", "default": "FR"}, "NumDossier": {"source": "queryStringParameters", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "DateAccord": {"source": "queryStringParameters", "name": "DateAccord"}, "TypeAction": {"source": "queryStringParameters", "name": "TypeAction"}}, "return": {"type": "list", "container": {"Liste": "{LAMBDA_RESPONSE}"}}}}, "/pannes": {"GET": {"id": "WS125", "type": "sqlDriven", "secret": "{HANA_SECRET}", "controller": "<PERSON><PERSON>", "sql_template": "sqlStatements/get_pannes_by_location_query_template.sql", "return": {"type": "list", "container": {"Srid": "4326", "Data": "{LAMBDA_RESPONSE}"}}, "params": {"Lat0": {"name": "Lat0", "source": "queryStringParameters", "default": null, "validation": {"type": "float", "rangeMin": -90, "rangeMax": 90}}, "Lat1": {"name": "Lat1", "source": "queryStringParameters", "default": null, "validation": {"type": "float", "rangeMin": -90, "rangeMax": 90}}, "Long0": {"name": "Long0", "source": "queryStringParameters", "default": null, "validation": {"type": "float", "rangeMin": -180, "rangeMax": 180}}, "Long1": {"name": "Long1", "source": "queryStringParameters", "default": null, "validation": {"type": "float", "rangeMin": -180, "rangeMax": 180}}, "DateDebut": {"name": "DateDebut", "source": "queryStringParameters", "default": null}, "DateFin": {"name": "DateFin", "source": "queryStringParameters", "default": null}, "Rue": {"name": "Rue", "source": "queryStringParameters", "default": null}, "Zipcode": {"name": "Zipcode", "source": "queryStringParameters", "default": null}, "Ville": {"name": "Ville", "source": "queryStringParameters", "default": null}, "Numero": {"name": "Numero", "source": "queryStringParameters", "default": null}, "OnlyEp": {"name": "Ep", "source": "queryStringParameters", "default": false, "validation": {"type": "bool"}}, "EnCours": {"name": "EnCours", "source": "queryStringParameters", "default": null, "validation": {"type": "bool"}}}}}, "/pannes/{Id}": {"GET": {"type": "sqlDriven", "secret": "{HANA_SECRET}", "controller": "<PERSON><PERSON>", "sql_template": "sqlStatements/get_pannes_by_ids_query_template.sql", "return": {"type": "object", "container": {"Srid": "4326", "Data": "{LAMBDA_RESPONSE}"}}, "params": {"id": {"source": "pathParameters", "name": "Id", "validation": {"type": "int"}}}}}, "/pannes/active": {"GET": {"type": "panne<PERSON><PERSON><PERSON>"}}, "/pannes/tad": {"GET": {"type": "sqlDriven", "secret": "{IPTelephony_SECRET}", "controller": "<PERSON>", "sql_template": "sqlStatements/get_pannes_tad.sql", "return": {"type": "list", "container": {"Data": "{LAMBDA_RESPONSE}"}}, "params": {}}}, "/pannes/ep": {"POST": {"id": "WS22", "type": "passThrough", "URL": "https://{SAP}/RESTAdapter/OS_APIRESA_PM_WS22", "transform": [{"function": "auth:auth", "type": "request", "params": {"secret": "{BASICAUTH}"}}, {"function": "paramMapping:mapParameters", "type": "request", "params": {"input": {"source": "headers", "name": "Accept-Language", "default": "FR"}, "output": {"source": "params", "name": "<PERSON><PERSON>", "case": "upper"}}}, {"function": "ws23:adapt_equi_id", "type": "request"}, {"function": "errorHandler:resa<PERSON><PERSON>r", "type": "response"}, {"function": "capitalizeJson:capitalizeJson", "type": "response"}, {"function": "ws23:save_coords", "type": "response"}]}}, "/pannes/planned": {"GET": {"type": "sqlDriven", "secret": "{HANA_SECRET}", "controller": "ws24", "sql_template": {"base_statement": "sqlStatements/ws24_get_planned_interruptions.sql", "street_filter": "sqlStatements/ws24_filter_by_street.sql", "city_filter": "sqlStatements/ws24_filter_by_city.sql", "rad_filter": "sqlStatements/ws24_filter_by_rad.sql"}, "return": {"type": "list", "container": {"Srid": "4326", "Data": "{LAMBDA_RESPONSE}"}}, "params": {"CdPostal": {"source": "queryStringParameters", "name": "CdPostal", "default": null}, "Localite": {"source": "queryStringParameters", "name": "Localite", "default": null}, "Rue": {"source": "queryStringParameters", "name": "Rue", "default": null}, "IdRue": {"source": "queryStringParameters", "name": "IdRue", "default": null}, "IdLocalite": {"source": "queryStringParameters", "name": "IdLocalite", "default": null}, "IdRadRue": {"source": "queryStringParameters", "name": "IdRadRue", "default": null}, "IdRadLocalite": {"source": "queryStringParameters", "name": "IdRadLocalite", "default": null}, "Lat0": {"name": "Lat0", "source": "queryStringParameters", "validation": {"type": "float", "rangeMin": -90, "rangeMax": 90}, "default": null}, "Lat1": {"name": "Lat1", "source": "queryStringParameters", "validation": {"type": "float", "rangeMin": -90, "rangeMax": 90}, "default": null}, "Long0": {"name": "Long0", "source": "queryStringParameters", "validation": {"type": "float", "rangeMin": -180, "rangeMax": 180}, "default": null}, "Long1": {"name": "Long1", "source": "queryStringParameters", "validation": {"type": "float", "rangeMin": -180, "rangeMax": 180}, "default": null}}}}, "/pannes/unplanned": {"GET": {"id": "WS25", "type": "sqlDriven", "secret": "{HANA_SECRET}", "controller": "ws25", "sql_template": {"base_statement": "sqlStatements/ws25_get_unplanned_interruptions.sql"}, "return": {"type": "list", "container": {"Srid": "4326", "Data": "{LAMBDA_RESPONSE}"}}, "params": {"CdPostal": {"source": "queryStringParameters", "name": "CdPostal", "default": null}, "Localite": {"source": "queryStringParameters", "name": "Localite", "default": null}, "Rue": {"source": "queryStringParameters", "name": "Rue", "default": null}, "IdRue": {"source": "queryStringParameters", "name": "IdRue", "default": null}, "IdLocalite": {"source": "queryStringParameters", "name": "IdLocalite", "default": null}, "IdRadRue": {"source": "queryStringParameters", "name": "IdRadRue", "default": null}, "IdRadLocalite": {"source": "queryStringParameters", "name": "IdRadLocalite", "default": null}, "Lat0": {"name": "Lat0", "source": "queryStringParameters", "validation": {"type": "float", "rangeMin": -90, "rangeMax": 90}, "default": null}, "Lat1": {"name": "Lat1", "source": "queryStringParameters", "validation": {"type": "float", "rangeMin": -90, "rangeMax": 90}, "default": null}, "Long0": {"name": "Long0", "source": "queryStringParameters", "validation": {"type": "float", "rangeMin": -180, "rangeMax": 180}, "default": null}, "Long1": {"name": "Long1", "source": "queryStringParameters", "validation": {"type": "float", "rangeMin": -180, "rangeMax": 180}, "default": null}, "EnCours": {"name": "EnCours", "source": "queryStringParameters", "default": null, "validation": {"type": "bool"}}}}}, "/pannes/unplanned/{Id}": {"GET": {"type": "sqlDriven", "secret": "{HANA_SECRET}", "controller": "ws25_1", "sql_template": "sqlStatements/ws25.1_get_unplanned_interruption.sql", "return": {"type": "object"}, "params": {"Id": {"source": "pathParameters", "name": "Id"}}}}, "/pannes/smart": {"GET": {"id": "WS184", "type": "getDynamoDB", "function": "pannes_smart:get_panne_smart"}, "POST": {"id": "WS183", "type": "getDynamoDB", "function": "pannes_smart:post_panne_smart"}}, "/cab/point_rechargement": {"GET": {"type": "sqlDriven", "secret": "{STORELOCATION_SECRET}", "controller": "pointderecharge", "sql_template": ["sqlStatements/get_store_locations_template.sql", "sqlStatements/get_store_locations_geo_filter_template.sql"], "return": {"type": "list", "container": {"Srid": "4326", "Data": "{LAMBDA_RESPONSE}"}}, "params": {"Lat0": {"name": "Lat0", "source": "queryStringParameters", "default": null, "validation": {"type": "float", "rangeMin": 0, "rangeMax": 90}}, "Lat1": {"name": "Lat1", "source": "queryStringParameters", "default": null, "validation": {"type": "float", "rangeMin": 0, "rangeMax": 90}}, "Long0": {"name": "Long0", "source": "queryStringParameters", "default": null, "validation": {"type": "float", "rangeMin": 0, "rangeMax": 180}}, "Long1": {"name": "Long1", "source": "queryStringParameters", "default": null, "validation": {"type": "float", "rangeMin": 0, "rangeMax": 180}}}}}, "/ean/cpt": {"GET": {"type": "sqlDriven", "secret": "{HANA_SECRET}", "sql_template": "sqlStatements/ws28_get_ean_from_counter.sql", "controller": "ws28", "return": {"type": "list", "container": {"ListeEan": "{LAMBDA_RESPONSE}"}, "pagination": false}, "params": {"NumCompteur": {"name": "NumCompteur", "source": "queryStringParameters"}}}}, "/ean/validation": {"GET": {"type": "eanFormatChecker"}}, "/ep": {"GET": {"type": "sqlDriven", "secret": "{HANA_SECRET}", "controller": "Ep", "sql_template": "sqlStatements/get_ep_equipements_by_gps_query_template.sql", "return": {"type": "list", "container": {"Srid": "4326", "Data": "{LAMBDA_RESPONSE}"}}, "params": {"FiltreEnPanne": {"name": "FiltreEnPanne", "source": "queryStringParameters", "default": null, "validation": {"type": "bool"}}, "Lat0": {"name": "Lat0", "source": "queryStringParameters", "default": null, "validation": {"type": "float", "rangeMin": -90, "rangeMax": 90}}, "Lat1": {"name": "Lat1", "source": "queryStringParameters", "default": null, "validation": {"type": "float", "rangeMin": -90, "rangeMax": 90}}, "Long0": {"name": "Long0", "source": "queryStringParameters", "default": null, "validation": {"type": "float", "rangeMin": -180, "rangeMax": 180}}, "Long1": {"name": "Long1", "source": "queryStringParameters", "default": null, "validation": {"type": "float", "rangeMin": -180, "rangeMax": 180}}, "DateDebut": {"name": "DateDebut", "source": "queryStringParameters", "default": null}, "DateFin": {"name": "DateFin", "source": "queryStringParameters", "default": null}}}}, "/ep/{Id}": {"GET": {"type": "sqlDriven", "secret": "{HANA_SECRET}", "controller": "ws120", "sql_template": "sqlStatements/WS120_get_ep_by_id.sql", "params": {"Id": {"name": "Id", "source": "pathParameters"}, "IdCommune": {"name": "IdCommune", "source": "queryStringParameters", "default": null}}, "return": {"type": "object"}}}, "/ep/communes": {"GET": {"type": "sqlDriven", "secret": "{HANA_SECRET}", "controller": "NoPaginationPascalCaseController", "sql_template": "sqlStatements/ws128_ep_communes.sql", "params": {}, "return": {"type": "list", "container": {"Liste": "{LAMBDA_RESPONSE}"}}}}, "/partenaire/{PartenaireId}": {"GET": {"type": "sqlDriven", "secret": "{HANA_SECRET}", "authorizer": "Basic", "authorizerSecrets": ["MyResaAPI/SAP_User/BasicAuthPassword"], "controller": "Controller", "sql_template": "sqlStatements/get_partner_info.sql", "params": {"PartenaireId": {"source": "pathParameters", "name": "PartenaireId"}}, "return": {"type": "object"}}}, "/portP1": {"GET": {"type": "passThrough", "URL": "https://{SAP}/RESTAdapter/OS_APIRESA_ISU_WS60", "transform": [{"function": "auth:auth", "type": "request", "params": {"secret": "{BASICAUTH}"}}, {"function": "paramMapping:mapParameters", "type": "request", "params": {"input": {"source": "headers", "name": "Accept-Language", "default": "FR"}, "output": {"source": "params", "name": "<PERSON><PERSON>", "case": "upper"}}}, {"function": "headers:headers", "type": "request", "params": {"headers": {"accept": "application/json"}}}, {"function": "errorHandler:resa<PERSON><PERSON>r", "type": "response"}, {"function": "capitalizeJson:capitalizeJson", "type": "response"}], "params": {"Ean": {"source": "queryStringParameters", "name": "<PERSON><PERSON>", "default": null}, "NumCpt": {"source": "queryStringParameters", "name": "NumCpt", "default": null}}}}, "/portP1/demande": {"POST": {"type": "passThrough", "URL": "https://{SAP}/RESTAdapter/OS_APIRESA_ISU_WS61", "transform": [{"function": "auth:auth", "type": "request", "params": {"secret": "{BASICAUTH}"}}, {"function": "paramMapping:mapParameters", "type": "request", "params": {"input": {"source": "headers", "name": "Accept-Language", "default": "FR"}, "output": {"source": "params", "name": "<PERSON><PERSON>", "case": "upper"}}}, {"function": "headers:headers", "type": "request", "params": {"headers": {"accept": "application/json"}}}, {"function": "errorHandler:resa<PERSON><PERSON>r", "type": "response"}, {"function": "capitalizeJson:capitalizeJson", "type": "response"}]}}, "/utilisateurs": {"POST": {"type": "getDynamoDB", "function": "compte:creer"}, "PATCH": {"type": "getDynamoDB", "function": "compte:edit"}}, "/utilisateurs/authenticate": {"POST": {"type": "get_ad_token"}}, "/utilisateurs/authenticate/logout": {"POST": {"type": "getDynamoDB", "function": "compte:logout"}}, "/utilisateurs/activate": {"POST": {"type": "getDynamoDB", "function": "compte:activer"}}, "/envMessage": {"POST": {"id": "WS68", "type": "getDynamoDB", "function": "compte:env_message"}}, "/envMessage/{TemplateId}/body": {"GET": {"id": "WS189", "type": "getDynamoDB", "function": "envMessageTemplateidBody:handler", "params": {"TemplateId": {"source": "pathParameters", "name": "TemplateId"}}}}, "/utilisateurs/reset/sendMail": {"POST": {"id": "WS03", "type": "getDynamoDB", "function": "compte:reset"}}, "/utilisateurs/reset": {"POST": {"type": "getDynamoDB", "function": "compte:resetChangePassword"}}, "/utilisateurs/updateMail/sendMail": {"POST": {"id": "WS04.4", "type": "getDynamoDB", "function": "compte:updateMailSendMail"}}, "/utilisateurs/updateMail": {"POST": {"id": "WS04.3", "type": "getDynamoDB", "function": "compte:updateMail"}}, "/utilisateurs/activate/sendMail": {"POST": {"id": "WS04.1", "type": "getDynamoDB", "function": "compte:send_verification_email"}}, "/utilisateurs/activate/sendSms": {"POST": {"type": "getDynamoDB", "function": "compte:send_verification_sms"}}, "/utilisateurs/activate/checkSms": {"POST": {"type": "getDynamoDB", "function": "compte:check_verification_sms"}}, "/utilisateurs/activate/checkMail": {"GET": {"type": "getDynamoDB", "function": "compte:check_verification_mail"}}, "/utilisateurs/updatePassword": {"POST": {"id": "WS04.2", "type": "getDynamoDB", "function": "compte:updatePassword"}}, "/me": {"DELETE": {"id": "WS84", "type": "getDynamoDB", "function": "compte:delete_account"}}, "/me/delete": {"GET": {"id": "WS84", "type": "getDynamoDB", "function": "compte:delete_account"}}, "/me/delete/sendMail": {"POST": {"id": "WS154", "type": "getDynamoDB", "function": "compte:send_delete_account_mail"}}, "/me/vehicles": {"POST": {"id": "WS157", "type": "vehiclesDataHandler", "function": "vehicles:ajouter"}, "GET": {"id": "WS156", "type": "vehiclesDataHandler", "function": "vehicles:recuperer"}}, "/me/vehicles/<UUID>": {"DELETE": {"id": "WS159", "type": "vehiclesDataHandler", "function": "vehicles:supprimer"}, "GET": {"type": "vehiclesDataHandler"}}, "/me/vehicles/<UUID>/sendMail": {"GET": {"id": "WS170", "type": "vehiclesDataHandler", "function": "vehicles:sendMail"}}, "/me/vehicles/<UUID>/pdf": {"GET": {"id": "WS171", "type": "vehiclesDataHandler", "function": "vehicles:pdf"}}, "/me/bornes_recharge": {"GET": {"id": "WS173", "type": "bornesRechargeDataHandler", "function": "bornesRechage:get_data"}}, "/me/bornes_recharge/<uuid>": {"PUT": {"id": "WS174", "type": "bornesRechargeDataHandler", "function": "bornesRechage:put_data"}, "DELETE": {"id": "WS175", "type": "bornesRechargeDataHandler", "function": "bornesRechage:delete_data"}}, "/uuid": {"GET": {"id": "WS195", "type": "simpleCompute", "function": "uuid_v1_generator:generate"}}, "/panne_subscription": {"POST": {"id": "ws09", "type": "getDynamoDB", "function": "pannes:ajouter"}}, "/panne_subscription/{PanneId}": {"DELETE": {"type": "getDynamoDB", "function": "pannes:enlever"}}, "/panne_subscription/{PanneId}/remove": {"GET": {"type": "getDynamoDB", "function": "pannes:unsubscribe"}}, "/me/dashboard": {"GET": {"type": "sqlDriven", "secret": "{HANA_SECRET}", "controller": "ws14", "sql_template": {"user_data": "sqlStatements/ws14/ws14_user_data.sql", "eans": "sqlStatements/ws14/ws14_eans.sql", "dossiers": "sqlStatements/ws14/ws14_dossiers.sql", "preferences": "sqlStatements/ws14/ws14_preferences.sql"}, "params": {}, "return": {"type": "list"}}}, "/me/energy_profile": {"POST": {"id": "WS206", "type": "getDynamoDB", "function": "energy_profile:register"}, "GET": {"id": "WS208", "type": "getDynamoDB", "function": "energy_profile:fetch"}}, "/me/energy_profile/target": {"GET": {"id": "WS217", "type": "sqlDriven", "secret": "{REDSHIFT_SECRET}", "controller": "ws217", "sql_template": "sqlStatements/ws217_monthly_sql_multi_ean.sql"}}, "/me/notifications": {"GET": {"type": "getDynamoDB", "function": "dashboard:notifications"}, "POST": {"type": "getDynamoDB", "function": "dashboard:create_notifications"}}, "/me/preferences": {"GET": {"type": "getDynamoDB", "function": "preferences:lire"}, "POST": {"type": "getDynamoDB", "function": "preferences:ajouter"}}, "/me/preferences/{Key}": {"PATCH": {"type": "getDynamoDB", "function": "preferences:modifier"}, "DELETE": {"type": "getDynamoDB", "function": "preferences:enlever"}}, "/me/consentements": {"POST": {"id": "WS112", "type": "getDynamoDB", "function": "consentements:ajouter"}, "GET": {"id": "WS111", "type": "getDynamoDB", "function": "consentements:lire"}}, "/sap/utilisateur/creation": {"POST": {"type": "passThrough", "URL": "https://{SAP}/RESTAdapter/OS_APIRESA_ISU_WS73", "authorizer": "Basic", "authorizerSecrets": ["MyResaAPI/SAP_User/BasicAuthPassword"], "transform": [{"function": "auth:auth", "type": "request", "params": {"secret": "{BASICAUTH}"}}, {"function": "paramMapping:mapParameters", "type": "request", "params": {"input": {"source": "headers", "name": "Accept-Language", "default": "FR"}, "output": {"source": "params", "name": "<PERSON><PERSON>", "case": "upper"}}}, {"function": "errorHandler:resa<PERSON><PERSON>r", "type": "response"}, {"function": "capitalizeJson:capitalizeJson", "type": "response"}]}}, "/sap/utilisateur/recherche": {"GET": {"type": "sqlDriven", "secret": "{HANA_SECRET}", "controller": "SapUser<PERSON><PERSON>y", "sql_template": "sqlStatements/sap_utilisateur_recherche.sql", "authorizerSecrets": ["MyResaAPI/SAP_User/BasicAuthPassword"], "params": {"BpNom": {"source": "queryStringParameters", "name": "BpNom", "default": null}, "BpPrenom": {"source": "queryStringParameters", "name": "BpPrenom", "default": null}, "BpTelFixe": {"source": "queryStringParameters", "name": "BpTelFixe", "default": null}, "BpTelPortable": {"source": "queryStringParameters", "name": "BpTelPortable", "default": null}, "BpEmail": {"source": "queryStringParameters", "name": "BpEmail", "default": null}, "BpRue": {"source": "queryStringParameters", "name": "BpRue", "default": null}, "BpNumrue": {"source": "queryStringParameters", "name": "BpNumrue", "default": null}, "BpCdpostal": {"source": "queryStringParameters", "name": "BpCdpostal", "default": null}, "BpLocalite": {"source": "queryStringParameters", "name": "BpLocalite", "default": null}}, "return": {"type": "list", "container": {"Data": "{LAMBDA_RESPONSE}"}}}}, "/sap/utilisateur/modification": {"POST": {"type": "passThrough", "URL": "https://{SAP}/RESTAdapter/OS_APIRESA_ISU_WS74", "authorizer": "Basic", "authorizerSecrets": ["MyResaAPI/SAP_User/BasicAuthPassword"], "transform": [{"function": "auth:auth", "type": "request", "params": {"secret": "{BASICAUTH}"}}, {"function": "paramMapping:mapParameters", "type": "request", "params": {"input": {"source": "headers", "name": "Accept-Language", "default": "FR"}, "output": {"source": "params", "name": "<PERSON><PERSON>", "case": "upper"}}}, {"function": "errorHandler:resa<PERSON><PERSON>r", "type": "response"}, {"function": "capitalizeJson:capitalizeJson", "type": "response"}]}}, "/sap/utilisateur/suppression": {"DELETE": {"type": "passThrough", "URL": "https://{SAP}/RESTAdapter/OS_APIRESA_ISU_WS75", "authorizer": "Basic", "authorizerSecrets": ["MyResaAPI/SAP_User/BasicAuthPassword"], "transform": [{"function": "auth:auth", "type": "request", "params": {"secret": "{BASICAUTH}"}}, {"function": "paramMapping:mapParameters", "type": "request", "params": {"input": {"source": "headers", "name": "Accept-Language", "default": "FR"}, "output": {"source": "params", "name": "<PERSON><PERSON>", "case": "upper"}}}, {"function": "errorHandler:resa<PERSON><PERSON>r", "type": "response"}, {"function": "capitalizeJson:capitalizeJson", "type": "response"}]}}, "/token": {"POST": {"type": "passThrough", "method": "GET", "URL": "https://{SAP}/RESTAdapter/OS_APIRESA_ISU_WS64", "transform": [{"function": "auth:auth", "type": "request", "params": {"secret": "{BASICAUTH}"}}, {"function": "paramMapping:mapParameters", "type": "request", "params": {"input": {"source": "headers", "name": "Accept-Language", "default": "FR"}, "output": {"source": "params", "name": "<PERSON><PERSON>", "case": "upper"}}}, {"function": "ppp:prepare_ws64", "type": "request", "params": {}}, {"function": "capitalizeJson:capitalizeJson", "type": "response"}, {"function": "errorHandler:resa<PERSON><PERSON>r", "type": "response"}, {"function": "ppp:add_account_info_from_token", "type": "response", "params": {}}]}}, "/token/invalider": {"POST": {"type": "getDynamoDB", "function": "mock:mock200"}}, "/me/notifications/status": {"PATCH": {"type": "getDynamoDB", "function": "dashboard:notifications_status"}}, "/formulaire/demande": {"POST": {"type": "getDynamoDB", "function": "formulaire:formulaire_demande"}}, "/fichiers/upload": {"GET": {"id": "WS77", "type": "documentUpload"}}, "/fichiers/temp_upload": {"GET": {"type": "tempFileUpload"}}, "/fichiers/liste": {"GET": {"type": "documentUpload"}}, "/asynchrone": {"POST": {"type": "getDynamoDB", "function": "asynchrone:post"}}, "/asynchrone/callback": {"POST": {"type": "getDynamoDB", "function": "asynchrone:callback"}}, "/monitoring": {"GET": {"type": "getDynamoDB", "function": "monitoring:monitoring", "authorizer": "Basic", "authorizerSecrets": ["MyResaAPI/SAP_User/BasicAuthPassword"]}}, "/asynchrone/{Id}": {"GET": {"type": "getDynamoDB", "function": "asynchrone:get_result"}}, "/powalco/entrepreneurs/account": {"POST": {"type": "getDynamoDB", "function": "entrepreneur:creer_compte"}}, "/powalco/entrepreneurs/account/enable": {"PATCH": {"type": "getDynamoDB", "function": "entrepreneur:switch_account_enabled_state"}}, "/powalco/entrepreneurs": {"GET": {"type": "sqlDriven", "controller": "Entrepreneurs", "secret": "{HANA_SECRET}", "sql_template": "sqlStatements/get_entrepreneurs.sql", "params": {}, "return": {"type": "list", "container": {"Data": "{LAMBDA_RESPONSE}"}}}}, "/powalco/entrepreneurs/{EntrepreneurId}": {"GET": {"type": "getDynamoDB", "function": "entrepreneur:get_entrepreneur", "authorizer": "Basic", "authorizerSecrets": ["MyResaAPI/SAP_User/BasicAuthPassword"]}}, "/powalco/chantiers": {"GET": {"type": "sqlDriven", "controller": "Chantiers", "secret": "{HANA_SECRET}", "sql_template": "sqlStatements/get_chantiers_by_entrepreneur.sql", "params": {}, "return": {"type": "list", "container": {"Data": "{LAMBDA_RESPONSE}"}}}}, "/powalco/chantiers/{Id}": {"GET": {"type": "sqlDriven", "secret": "{HANA_SECRET}", "authorizer": "Basic", "authorizerSecrets": ["MyResaAPI/SAP_User/BasicAuthPassword"], "sql_template": "sqlStatements/get_select_chantier.sql", "params": {"Id": {"source": "pathParameters", "name": "Id"}}, "return": {"type": "object"}}}, "/powalco/entrepreneurs/chantiers/upload": {"GET": {"type": "getDynamoDB", "function": "entrepreneur:list_files"}, "POST": {"type": "getDynamoDB", "function": "entrepreneur:upload_file"}, "PATCH": {"type": "getDynamoDB", "function": "entrepreneur:modify_upload"}, "DELETE": {"type": "getDynamoDB", "function": "entrepreneur:delete_file"}}, "/ppp/master/{IdBp}": {"GET": {"type": "sqlDriven", "secret": "{HANA_SECRET}", "sql_template": "sqlStatements/WS103_get_master_bp.sql", "params": {"BP": {"source": "pathParameters", "name": "IdBp", "default": null}}, "return": {"type": "string"}}}, "/gazMeter/{sn}/pin": {"GET": {"type": "sqlDriven", "secret": "{HANA_SECRET}", "controller": "GazPin", "sql_template": "sqlStatements/gaz_meter_get_pin.sql", "params": {"sn": {"source": "pathParameters", "name": "sn"}, "apikey": {"source": "headers", "name": "apikey"}}, "return": {"type": "string"}}}, "/payements/mollie/update": {"POST": {"id": "WS110", "type": "getDynamoDB", "function": "mollie:update"}}, "/payements/mollie/status": {"GET": {"id": "WS187", "type": "getDynamoDB", "function": "mollie:retrieve_mollie_data"}, "POST": {"id": "WS210", "type": "getDynamoDB", "function": "mollie:post_mollie_waiting"}}, "/referentiel/civilite": {"GET": {"type": "sqlDriven", "secret": "{HANA_SECRET}", "controller": "NoPagination", "sql_template": "sqlStatements/referentiel_civilite.sql", "return": {"type": "list", "container": {"Data": "{LAMBDA_RESPONSE}"}}, "params": {}}}, "/digacert/soap": {"POST": {"type": "passThrough", "URL": "{DIGACERT_URL}", "transform": [{"function": "ws113:store_cert", "type": "response"}]}}, "/utilisateurs/edit/{Uid}/preferences": {"GET": {"type": "getDynamoDB", "function": "preferences:set_by_uid"}}, "/sms/incoming": {"POST": {"type": "incomingSms"}}, "/bornes_recharge": {"GET": {"type": "passThrough", "URL": "https://api.eco-movement.com/api/ocpi/cpo/2.1.1/locations", "transform": [{"function": "auth:insert<PERSON><PERSON><PERSON>", "type": "request", "params": {"output": {"source": "headers", "name": "Authorization"}, "secret": "MyResaAPI/EcoMovementToken"}}, {"function": "ws129:refine_result", "type": "response"}]}, "POST": {"type": "getDynamoDB", "function": "bornes_recharge:post"}}, "/ibanity": {"POST": {"type": "genericPassThrough", "base_url": "https://api.ibanity.com", "security": {"default": {"cert": "Ibanity/Cert/SANDBOX", "key": "Ibanity/PK/SANDBOX"}, "IBANITY_CERT_RESA_BETA": {"cert": "Ibanity/Cert/BETA", "key": "Ibanity/PK/BETA"}, "IBANITY_CERT_RESA_SANDBOX": {"cert": "Ibanity/Cert/SANDBOX", "key": "Ibanity/PK/SANDBOX"}, "IBANITY_CERT_RESA_PROD": {"cert": "Ibanity/Cert/PRD", "key": "Ibanity/PK/PRD"}}}}, "/raccordement": {"POST": {"id": "WS199", "type": "simpleCompute", "function": "connection:create"}}, "/raccordement/upload_plan": {"POST": {"type": "passThrough", "URL": "{FME_RACC_PLAN_URL}"}}, "/documentation": {"GET": {"id": "WS190", "type": "getDocApi"}}, "/communes/utilisateurs": {"GET": {"id": "WS135", "type": "getDynamoDB", "function": "communes:get_users"}, "POST": {"id": "WS137", "type": "getDynamoDB", "function": "communes:create_user"}}, "/communes/utilisateurs/{Uid}": {"GET": {"id": "WS136", "type": "getDynamoDB", "function": "communes:get_user"}, "PATCH": {"id": "WS138", "type": "getDynamoDB", "function": "communes:patch_user"}, "DELETE": {"id": "WS145", "type": "getDynamoDB", "function": "communes:delete_user"}}, "/communes/roles": {"GET": {"id": "WS142", "type": "sqlDriven", "secret": "{HANA_SECRET}", "controller": "NoPagination", "sql_template": "sqlStatements/ws142_get_communes_roles.sql"}}, "/communes/demande_travaux/dossiers": {"GET": {"id": "WS133", "type": "sqlDriven", "secret": "{HANA_SECRET}", "controller": "ws133", "sql_template": {"get_dossiers": "sqlStatements/ws133_get_communes_dossiers.sql", "get_lib_status": "sqlStatements/ws59/get_lib_status.sql"}, "roles": ["RACC_CONSU", "RACC_GERER"]}}, "/communes/demande_travaux/dossiers/{Id}": {"GET": {"id": "WS134", "type": "sqlDriven", "secret": "{HANA_SECRET}", "controller": "ws134", "params": {"Id": {"source": "pathParameters", "name": "Id"}}, "sql_template": {"get_config_dosrac": "sqlStatements/ws59/zpmfct_get_config_dosrac.sql", "partenaire_to_dossier": "sqlStatements/ws59/get_dossiers_from_partenaire.sql", "ean_to_dossier_0": "sqlStatements/ws59/get_dossiers_from_ean_asset.sql", "ean_to_dossier_1": "sqlStatements/ws59/get_dossiers_from_ean_install.sql", "adresse_to_dossier_0": "sqlStatements/ws59/get_dossiers_from_install_tech.sql", "adresse_to_dossier_1": "sqlStatements/ws59/get_dossiers_from_obj_racc.sql", "expand_dossier_list": "sqlStatements/ws59/expand_dossier_list.sql", "dossier_to_partenaire": "sqlStatements/ws59/get_partenaire_from_dossier.sql", "dossier_to_ean": "sqlStatements/ws59/get_ean_from_dossier.sql", "dossier_to_adresse": "sqlStatements/ws59/get_adresse_from_dossier.sql", "get_dossier_statut": "sqlStatements/ws59/get_dossier_statut.sql", "get_dossier_coordinator": "sqlStatements/ws59/get_dossier_coordinator.sql", "get_info_action_date_planif": "sqlStatements/ws59/get_info_action_date_planif.sql", "get_info_action_mont": "sqlStatements/ws59/get_info_action_mont.sql", "get_info_action_ctrl_conc": "sqlStatements/ws59/get_info_action_ctrl_conc.sql", "get_info_action": "sqlStatements/ws59/get_info_action.sql", "get_info_auart": "sqlStatements/ws59/get_info_auart.sql", "get_lib_status": "sqlStatements/ws59/get_lib_status.sql", "get_dossiers_from_user_info": "sqlStatements/ws59/get_dossiers_from_user_info.sql", "constants": "sqlStatements/ws59/get_constants.sql", "get_dossiers": "sqlStatements/ws133_get_communes_dossiers.sql"}}}, "/communes/ean": {"GET": {"id": "WS130", "type": "sqlDriven", "secret": "{HANA_SECRET}", "controller": "CommuneControllerEan", "sql_template": "sqlStatements/ws130_get_communes_eans.sql", "params": {"Smart": {"source": "queryStringParameters", "name": "Smart", "default": null, "validation": {"type": "bool"}}}, "roles": ["INDEX_CPT_CONSU", "INDEX_CPT_GERER"]}}, "/communes/ean/cadrans": {"GET": {"id": "WS197", "type": "sqlDriven", "secret": "{HANA_SECRET}", "controller": "CommuneControllerEanCadran", "sql_template": "sqlStatements/ws197_get_commune_ean_cadran_info.sql", "params": {"Smart": {"source": "queryStringParameters", "name": "Smart", "default": null, "validation": {"type": "bool"}}}, "roles": ["INDEX_CPT_CONSU", "INDEX_CPT_GERER"]}}, "/communes/index": {"GET": {"id": "WS147", "type": "sqlDriven", "secret": "{HANA_SECRET}", "controller": "CommuneControllerIndex", "sql_template": "sqlStatements/ws147_get_ean_info_non_releve.sql", "params": {"Smart": {"source": "queryStringParameters", "name": "Smart", "default": false, "validation": {"type": "bool"}}, "FullEan": {"source": "queryStringParameters", "name": "FullEan", "default": false, "validation": {"type": "bool"}}}, "roles": ["INDEX_CPT_CONSU", "INDEX_CPT_GERER"]}, "POST": {"id": "WS148", "type": "CommuneUploadIndex", "roles": ["INDEX_CPT_GERER"]}}, "/communes/index/smart": {"GET": {"id": "WS220", "type": "sqlDriven", "secret": "{REDSHIFT_SECRET}", "controller": "CommuneControllerIndexSmart", "sql_template": {"monthly": "sqlStatements/ws205_get_monthly_conso.sql", "daily": "sqlStatements/ws205_get_daily_conso.sql", "get_eans": "sqlStatements/ws130_get_communes_eans.sql"}, "params": {"Type": {"source": "queryStringParameters", "name": "Type", "default": null}, "StartDate": {"source": "queryStringParameters", "name": "StartDate", "default": null}, "EndDate": {"source": "queryStringParameters", "name": "EndDate", "default": null}, "Ean": {"source": "queryStringParameters", "name": "<PERSON><PERSON>", "default": null}, "MeterId": {"source": "queryStringParameters", "name": "MeterId", "default": null}}, "roles": ["INDEX_CPT_CONSU", "INDEX_CPT_GERER"]}}, "/communes/index/smart/export": {"GET": {"id": "WS222", "type": "simpleCompute", "function": "export_ean_data:start_export_communes"}}, "/communes/ean/{Ean}": {"GET": {"id": "WS131", "type": "sqlDriven", "secret": "{HANA_SECRET}", "controller": "ws131", "params": {"Ean": {"source": "pathParameters", "name": "<PERSON><PERSON>"}}, "sql_template": {"get_energy_type": "sqlStatements/ws29_get_energy_type.sql", "get_energy_measures": "sqlStatements/ws29_get_energy_measures.sql", "get_installation_properties": "sqlStatements/ws29_get_installation_properties.sql", "get_business_info": "sqlStatements/ws29_get_business_info.sql", "get_hgz_info": "sqlStatements/ws29_get_hgz_info.sql", "get_counter_info": "sqlStatements/ws29_get_counter_info.sql", "get_adress_info": "sqlStatements/ws29_get_adress_info.sql", "get_liaison_info_E": "sqlStatements/ws29_get_liaison_info_E.sql", "get_liaison_info_G": "sqlStatements/ws29_get_liaison_info_G.sql", "get_protection_info": "sqlStatements/ws29_get_protection_info.sql", "get_label_horaire": "sqlStatements/ws29_get_label_horaire.sql", "get_direct_characteristic": "sqlStatements/ws29_get_direct_characteristic.sql", "get_characteristic_by_names_list": "sqlStatements/ws29_get_characteristic_by_names_list.sql", "get_characteristic_by_name": "sqlStatements/ws29_get_characteristic_by_name.sql", "get_eans": "sqlStatements/ws130_get_communes_eans.sql"}}}, "/communes/ean/{Ean}/historique": {"GET": {"id": "WS132", "type": "sqlDriven", "secret": "{HANA_SECRET}", "controller": "ws132", "params": {"Ean": {"source": "pathParameters", "name": "<PERSON><PERSON>"}}, "roles": ["INDEX_CPT_CONSU", "INDEX_CPT_GERER"], "sql_template": {"get_historique": "sqlStatements/ws132_get_historique_conso_from_ean.sql", "get_eans": "sqlStatements/ws130_get_communes_eans.sql"}}}, "/communes/ep": {"GET": {"id": "WS140", "type": "sqlDriven", "secret": "{HANA_SECRET}", "controller": "ws140", "roles": ["CONSO_EP"], "sql_template": {"ep_conso": "sqlStatements/ws140_get_ep_conso.sql", "led_count": "sqlStatements/ws140_get_ep_led_count.sql"}}}, "/raccordement/tarifs": {"GET": {"id": "WS158", "type": "sqlDriven", "secret": "{HANA_SECRET}", "controller": "NoPaginationPascalCaseController", "sql_template": "sqlStatements/WS158_get_non_std_racc_prices.sql"}}, "/raccordement/cout": {"GET": {"id": "WS172", "type": "coutModifRaccordement"}}, "/raccordement/forfaits": {"GET": {"type": "mock_call", "return": {"code": 200, "body": [{"Label": "Essentiel", "Phases": 1, "Amperage": 40, "Power": 9.2, "AmperageUnit": "A", "PowerUnit": "kVA", "Price": 971.98}, {"Label": "<PERSON><PERSON>", "Phases": 1, "Amperage": 63, "Power": 14.5, "AmperageUnit": "A", "PowerUnit": "kVA", "Price": 1855.78}, {"Label": "Essentiel", "Phases": 3, "Amperage": 25, "Power": 10, "AmperageUnit": "A", "PowerUnit": "kVA", "Price": 971.98}, {"Label": "<PERSON><PERSON>", "Phases": 3, "Amperage": 32, "Power": 12.7, "AmperageUnit": "A", "PowerUnit": "kVA", "Price": 1855.78}, {"Label": "Confort +", "Phases": 3, "Amperage": 40, "Power": 15.9, "AmperageUnit": "A", "PowerUnit": "kVA", "Price": 2567.13}, {"Label": "Power", "Phases": 3, "Amperage": 50, "Power": 19.9, "AmperageUnit": "A", "PowerUnit": "kVA", "Price": 5509.54}, {"Label": "Power +", "Phases": 3, "Amperage": 63, "Power": 25.1, "AmperageUnit": "A", "PowerUnit": "kVA", "Price": 6662.79}, {"Label": "Essentiel", "Phases": 4, "Amperage": 16, "Power": 11.1, "AmperageUnit": "A", "PowerUnit": "kVA", "Price": 971.98}, {"Label": "<PERSON><PERSON>", "Phases": 4, "Amperage": 20, "Power": 13.9, "AmperageUnit": "A", "PowerUnit": "kVA", "Price": 1855.78}, {"Label": "Confort +", "Phases": 4, "Amperage": 25, "Power": 17.3, "AmperageUnit": "A", "PowerUnit": "kVA", "Price": 2567.13}, {"Label": "Power", "Phases": 4, "Amperage": 32, "Power": 22.2, "AmperageUnit": "A", "PowerUnit": "kVA", "Price": 5509.54}, {"Label": "Power +", "Phases": 4, "Amperage": 40, "Power": 27.7, "AmperageUnit": "A", "PowerUnit": "kVA", "Price": 6662.79}, {"Label": "PRO 35", "Phases": 4, "Amperage": 50, "Power": 34.6, "AmperageUnit": "A", "PowerUnit": "kVA", "Price": 8150.16}, {"Label": "PRO 44", "Phases": 4, "Amperage": 63, "Power": 43.6, "AmperageUnit": "A", "PowerUnit": "kVA", "Price": 10090.21}, {"Label": "PRO 55", "Phases": 4, "Amperage": 80, "Power": 55.4, "AmperageUnit": "A", "PowerUnit": "kVA", "Price": 12633.38}, {"Label": "PRO 69", "Phases": 4, "Amperage": 100, "Power": 69.3, "AmperageUnit": "A", "PowerUnit": "kVA", "Price": 18475.45}]}}}, "/vehicules": {"GET": {"id": "WS155", "type": "evdbVehiclesExposition", "function": "vehicles:get_data"}}, "/sharepoint/passthrough/search": {"POST": {"id": "WS194", "type": "passThrough", "URL": "https://{SHP_ONLINE_API}/api/search", "transform": [{"function": "auth:oauth_client_credentials", "type": "request", "params": {"secret": "MyResaAPI/SHP_ONLINE_OAUTH"}}]}}, "/sharepoint/valise/<ValiseUid>": {"GET": {"id": "WS177", "type": "sharepointHandler"}, "POST": {"id": "WS178", "type": "sharepointHandler"}}, "/sharepoint/valise/<ValiseUid>/all": {"GET": {"id": "WS179", "type": "sharepointHandler"}}, "/sharepoint/valise/copy": {"POST": {"id": "WS182", "type": "sharepointHandler"}}, "/sharepoint/valise/<ValiseUid>/<DocumentId>": {"GET": {"id": "WS180", "type": "sharepointHandler"}, "PUT": {"id": "WS181", "type": "sharepointHandler"}}, "/sharepoint/<valise>/metadata": {"GET": {"id": "WS198", "type": "sharepointHandler"}}, "/tad/locality": {"GET": {"id": "WS211", "type": "sqlDriven", "secret": "{IPTelephony_SECRET}", "sql_template": "sqlStatements/tad/ws211_get_locality.sql", "params": {"PostalCode": {"source": "queryStringParameters", "name": "PostalCode", "validation": {"type": "int"}}}, "return": {"type": "object"}}}, "/tad/redirection": {"GET": {"id": "WS212", "type": "sqlDriven", "secret": "{IPTelephony_SECRET}", "sql_template": "sqlStatements/tad/ws212_get_redirection.sql", "return": {"type": "object"}}}, "tasks": {"check_alert": {"type": "sqlDriven", "secret": "{REDSHIFT_SECRET}", "controller": "Alerte<PERSON><PERSON>o", "sql_template": "sqlStatements/alerte_conso.sql"}, "check_bilan": {"type": "sqlDriven", "secret": "{REDSHIFT_SECRET}", "controller": "BilanConso", "sql_template": "sqlStatements/bilan_conso.sql"}, "export_ean_data-fetch_raw_data_all_ean": {"type": "sqlDriven", "secret": "{REDSHIFT_SECRET}", "controller": "ExportEanData", "sql_template": "sqlStatements/ws205_get_export_conso.sql"}, "export_ean_data-raw_data_to_csv_by_ean": {"type": "simpleCompute", "function": "export_ean_data:raw_data_to_csv_by_ean"}, "export_ean_data-zip_and_notify": {"type": "simpleCompute", "function": "export_ean_data:zip_and_notify"}}}