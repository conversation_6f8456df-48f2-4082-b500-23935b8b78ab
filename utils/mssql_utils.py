# import pyodbc
import traceback

import pymssql

from utils.log_utils import log_err


def make_connection(db_credentials):
    """
    Ouvre une connexion vers la DB. Credentials fournis via systeme exploitation en DEV
    et via des AWS secrets en PROD.
    :return:
    """

    try:
        conn = pymssql.connect(
            server=db_credentials["ENDPOINT"],
            port=db_credentials["PORT"],
            user=db_credentials["USER"],
            password=db_credentials["PASSWORD"],
            database=db_credentials["DATABASE"],
        )
        return conn
    except Exception as e:
        print(e)
        errmsg = "ERROR: 500 Request Erreur de connexion avec la base de donnees"
        log_err(traceback.format_exc())
        raise Exception(errmsg)
