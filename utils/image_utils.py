import img2pdf
from wand.color import Color
from wand.image import Image


def convert_image_to_pdf(image_data: bytes) -> bytes:
    new_image_data = remove_alpha(image_data)
    return img2pdf.convert(new_image_data)


def remove_alpha(image_data: bytes) -> bytes:
    with Image(blob=image_data) as img:
        if not img.alpha_channel:
            return image_data
        else:
            img.background_color = Color("white")
            img.alpha_channel = "remove"
            img.alpha_channel = False
            return img.make_blob()
