from dataclasses import dataclass
from typing import List

from utils.string_utils import ifstrip

u_codes = {
    "U00": {
        "Desc": "Simple tarif",
        "Operands": [{"input": "EY_EV_ATH", "output": "EnergieTh"}],
    },
    "U10": {
        "Desc": "Simple tarif",
        "Operands": [{"input": "EY_EV_ATH", "output": "EnergieTh"}],
    },
    "U13": {
        "Desc": "Simple tarif",
        "Operands": [{"input": "EY_EV_ATH", "output": "EnergieTh"}],
    },
    "U20": {
        "Desc": "Simple tarif + Chauffage(Exclusif nuit)",
        "Operands": [
            {"input": "EY_EV_ATH", "output": "EnergieTh"},
            {"input": "EY_EV_AEX", "output": "EnergieActExn"},
        ],
    },
    "U26": {
        "Desc": "Chauffage(Exclusif nuit)",
        "Operands": [{"input": "EY_EV_AEX", "output": "EnergieActExn"}],
    },
    "U30": {
        "Desc": "Simple tarif",
        "Operands": [{"input": "EY_EV_ATH", "output": "EnergieTh"}],
    },
    "U31": {
        "Desc": "Simple tarif",
        "Operands": [{"input": "EY_EV_ATH", "output": "EnergieTh"}],
    },
    "U33": {
        "Desc": "Simple tarif",
        "Operands": [{"input": "EY_EV_ATH", "output": "EnergieTh"}],
    },
    "U40": {
        "Desc": "Double tarif",
        "Operands": [
            {"input": "EY_EV_AHI", "output": "EnergieActHi"},
            {"input": "EY_EV_ALO", "output": "EnergieActLo"},
        ],
    },
    "U50": {
        "Desc": "Double tarif + chauffage (Exclusif nuit)",
        "Operands": [
            {"input": "EY_EV_AHI", "output": "EnergieActHi"},
            {"input": "EY_EV_ALO", "output": "EnergieActLo"},
            {"input": "EY_EV_AEX", "output": "EnergieActExn"},
        ],
    },
    "U60": {
        "Desc": "Double tarif",
        "Operands": [
            {"input": "EY_EV_AHI", "output": "EnergieActHi"},
            {"input": "EY_EV_ALO", "output": "EnergieActLo"},
        ],
    },
    "U61": {
        "Desc": "Double tarif",
        "Operands": [
            {"input": "EY_EV_AHI", "output": "EnergieActHi"},
            {"input": "EY_EV_ALO", "output": "EnergieActLo"},
        ],
    },
    "U62": {
        "Desc": "Double tarif",
        "Operands": [
            {"input": "EY_EV_AHI", "output": "EnergieActHi"},
            {"input": "EY_EV_ALO", "output": "EnergieActLo"},
        ],
    },
    "U63": {
        "Desc": "Double tarif",
        "Operands": [
            {"input": "EY_EV_AHI", "output": "EnergieActHi"},
            {"input": "EY_EV_ALO", "output": "EnergieActLo"},
        ],
    },
    "U69": {
        "Desc": "Double tarif + chauffage (Exclusif nuit)",
        "Operands": [
            {"input": "EY_EV_AHI", "output": "EnergieActHi"},
            {"input": "EY_EV_ALO", "output": "EnergieActLo"},
            {"input": "EY_EV_AEX", "output": "EnergieActExn"},
        ],
    },
    "U71": {
        "Desc": "Double tarif",
        "Operands": [
            {"input": "EY_EV_AHI", "output": "EnergieActHi"},
            {"input": "EY_EV_ALO", "output": "EnergieActLo"},
        ],
    },
    "U72": {
        "Desc": "Double tarif",
        "Operands": [
            {"input": "EY_EV_AHI", "output": "EnergieActHi"},
            {"input": "EY_EV_ALO", "output": "EnergieActLo"},
        ],
    },
    "U73": {
        "Desc": "Double tarif",
        "Operands": [
            {"input": "EY_EV_AHI", "output": "EnergieActHi"},
            {"input": "EY_EV_ALO", "output": "EnergieActLo"},
        ],
    },
    "U80": {
        "Desc": "Double tarif + tarif en pointe",
        "Operands": [
            {"input": "EY_EV_AHI", "output": "EnergieActHi"},
            {"input": "EY_EV_ALO", "output": "EnergieActLo"},
            {"input": "EY_EV_APE", "output": "EnergieActPe"},
        ],
    },
    "U90": {
        "Desc": "Double tarif + tarif en pointe",
        "Operands": [
            {"input": "EY_EV_AHI", "output": "EnergieActHi"},
            {"input": "EY_EV_ALO", "output": "EnergieActLo"},
            {"input": "EY_EV_APE", "output": "EnergieActPe"},
        ],
    },
    "U91": {
        "Desc": "Double tarif + tarif en pointe",
        "Operands": [
            {"input": "EY_EV_AHI", "output": "EnergieActHi"},
            {"input": "EY_EV_ALO", "output": "EnergieActLo"},
            {"input": "EY_EV_APE", "output": "EnergieActPe"},
        ],
    },
    "U93": {
        "Desc": "Double tarif + tarif en pointe",
        "Operands": [
            {"input": "EY_EV_AHI", "output": "EnergieActHi"},
            {"input": "EY_EV_ALO", "output": "EnergieActLo"},
            {"input": "EY_EV_APE", "output": "EnergieActPe"},
        ],
    },
    "U96": {
        "Desc": "Simple tarif + Double tarif",
        "Operands": [
            {"input": "EY_EV_ATH", "output": "EnergieTh"},
            {"input": "EY_EV_AHI", "output": "EnergieActHi"},
            {"input": "EY_EV_ALO", "output": "EnergieActLo"},
        ],
    },
    "U98": {
        "Desc": "Double tarif + tarif en pointe",
        "Operands": [
            {"input": "EY_EV_AHI", "output": "EnergieActHi"},
            {"input": "EY_EV_ALO", "output": "EnergieActLo"},
            {"input": "EY_EV_APE", "output": "EnergieActPe"},
        ],
    },
    "U102": {
        "Desc": "Trihoraire",
        "Operands": [
            {"input": "EY_EV_ANH", "output": "EnergieActNp"},
            {"input": "EY_EV_ANL", "output": "EnergieActNplok"},
            {"input": "EY_EV_APH", "output": "EnergieActPeHi"},
            {"input": "EY_EV_APL", "output": "EnergieActPeLo"},
        ],
    },
    "U104": {
        "Desc": "Double tarif + chauffage(Effacement Heures Pointe)",
        "Operands": [
            {"input": "EY_EV_AHI", "output": "EnergieActHi"},
            {"input": "EY_EV_ALO", "output": "EnergieActLo"},
            {"input": "EY_EV_ANP", "output": "EnergieActNp"},
        ],
    },
    "U106": {
        "Desc": "Simple tarif + chauffage(Effacement Heures Pointe)",
        "Operands": [
            {"input": "EY_EV_ATH", "output": "EnergieTh"},
            {"input": "EY_EV_ANP", "output": "EnergieActEhp"},
        ],
    },
    "U107": {
        "Desc": "Chauffage(Effacement Heures Pointe)",
        "Operands": [{"input": "EY_EV_ANP", "output": "EnergieActNp"}],
    },
    "Gaz": {
        "Desc": "Simple tarif",
        "Operands": [{"input": "GY_EV_TH", "output": "EnergieTh"}],
    },
}


@dataclass
class UCode:
    """
    A class for creating and managing UCode objects.

    Parameters
    ----------
    code : str
        The UCode code.

    Attributes
    ----------
    code : str
        The UCode code.
    desc : str
        The description of the UCode.
    operands : List[dict]
        The list of operands for the UCode.

    """

    code: str
    desc: str
    operands: List[dict]

    def __init__(self, code: str):
        """
        Constructs all the necessary attributes for the UCode object.

        Parameters
        ----------
        code : str
            The UCode code.

        """
        self.code = ifstrip(code)
        self.desc = u_codes.get(self.code, {}).get("Desc", "")
        self.operands = u_codes.get(self.code, {}).get("Operands", [])
