import distutils
import inspect
import json
import os
import shutil
import tempfile
from decimal import Decimal
from distutils.dir_util import copy_tree
from functools import wraps
from io import StringIO
from multiprocessing import Process
from pathlib import Path
from sys import gettrace
from time import sleep
from types import SimpleNamespace
from typing import Callable, Type
from unittest import mock
from unittest.mock import Magic<PERSON><PERSON>, Mock, patch
from urllib.parse import urlparse
from uuid import uuid4

import boto3
from flask import Request
from moto import mock_dynamodb, mock_s3, mock_secretsmanager, mock_sqs
from requests import Response

import get_ad_token
from tools.local_flask.gateway import call_with_aws_event, start_local_flask
from utils.aws_utils import (
    get_dynamodb_table,
    get_resource_key,
    get_secret_string,
    load_s3_file,
    scanAll,
    write_secret,
)
from utils.DecimalEncoder import DecimalEncoder

# Use tempfile.mkdtemp for safer temporary directory creation
mock_temp_dir = tempfile.mkdtemp(prefix='MyResa_mock_')
mock_file_path = os.path.join(mock_temp_dir, str(uuid4()))
mock_file_path_in_tmp = os.path.relpath(mock_file_path, tempfile.gettempdir())
dumped_data = False


def mock_environnement(function):
    global dumped_data
    mock_environ = {
        "AWS_ACCESS_KEY_ID": "testing",
        "AWS_SECRET_ACCESS_KEY": "testing",
        "AWS_SECURITY_TOKEN": "testing",
        "AWS_SESSION_TOKEN": "testing",
        "USE_MOCK": "true",
        "DYNAMODB": "MyResaUser_mock",
        "BUCKET": "mock_s3_bucket",
        "ENV_MESSAGE_LOG_BUCKET": "mock_s3_bucket_mesage_log",
        "BornesRechargeTable": "mock_BornesRechargeTable",
        "MAPPING_BUCKET_NAME": "mock_s3_bucket",
        "DYNAMODB_TOKEN_INVALIDATION": "TokenInvalidation_mock",
        "CACHE_BASE_DIR": mock_file_path,
    }

    if not dumped_data:
        # Remove local cache
        try:
            tmp_safe_path = os.path.join(tempfile.gettempdir(), mock_file_path_in_tmp)
            if os.path.commonpath([tmp_safe_path]) == os.path.commonpath([tmp_safe_path, mock_file_path]):
                shutil.rmtree(tmp_safe_path)
            distutils.dir_util._path_created = {}
        except FileNotFoundError:
            pass

        # Set local files in cache
        resources_safe_path = os.path.join(tempfile.gettempdir(), mock_file_path_in_tmp, "s3", mock_environ["BUCKET"], "resources")
        version_safe_path = os.path.join(tempfile.gettempdir(), mock_file_path_in_tmp, "s3", mock_environ["BUCKET"], "resources", os.environ["API_VERSION"])
        if os.path.commonpath([tempfile.gettempdir()]) == os.path.commonpath([tempfile.gettempdir(), version_safe_path]):
            Path(version_safe_path).mkdir(parents=True, exist_ok=True)
            copy_tree("./resources", version_safe_path)

        # Copy AD publickey
        with open(f"{resources_safe_path}/AD_publickey.json", "w") as ad_key_file:
            ad_key = load_s3_file(os.environ["BUCKET"], "resources/AD_publickey.json", no_cache=True)
            ad_key_file.write(ad_key)

        save_ressources()
        dumped_data = True

    @mock_s3
    @mock_dynamodb
    @mock_secretsmanager
    @mock_sqs
    def with_mock(*args, **kwargs):
        with mock.patch.dict("os.environ", {**os.environ, **mock_environ}, clear=True):
            create_environment()
            load_ressources()
            return function(*args, **kwargs)

    return with_mock


def save_ressources():
    Path(mock_file_path).mkdir(parents=True, exist_ok=True)
    with open(mock_file_path + "/mock_dump.json", "w") as mock_dump_file:
        mock_dump = {"SecretManager": {}, "DynamoDB": {}}

        def _copy_secret(secret_name):
            mock_dump["SecretManager"][secret_name] = get_secret_string(secret_name)

        def _copy_dynamo_table(table_name):
            mock_dump["DynamoDB"][table_name] = scanAll(table_name)

        _copy_secret("MyResaAPI/ADFS/dev")

        _copy_dynamo_table(f"WS68_TEMPLATE_{os.environ.get('STAGE_TAG')}")

        mock_dump_file.write(json.dumps(mock_dump, cls=DecimalEncoder))


def load_ressources():
    with open(mock_file_path + "/mock_dump.json") as mock_dump_file:
        mock_dump = json.loads(mock_dump_file.read())

        write_secret("MyResaAPI/Sharepoint/QTA", '{"host":"mock","port":"mock","path":"mock","env":"QTA-300"}')
        write_secret("MyResaApi/sendinblueApiKey", "mock")
        write_secret("MyResaAPI/AWS/BasicAuthPassword", '{"DecodedPassword":"mock","DecodedUsername":"mock"}')
        write_secret("MyResaAPI/SAP_User/BasicAuthPassword", '{"DecodedPassword":"mock","DecodedUsername":"mock"}')
        write_secret("MyResaWeb/ADFS/Qual", '{"client_id":"mock","redirect_domain":"mock"}')
        write_secret("MyResaAPI/SHP_ONLINE_OAUTH", '{"AccessTokenURL":"mock","ClientID":"mock","ClientSecret":"mock","Scope":"mock"}')
        write_secret("SAP_HANA_QTA", "{}")
        write_secret("MyResaAPI/ApiKey", "mock")

        for key, value in mock_dump["SecretManager"].items():
            write_secret(key, value)

        for key, value in mock_dump["DynamoDB"].items():
            table = get_dynamodb_table(key)
            with table.batch_writer() as batch:
                for item in value:
                    batch.put_item(Item=item)


samples = {
    "ghost_user": {
        "uid": "ghost_123456789",
        "valide": False,
        "ean": [],
        "preferences": {},
        "dossiers": [],
        "pannes": [],
        "notifications": [],
    },
    "activated_user": {
        "uid": "Acc.Test.Dae9z",
        "adresse": {
            "Cdpostal": "4367",
            "CodePays": "BE",
            "Localite": "CRISNEE",
            "NumRue": "29",
            "Rue": "Rue Nestor Royer",
        },
        "bp": 3100487064,
        "Commune": None,
        "contact_email": "<EMAIL>",
        "createdAt": 1680081563183,
        "dossiers": [{"id": "2109427"}],
        "consentements": [
            {"DateTime": Decimal("1740652633.352982997894287109375"), "Key": "smartPortal.benchmarking", "Value": True},
            {"DateTime": Decimal("1740652633.352982997894287109375"), "Key": "smartPortal.acceptance", "Value": False},
            {"DateTime": Decimal("1740652702.3942821025848388671875"), "Key": "smartPortal.benchmarking", "Value": True},
            {"DateTime": Decimal("1740652702.3942821025848388671875"), "Key": "smartPortal.acceptance", "Value": False},
            {"DateTime": Decimal("1740652803.533812999725341796875"), "Key": "smartPortal.benchmarking", "Value": True},
            {"DateTime": Decimal("1740652803.533812999725341796875"), "Key": "smartPortal.acceptance", "Value": False},
        ],
        "ean": [
            {
                "contract": [
                    {"ctr_from": "20230929", "ctr_to": "99991231", "num_ctr": 51004090541},
                    {"ctr_from": "20220601", "ctr_to": "20230928", "num_ctr": 51003720418},
                    {"ctr_from": "20210601", "ctr_to": "20220601", "num_ctr": 51003720419},
                ],
                "ean": "541456700002569958",
                "energy": "01",
                "meterid": ["1SAG1105183360", "000000000033928477"],
                "address_hash": "testhash",
            },
            {
                "contract": [
                    {"ctr_from": "20230929", "ctr_to": "99991231", "num_ctr": 51004090541},
                    {"ctr_from": "20220601", "ctr_to": "20230928", "num_ctr": 51003720418},
                    {"ctr_from": "20210601", "ctr_to": "20220601", "num_ctr": 51003720419},
                ],
                "ean": "541460900001145396",
                "energy": "01",
                "meterid": ["1SAG1105183360", "000000000033928477"],
                "address_hash": "testhash",
            },
            {
                "contract": [
                    {"ctr_from": "20230929", "ctr_to": "99991231", "num_ctr": 51004090541},
                    {"ctr_from": "20220601", "ctr_to": "20230928", "num_ctr": 51003720418},
                    {"ctr_from": "20210601", "ctr_to": "20220601", "num_ctr": 51003720419},
                ],
                "ean": "541456700002609883",
                "energy": "01",
                "meterid": ["1SAG1105183360", "000000000033928477"],
                "address_hash": "testhash2",
            },
        ],
        "email": "<EMAIL>",
        "password": "Welcome@2023",
        "firstname": "Denis",
        "ItsMeSub": "s2e02whnw6leungx6eantnst434sdrrgxajb",
        "ItsMeToken": "*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
        "itsme_sub": "test_sub",
        "lastname": "Francotte",
        "last_login": "2023-12-08T14:44:11.291098",
        "launched_activation": True,
        "master": None,
        "phone": "+32496123456",
        "phone_fixe": None,
        "ppp": False,
        "preferences": {
            "bp_connecte": True,
            "bp_origine": "RGP",
            "com_conf_paiement_mail": True,
            "com_conf_paiement_postal": True,
            "com_conf_paiement_sms": True,
            "com_conf_sepa_mail": True,
            "com_conf_sepa_postal": True,
            "com_conf_sepa_sms": True,
            "com_dossier_racc_mail": True,
            "com_dossier_racc_postal": True,
            "com_dossier_racc_sms": True,
            "com_encod_index_mail": True,
            "com_encod_index_postal": True,
            "com_encod_index_sms": True,
            "com_global": "mail",
            "com_marketing_mail": True,
            "com_panne_evo_mail": True,
            "com_panne_evo_sms": True,
            "com_panne_fin_mail": True,
            "com_panne_fin_sms": True,
            "com_panne_mail": True,
            "com_panne_sms": True,
            "com_pass_index_mail": True,
            "com_pass_index_postal": True,
            "com_pass_index_sms": True,
            "com_ppp_mail": True,
            "com_ppp_sms": True,
            "com_rappel_paiement_mail": True,
            "com_rappel_paiement_postal": True,
            "com_rappel_paiement_sms": True,
            "com_smartportal_alert_mail": True,
            "com_smartportal_alert_sms": True,
            "com_smartportal_bilan_mail": True,
            "com_smartportal_invitation_mail": True,
            "com_smartportal_mail": True,
            "com_smartportal_sms": True,
            "condgen": True,
            "langue": "FR",
        },
        "smart_portal": True,
        "sms_validation": [
            {
                "code": "634506",
                "expire": "2023-03-29T09:36:21.739620",
                "phone": "+32496908712",
                "sent": "2023-03-29T09:26:21.739647",
                "type": "sendSms",
            },
            {
                "code": "615242",
                "expire": "2023-05-17T07:26:26.292831",
                "phone": "+32496908712",
                "sent": "2023-05-17T07:16:26.292856",
                "type": "sendSms",
            },
        ],
        "sms_validation_last_try": "2023-05-17T07:16:41.400208",
        "valide": True,
        "valid_contact_email": True,
        "valid_phone": True,
    },
    "commune_user": {
        "password": "Welcome@2023",
        "uid": "Lie.ResaAdmin.7XWQF",
        "adresse": {"Cdpostal": "0000", "CodePays": "BE", "Localite": "", "NumRue": "0", "Rue": ""},
        "bp": 3101239234,
        "Commune": {
            "Actif": True,
            "Admin": True,
            "CodePostaux": ["4030", "4032", "4000", "4430", "4031", "4020"],
            "Departement": "Depart.",
            "Fonction": "Fonct.",
            "Id": "62063",
            "Localite": "Liège",
        },
        "commune_id": "62063",
        "contact_email": "<EMAIL>",
        "createdAt": 1705670802592,
        "dossiers": [],
        "ean": [],
        "email": "<EMAIL>",
        "firstname": "Liege",
        "lastname": "ResaAdmin",
        "last_login": "2024-07-08T12:21:31.058585",
        "launched_activation": True,
        "master": None,
        "notifications": [],
        "pannes": [],
        "phone_fixe": None,
        "ppp": False,
        "preferences": {
            "bp_commune": True,
            "bp_connecte": True,
            "bp_origine": "MyRESA",
            "com_conf_paiement_mail": False,
            "com_conf_paiement_postal": False,
            "com_conf_paiement_sms": False,
            "com_conf_sepa_mail": False,
            "com_conf_sepa_postal": False,
            "com_conf_sepa_sms": False,
            "com_dossier_racc_mail": True,
            "com_dossier_racc_postal": False,
            "com_dossier_racc_sms": False,
            "com_encod_index_mail": False,
            "com_encod_index_postal": False,
            "com_encod_index_sms": False,
            "com_global": "mail",
            "com_marketing_mail": False,
            "com_panne_evo_mail": False,
            "com_panne_evo_sms": False,
            "com_panne_fin_mail": False,
            "com_panne_fin_sms": False,
            "com_panne_mail": False,
            "com_panne_sms": False,
            "com_pass_index_mail": False,
            "com_pass_index_postal": False,
            "com_pass_index_sms": False,
            "com_ppp_mail": True,
            "com_ppp_sms": True,
            "com_rappel_paiement_mail": False,
            "com_rappel_paiement_postal": False,
            "com_rappel_paiement_sms": False,
            "com_smartportal_alert_mail": True,
            "com_smartportal_alert_sms": True,
            "com_smartportal_bilan_mail": True,
            "com_smartportal_invitation_mail": False,
            "condgen": False,
            "langue": "FR",
        },
        "smart_portal": False,
        "sms_validation": [{"code": "995765", "expire": "2024-07-08T10:06:38.777542", "phone": "+32499123456", "sent": "2024-07-08T09:56:38.777565", "type": "sendSms"}],
        "valide": True,
        "valid_contact_email": True,
        "valid_phone": False,
    },
}


def create_environment():
    boto3.client("dynamodb").create_table(
        TableName=os.environ["DYNAMODB"],
        AttributeDefinitions=[
            {"AttributeName": "uid", "AttributeType": "S"},
            {"AttributeName": "email", "AttributeType": "S"},
        ],
        KeySchema=[
            {"AttributeName": "uid", "KeyType": "HASH"},
        ],
        ProvisionedThroughput={"ReadCapacityUnits": 10, "WriteCapacityUnits": 10},
        GlobalSecondaryIndexes=[
            {
                "IndexName": "email-index",
                "KeySchema": [
                    {"AttributeName": "email", "KeyType": "HASH"},
                ],
                "Projection": {"ProjectionType": "ALL"},
                "ProvisionedThroughput": {
                    "ReadCapacityUnits": 10,
                    "WriteCapacityUnits": 10,
                },
            },
        ],
    )
    user_table = boto3.resource("dynamodb").Table(os.environ["DYNAMODB"])
    for elem in samples.values():
        user_table.put_item(Item=elem)
    boto3.resource("dynamodb").create_table(
        TableName=os.environ["BornesRechargeTable"],
        KeySchema=[
            {"AttributeName": "Ean", "KeyType": "HASH"},
            {"AttributeName": "DateCreation", "KeyType": "RANGE"},
        ],
        AttributeDefinitions=[
            {"AttributeName": "Ean", "AttributeType": "S"},
            {"AttributeName": "DateCreation", "AttributeType": "S"},
            {"AttributeName": "Uuid", "AttributeType": "S"},
        ],
        ProvisionedThroughput={"ReadCapacityUnits": 10, "WriteCapacityUnits": 10},
        GlobalSecondaryIndexes=[
            {
                "IndexName": "uuid-index",
                "KeySchema": [
                    {"AttributeName": "Uuid", "KeyType": "HASH"},
                ],
                "Projection": {"ProjectionType": "ALL"},
                "ProvisionedThroughput": {
                    "ReadCapacityUnits": 10,
                    "WriteCapacityUnits": 10,
                },
            },
        ],
    )
    boto3.client("dynamodb").create_table(
        TableName=os.environ["DYNAMODB_TOKEN_INVALIDATION"],
        AttributeDefinitions=[
            {"AttributeName": "token", "AttributeType": "S"},
        ],
        KeySchema=[
            {"AttributeName": "token", "KeyType": "HASH"},
        ],
        ProvisionedThroughput={"ReadCapacityUnits": 10, "WriteCapacityUnits": 10},
    )

    boto3.client("dynamodb").create_table(
        TableName=f"UserVehicles_{os.environ['STAGE_TAG']}",
        AttributeDefinitions=[
            {"AttributeName": "UUID", "AttributeType": "S"},
            {"AttributeName": "UID", "AttributeType": "S"},
        ],
        KeySchema=[
            {"AttributeName": "UUID", "KeyType": "HASH"},
        ],
        ProvisionedThroughput={"ReadCapacityUnits": 10, "WriteCapacityUnits": 10},
        GlobalSecondaryIndexes=[
            {
                "IndexName": "UID-index",
                "KeySchema": [
                    {"AttributeName": "UID", "KeyType": "HASH"},
                ],
                "Projection": {"ProjectionType": "ALL"},
                "ProvisionedThroughput": {
                    "ReadCapacityUnits": 10,
                    "WriteCapacityUnits": 10,
                },
            },
        ],
    )

    boto3.client("dynamodb").create_table(
        TableName=f"WS68_TEMPLATE_{os.environ.get('STAGE_TAG')}",
        AttributeDefinitions=[{"AttributeName": "Id", "AttributeType": "S"}],
        KeySchema=[
            {"AttributeName": "Id", "KeyType": "HASH"},
        ],
        ProvisionedThroughput={"ReadCapacityUnits": 10, "WriteCapacityUnits": 10},
    )
    boto3.client("dynamodb").create_table(
        TableName=f"MyResaAPI_MISC_{os.environ['STAGE_TAG']}",
        AttributeDefinitions=[{"AttributeName": "Key", "AttributeType": "S"}],
        KeySchema=[
            {"AttributeName": "Key", "KeyType": "HASH"},
        ],
        ProvisionedThroughput={"ReadCapacityUnits": 10, "WriteCapacityUnits": 10},
    )

    boto3.client("dynamodb").create_table(
        TableName=f"MollieStatus_{os.environ.get('STAGE_TAG')}",
        AttributeDefinitions=[
            {"AttributeName": "payment_id", "AttributeType": "S"},
            {"AttributeName": "order_id", "AttributeType": "S"},
        ],
        KeySchema=[
            {"AttributeName": "payment_id", "KeyType": "HASH"},
        ],
        ProvisionedThroughput={"ReadCapacityUnits": 10, "WriteCapacityUnits": 10},
        GlobalSecondaryIndexes=[
            {
                "IndexName": "order_id-index",
                "KeySchema": [
                    {"AttributeName": "order_id", "KeyType": "HASH"},
                ],
                "Projection": {"ProjectionType": "ALL"},
                "ProvisionedThroughput": {
                    "ReadCapacityUnits": 10,
                    "WriteCapacityUnits": 10,
                },
            },
        ],
    )

    boto3.client("dynamodb").create_table(
        TableName=os.environ["SmartConsoAlerts"],
        AttributeDefinitions=[
            {"AttributeName": "Uid", "AttributeType": "S"},
            {"AttributeName": "Meter", "AttributeType": "S"},
        ],
        KeySchema=[
            {"AttributeName": "Uid", "KeyType": "HASH"},
        ],
        ProvisionedThroughput={"ReadCapacityUnits": 10, "WriteCapacityUnits": 10},
        GlobalSecondaryIndexes=[
            {
                "IndexName": "Meter-index",
                "KeySchema": [
                    {"AttributeName": "Meter", "KeyType": "HASH"},
                ],
                "Projection": {"ProjectionType": "ALL"},
                "ProvisionedThroughput": {
                    "ReadCapacityUnits": 10,
                    "WriteCapacityUnits": 10,
                },
            },
        ],
    )
    boto3.client("dynamodb").create_table(
        TableName=os.environ["UserEnergyProfiles"],
        AttributeDefinitions=[
            {"AttributeName": "Uid", "AttributeType": "S"},
            {"AttributeName": "HashAddress", "AttributeType": "S"},
        ],
        KeySchema=[
            {"AttributeName": "Uid", "KeyType": "HASH"},
        ],
        ProvisionedThroughput={"ReadCapacityUnits": 10, "WriteCapacityUnits": 10},
        GlobalSecondaryIndexes=[
            {
                "IndexName": "HashAddress-index",
                "KeySchema": [
                    {"AttributeName": "HashAddress", "KeyType": "HASH"},
                ],
                "Projection": {"ProjectionType": "ALL"},
                "ProvisionedThroughput": {
                    "ReadCapacityUnits": 10,
                    "WriteCapacityUnits": 10,
                },
            },
        ],
    )
    boto3.client("s3").create_bucket(
        Bucket=os.environ["BUCKET"],
        CreateBucketConfiguration={"LocationConstraint": "eu-west-1"},
    )
    boto3.client("s3").create_bucket(
        Bucket="mock_s3_bucket-temp-storage",
        CreateBucketConfiguration={"LocationConstraint": "eu-west-1"},
    )
    boto3.client("s3").create_bucket(
        Bucket=os.environ["ENV_MESSAGE_LOG_BUCKET"],
        CreateBucketConfiguration={"LocationConstraint": "eu-west-1"},
    )

    session = boto3.session.Session()
    client = session.client(service_name="secretsmanager", region_name="eu-west-1").create_secret(
        Name="MyResaAPI/ActiveDirectory",
        SecretString=json.dumps({"host": "localhost", "port": 636, "login": "login", "password": "password"}),
    )
    sqs = boto3.client("sqs").create_queue(QueueName="MockSQS_userSync")
    os.environ["USER_SYNC_SQS_URL"] = sqs["QueueUrl"]


def _get_mock_user_token(mock_user: dict):
    resp = get_ad_token.handler(
        {
            "body": f'{{"Username": "{mock_user["email"]}", "Password": "{mock_user["password"]}"}}',
            "headers": {},
        },
        None,
    )
    content = json.loads(resp.get("body", "{}"))
    return content.get("IdToken")


def get_mock_user_token():
    return _get_mock_user_token(samples["activated_user"])


def get_mock_commune_user_token():
    return _get_mock_user_token(samples["commune_user"])


def _mock_lambda_client(*args, **kwargs):
    if args[0] == "lambda":

        def invoke_fun(*args, **kwargs):
            result = {}

            if kwargs.get("FunctionName") == os.environ.get("HTML_TO_PDF_LAMBDA"):
                result = __import__("transformHtmlToPdf").handler(json.loads(kwargs.get("Payload")), None)
            elif kwargs.get("FunctionName") == os.environ.get("PASSTHROUGH_LAMBDA_ARN"):
                result = {"statusCode": 200, "body": ""}

            return {"Payload": StringIO(json.dumps(result))}

        return SimpleNamespace(invoke=invoke_fun)
    else:
        return boto3._get_default_session().client(*args, **kwargs)


@mock_environnement
@patch("sib_api_v3_sdk.Configuration", new=MagicMock())
@patch("sib_api_v3_sdk.TransactionalEmailsApi", new=Mock(return_value=Mock(get_smtp_template=Mock(return_value=Mock(html_content="<html>template</html>")))))
@patch("sib_api_v3_sdk.ApiClient", new=Mock())
@patch("sib_api_v3_sdk.SendSmtpEmail", new=Mock())
@patch("utils.db_connector.make_connection", new=Mock())
@patch(
    "utils.message_utils.send_sms",
    new=Mock(return_value={"SmsStatusCode": 200, "Phone": "+321654987"}),
)
@patch(
    "compte.send_in_blue_mail",
    new=Mock(return_value={"EmailStatusCode": 200, "Email": "<EMAIL>"}),
)
@patch(
    "compte.send_sms",
    new=Mock(return_value={"SmsStatusCode": 200, "Phone": "+321654987"}),
)
@patch("boto3.client", new=Mock(side_effect=_mock_lambda_client))
def launch_test_api(port=8011):
    # Start local API with flask to receive request
    flask = Process(target=start_local_flask, args=(port, "./resources"))
    flask.start()
    if gettrace():
        # the process that launch the test API need more time to be up when running on debug
        print("Hmm, Big Debugger is watching me")
        sleep(4)
    else:
        sleep(1)
    return flask


#  -------
endpoint_dict = {}


def mock_endpoint_route():
    global endpoint_dict

    if not endpoint_dict:
        # Configure routes based on linking.json file
        linking = json.loads(load_s3_file(os.environ["BUCKET"], get_resource_key("linking.json")))

        for endpoint, methods in linking.items():
            for method, config in methods.items():
                method = method.upper()
                endpoint = endpoint.replace("{", "<").replace("}", ">")
                path = endpoint.split("/")[1:]

                current_path = endpoint_dict
                for path_part in path:
                    if not current_path.get(path_part):
                        current_path[path_part] = {}
                    current_path = current_path[path_part]

                current_path[method] = {"handler": __import__(config["type"]).handler, "endpoint": endpoint}

    return endpoint_dict


def mock_request_call(*args, **kwargs):
    method = args[0].upper()
    url = urlparse(args[1])
    path = url.path.split("/")[1:]
    url_query = kwargs["params"]
    path_var = {}

    for pair in url.query.split("&"):
        if "=" in pair:
            k, v = pair.split("=")
            url_query[k] = v

    current_path = mock_endpoint_route()
    for path_part in path:
        if current_path.get(path_part):
            current_path = current_path[path_part]
        else:
            for path in current_path.keys():
                if path.startswith("<"):
                    path_var[path[1:-1]] = path_part
                    current_path = current_path[path]

    endpoint_config = current_path[method]
    handler = endpoint_config["handler"]

    mock_request = Mock(spec=Request)
    mock_request.method = method
    mock_request.url_rule.rule = endpoint_config["endpoint"]
    mock_request.path = url.path
    mock_request.args.to_dict.return_value = url_query
    mock_request.headers = kwargs["headers"]
    mock_request.host = "localhost"
    mock_request.content_type = mock_request.headers.get("Content-Type")
    mock_request.data = None
    if kwargs["data"]:
        if isinstance(kwargs["data"], str):
            mock_request.data = kwargs["data"].encode()
            if not mock_request.content_type:
                mock_request.content_type = "text/plain"
        if isinstance(kwargs["data"], bytes):
            mock_request.data = kwargs["data"]
            if not mock_request.content_type:
                mock_request.content_type = "application/octet-stream"
    if kwargs["json"]:
        try:
            mock_request.data = json.dumps(kwargs["json"]).encode()
            if not mock_request.content_type:
                mock_request.content_type = "application/json"
        except TypeError:
            pass
    mock_request.body = mock_request.data

    if mock_request.content_type:
        mock_request.headers.setdefault("Content-Type", mock_request.content_type)

    res_body, res_status_code, res_headers = call_with_aws_event(
        request=mock_request,
        handler=handler,
        path_parameters=path_var,
    )

    #  mock request reponse content
    mock_response = Mock(spec=Response)

    # Set up properties of the response object
    mock_response.status_code = res_status_code
    mock_response.headers = res_headers
    mock_response.request = mock_request
    mock_response.text = res_body
    mock_response.content = res_body.encode() if isinstance(res_body, str) else res_body
    mock_response.ok = 200 <= res_status_code < 400
    mock_response.json.side_effect = lambda: json.loads(res_body)

    return mock_response


def mock_api(target: Type | Callable) -> Type | Callable:
    """
    A decorator function for mocking internal flask API call in a target class or
    callable.

    Parameters
    ----------
    target : type or callable
        The class or function to be decorated. If a class is provided, each
        callable attribute will be wrapped with the mocking functionality.

    Returns
    -------
    type or callable
        The target with its dependencies mocked, allowing for isolated and
        repeatable tests.

    Notes
    -----
    The decorator applies a set of predefined mocks to external dependencies that
    are typically used for network communication, database interactions, and
    environment configurations. These mocks include APIs and utilities such as
    SendSmtpEmail, SMS sending functions, AWS clients, and others.

    """

    # Define a wrapper function
    def wrap(func):
        @wraps(func)
        @mock_environnement
        @patch("sib_api_v3_sdk.Configuration", new=MagicMock())
        @patch("sib_api_v3_sdk.TransactionalEmailsApi", new=Mock(return_value=Mock(get_smtp_template=Mock(return_value=Mock(html_content="<html>template</html>")))))
        @patch("sib_api_v3_sdk.ApiClient", new=Mock())
        @patch("sib_api_v3_sdk.SendSmtpEmail", new=Mock())
        @patch("utils.db_connector.make_connection", new=Mock())
        @patch("utils.message_utils.send_sms", new=Mock(return_value={"SmsStatusCode": 200, "Phone": "+321654987"}))
        @patch("compte.send_in_blue_mail", new=Mock(return_value={"EmailStatusCode": 200, "Email": "<EMAIL>"}))
        @patch("compte.send_sms", new=Mock(return_value={"SmsStatusCode": 200, "Phone": "+321654987"}))
        @patch("boto3.client", new=Mock(side_effect=_mock_lambda_client))
        @patch("utils.ldap_utils.LDAP", new=MagicMock())
        @patch("utils.api.request", new=Mock(side_effect=mock_request_call))
        def decorated_func(*args, **kwargs):
            return func(*args, **kwargs)

        return decorated_func

    # Check if the target is a class
    if inspect.isclass(target):
        for attr_name in target.__dict__.keys():
            if not attr_name[0] == "_":  # Don't mock private functions
                attr_value = getattr(target, attr_name)
                if callable(attr_value):
                    # Decorate each method of the class
                    setattr(target, attr_name, wrap(attr_value))
        return target
    else:
        # Assume it's a function
        return wrap(target)
