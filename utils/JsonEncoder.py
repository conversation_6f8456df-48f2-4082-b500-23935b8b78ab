import json
from datetime import datetime
from decimal import Decimal
from enum import Enum

from pydantic import BaseModel


class ResponseEncoder(json.JSONEncoder):
    """
    Custom JSON encoder for handling specific object types.

    This class extends the standard `json.JSONEncoder` to enable
    serialization of custom object types such as `Decimal`, `datetime`,
    and `BaseModel`. It ensures these types are properly converted to
    JSON-serializable formats. The encoder also handles objects with a
    `__dict__` attribute by converting them to their dictionary
    representation.

    """

    def default(self, obj):
        """Override the `default` method of `json.JSONEncoder` to implement custom serialization logic for various object types."""
        if isinstance(obj, Decimal):
            num = float(obj)
            if num.is_integer():
                num = int(obj)
            return num
        if isinstance(obj, datetime):
            return obj.__str__()
        if isinstance(obj, Enum):
            return obj.value
        if isinstance(obj, BaseModel):
            return obj.model_dump()
        if hasattr(obj, "__dict__"):
            return vars(obj)
        return json.JSONEncoder.default(self, obj)
