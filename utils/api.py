import base64
import json
import os
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from contextlib import suppress
from threading import Lock, Thread

from requests import request

from utils.aws_utils import get_secret, get_secret_string
from utils.dict_utils import get
from utils.errors import HttpError
from utils.log_utils import log_info_json


async def call(method, path, params=None, body=None, throw=True):
    if params is None:
        params = {}
    resp = request(method.lower(), os.environ["API_URL"] + path, params=params, data=body)
    print("Call async resp")
    print(resp)
    if resp.status_code < 200 or resp.status_code >= 300:
        if throw:
            raise Exception(f"{method} {path} return status code {resp.status_code}\n{resp.json()}")
        else:
            return None
    return resp.json()


def concurrentCall(futures):
    import asyncio

    threads = []

    with ThreadPoolExecutor() as executor:
        for futur in futures:
            threads.append(executor.submit(lambda: asyncio.run(futur)))

    for task in as_completed(threads):
        yield task.result()


def concurrent_execution(f, iterable, attribute_name, init_iterable=None, **f_params):
    if init_iterable is None:
        init_iterable = []
    result = init_iterable
    with ThreadPoolExecutor() as executor:
        future_result = {executor.submit(f, item, f_params): item for item in iterable}
        for future in as_completed(future_result):
            new_entry = {attribute_name: future_result[future], **future.result()}
            result.append(new_entry)
    return result


def basic_auth_headers(secret="MyResaAPI/SAP_User/BasicAuthPassword"):
    secret_ = get_secret(secret)
    hash_ = base64.standard_b64encode(str.encode(secret_["DecodedUsername"] + ":" + secret_["DecodedPassword"])).decode()
    return {
        "Authorization": f"Basic {hash_}",
        "Accept-Language": get(os.environ, "LANG", "FR", default_on_empty=True),
    }


def api_caller(method, path, params=None, body=None, headers=None, throw=True, propagate=False, raw=False):
    if headers is None:
        headers = {}
    if params is None:
        params = {}
    headers = {
        "Accept-Language": get(os.environ, "LANG", "FR", default_on_empty=True),
        **headers,
    }
    headers.setdefault("X-Api-Key", get_secret_string("MyResaAPI/ApiKey"))
    resp = request(
        method.lower(),
        os.environ["API_URL"] + path,
        params=params,
        data=body if isinstance(body, (str, bytes)) else None,
        json=body if not isinstance(body, str) else None,
        headers=headers,
        verify=os.environ.get("LOCAL") != "true",
        timeout=25,
    )

    request_body = resp.request.body
    with suppress(Exception):
        request_body = json.loads(resp.request.body)

    response_body = resp.content
    with suppress(Exception):
        response_body = json.loads(resp.content)

    log_info_json(
        {
            "request": {
                "method": resp.request.method,
                "url": resp.request.url,
                "headers": resp.request.headers,
                "body": request_body,
            },
            "response": {
                "statuts": resp.status_code,
                "headers": resp.headers,
                "body": response_body,
            },
        },
    )

    if raw:
        return resp
    if not resp.ok:
        if throw:
            resp_error_code = None
            resp_extra = None
            try:
                resp_data = resp.json()
                resp_text = resp_data.get("Message")
                resp_error_code = resp_data.get("ErrorCode")
                resp_extra = resp_data.get("Extra")
            except:
                resp_text = resp.text

            raise HttpError(
                status=resp.status_code,
                message=resp_text,
                headers=resp.headers,
                error_code=resp_error_code,
                extra=resp_extra,
            )
        else:
            return json.loads(resp.content) if propagate else None
    return resp.json()


class APICaller(Thread):
    __lock = Lock()

    def __init__(
        self,
        job_name,
        method,
        path,
        params=None,
        body=None,
        headers=None,
        throw=False,
        propagate=False,
        result_dict=None,
    ):
        Thread.__init__(self)
        if headers is None:
            headers = {}
        if params is None:
            params = {}
        if result_dict is None:
            result_dict = {}
        self.job_name = job_name
        self.method = method
        self.path = path
        self.params = params
        self.body = body
        self.headers = {
            "Accept-Language": get(os.environ, "LANG", "FR", default_on_empty=True),
            **headers,
        }
        self.throw = throw
        self.propagate = propagate
        self.result_dict = result_dict

    def run(self):
        result = api_caller(
            self.method,
            self.path,
            self.params,
            self.body,
            self.headers,
            self.throw,
            self.propagate,
        )
        self.result_dict[self.job_name] = result
