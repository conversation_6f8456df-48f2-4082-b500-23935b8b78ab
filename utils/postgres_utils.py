import traceback

import psycopg2

from utils.log_utils import log_err


def make_connection(db_credentials):
    """
    Ouvre une connexion vers la DB. Credentials fournis via systeme exploitation en DEV
    et via des AWS secrets en PROD.
    :return:
    """

    try:
        conn_str = "host={0} dbname={1} user={2} password={3} port={4}".format(
            db_credentials["ENDPOINT"],
            db_credentials["DATABASE"],
            db_credentials["USER"],
            db_credentials["PASSWORD"],
            db_credentials["PORT"],
        )
        conn = psycopg2.connect(conn_str)
        conn.autocommit = True
        return conn
    except Exception:
        errmsg = "ERROR: 500 Request Erreur de connexion avec la base de donnees"
        log_err(traceback.format_exc())
        raise Exception(errmsg)
