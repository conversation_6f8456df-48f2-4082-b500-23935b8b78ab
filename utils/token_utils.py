import os
from datetime import timed<PERSON><PERSON>, datetime

import boto3
from boto3.dynamodb.conditions import Key
from jose import jws, jwt
from jose.exceptions import J<PERSON><PERSON>rror, JWTError

from utils.aws_utils import get_secret_string, get_dynamodb_table
from utils.dict_utils import get
from utils.errors import UnauthorizedError
from utils.log_utils import log_err


def removeTokenType(authorizationHeader, type="Bearer"):
    if not authorizationHeader.startswith(type):
        raise UnauthorizedError("Token should start with `{}`".format(type))
    return authorizationHeader[len(type) + 1 :]


def getToken(headers, type="Bearer"):
    header = get(headers or {}, "Authorization")
    return removeTokenType(header, type) if header else None


def create_token(userMail):
    encoded = jwt.encode({"user": userMail}, "secretTokenResa", algorithm="HS256")
    return encoded


def checkForInvalidation(token):
    token_invalidation_table = get_dynamodb_table(os.environ["DYNAMODB_TOKEN_INVALIDATION"])
    invalid = token_invalidation_table.query(
        KeyConditionExpression=Key("token").eq(token),
    )["Items"]
    if invalid:
        raise UnauthorizedError("Token is revoked", error_code="TOKEN_REVOKED")


def createTokenInvalidation(token, validity=30 * 24 * 60 * 60):
    token_invalidation_table = get_dynamodb_table(os.environ["DYNAMODB_TOKEN_INVALIDATION"])
    token_invalidation_table.put_item(
        Item={
            "token": token,
            "TTL": int((datetime.now() + timedelta(seconds=validity)).timestamp()),
        }
    )


def verify_token(token, key):
    try:
        jws.verify(token, key, algorithms=["RS256"])
        return True
    except JWSError as e:
        log_err(e)
        return False


def decode_token(token):
    try:
        options = {
            "verify_signature": False,
            "verify_aud": False,
            # 'verify_iat': False,
            # 'verify_exp': False,
            # 'verify_nbf': False,
            # 'verify_iss': False,
            # 'verify_sub': False,
            # 'verify_jti': False,
            # 'verify_at_hash': False
        }
        return jwt.decode(token or "", None, options=options, algorithms=["RS256"])
    except JWTError as e:
        log_err(e)
        return None


def encode_simple_jwt(data):
    return jwt.encode(data, get_secret_string("defaultTokenECPrivateKey"), algorithm="ES256")


def decode_simple_jwt(token):
    return jwt.decode(token, get_secret_string("defaultTokenECPublicKey"))
