import json
import os
from abc import ABC, abstractmethod
from copy import copy

import boto3

from utils.api import APICaller
from utils.auth_utils import load_env_file, get_basic_credentials
from utils.dict_utils import get, pascal_to_snake, capitalizeKeys
from utils.dict_utils import merge, first
from utils.errors import NotFoundError
from utils.string_utils import strOrNone


class UserData(ABC):
    def __init__(self, user):
        self.user = user

    @abstractmethod
    def attributeName(self):
        pass

    @abstractmethod
    def format(self, data):
        pass

    def get(self):
        data = get(self.user, self.attributeName(), [])
        return [self.format(x) for x in data]

    def register(self, data):
        dynamodb = boto3.resource("dynamodb")
        table = dynamodb.Table(os.environ["DYNAMODB"])
        self.user[self.attributeName()] = data
        table.update_item(
            Key={"uid": self.user["uid"]},
            UpdateExpression="SET #x = :x",
            ExpressionAttributeNames={"#x": self.attributeName()},
            ExpressionAttributeValues={":x": data},
        )

    def remove(self, field, value):
        get_ = self.get()
        array = [data for data in get_ if data[field] != value]
        if len(get_) == len(array):
            className = self.__class__.__name__  # use the classname for the error massage might not be a good idea
            raise NotFoundError("{} `{}` doesn't exist for user `{}`".format(className, value, self.user["uid"]))
        self.register(array)
        return array


class Ean(UserData):
    def attributeName(self):
        return "ean"

    def get(self, enrich_data=False):
        """enrich_data will fetch extra data on some WS but make the request slower"""
        ret = super().get()
        if enrich_data:
            ret = self._enrich_data(ret)
        return ret

    def getAsDict(self, enrich_data=False):
        data = self.get(enrich_data)
        ret = {}
        for ean_object in data:
            ret[str(ean_object["ean"])] = ean_object
        return ret

    def format(self, data):
        return {"ean": str(data["ean"])}

    def _enrich_data(self, current_data):
        result_dict = {}
        ret = []

        ean_threads = [
            APICaller(
                job_name="ean_" + str(ean_object["ean"]),
                method="get",
                path="/ean",
                params={"Ean": str(ean_object["ean"])},
                body=None,
                headers={"Authorization": get_basic_credentials(load_env_file()["BASICAUTH_MYRESAAPI"])},
                throw=False,
                result_dict=result_dict,
            )
            for ean_object in current_data
        ]

        for t in ean_threads:
            t.start()
        for t in ean_threads:
            t.join()

        for ean_object in current_data:
            ean = str(ean_object["ean"])
            liste_ean = get(get(result_dict, "ean_" + ean, {}), "ListeEan", [])
            ean_elem = [elem for elem in liste_ean if str(get(elem, "Ean", "")) == ean]
            ean_info = first(ean_elem) or {}
            compteurs = [ean_info[k] for k in ean_info if k[0:6] == "NumCpt"] if ean_info else None
            ean_object = merge(
                ean_object,
                {
                    "Ean": str(ean),
                    "Adresse": {
                        "CdPostal": strOrNone(get(ean_info, "OrCdpostal")),
                        "Localite": strOrNone(get(ean_info, "OrLocalite")),
                        "Rue": strOrNone(get(ean_info, "OrAddress")),
                        "NumRue": strOrNone(get(ean_info, "OrNumCmpt")),
                    },
                    "SectActivite": strOrNone(get(ean_info, "SectActivite")),
                    "Status": strOrNone(get(ean_info, "StatutEan")),
                    "DateDebutPassage": get(ean_info, "DateDebut"),
                    "DateFinPassage": get(ean_info, "DateFin"),
                    "DateDebutReleve": get(ean_info, "DtLimiDebut"),
                    "DateFinReleve": get(ean_info, "DtLimiFin"),
                    "Cpt": compteurs,
                    "PartenaireId": get(ean_info, "PartenaireId"),
                    "NumeroContrat": get(ean_info, "CcHgz"),
                    "HaugazelId": get(ean_info, "BpHgz"),
                    "Profile": {
                        "SmartMeter": any([Ean.isSmartMeter(cpt) for cpt in compteurs or []]),
                        "Social": get(ean_info, "CcHgz") is not None,
                    },
                },
            )
            ret.append(ean_object)
        return ret

    @staticmethod
    def isSmartMeter(cpt):
        return str(cpt).startswith("1SAG")

    def addEan(self, ean):
        data = self.format(
            {
                "ean": ean,
            }
        )
        list_ = [d for d in super().get() if d["ean"] != data["ean"]]
        list_.append(data)
        self.register(list_)
        return list_

    def removeEan(self, ean):
        return self.remove("ean", ean)


class Dossier(UserData):
    def attributeName(self):
        return "dossiers"

    def get(self, enrich_data=False):
        """enrich_data will fetch extra data on some WS but make the request slower"""
        ret = super().get()
        if enrich_data:
            ret = self._enrich_data(ret)
        return ret

    def _enrich_data(self, current_data):
        dossiers = [str(dossier_object["id"]) for dossier_object in current_data]

        tmp = {}
        ws59 = APICaller(
            job_name="ws59",
            method="post",
            path="/demande_travaux/dossier",
            params=None,
            body=json.dumps({"NumDossier": dossiers}),
            throw=False,
            result_dict=tmp,
        )
        ws59.start()
        ws59.join()
        items = get(get(tmp, "ws59", {}), "Liste", [])
        del tmp["ws59"]

        return [{"Id": first(get(item, "NumDossier")), **item} for item in items if first(get(item, "NumDossier")) in dossiers]

    def format(self, data):
        return {"id": int(data["id"])}

    def addDossier(self, id):
        data = self.format({"id": id})
        list_ = [d for d in self.get() if d["id"] != data["id"]]
        list_.append(data)
        self.register(list_)
        return list_

    def removeDossier(self, id):
        return self.remove("id", id)


class Preferences(UserData):
    def attributeName(self):
        return "preferences"

    def get(self):
        current_pref = get(self.user, self.attributeName(), {})
        com_pref = {
            **current_pref,
            "com_global": get(current_pref, "com_global", "mail"),
            "com_encod_index_mail": get(current_pref, "com_encod_index_mail", False),
            "com_encod_index_sms": get(current_pref, "com_encod_index_sms", False),
            "com_encod_index_postal": get(current_pref, "com_encod_index_postal", False),
            "com_pass_index_mail": get(current_pref, "com_pass_index_mail", False),
            "com_pass_index_sms": get(current_pref, "com_pass_index_sms", False),
            "com_dossier_racc_mail": get(current_pref, "com_dossier_racc_mail", True),
            "com_dossier_racc_sms": get(current_pref, "com_dossier_racc_sms", False),
            "com_dossier_racc_postal": get(current_pref, "com_dossier_racc_postal", False),
            "com_rappel_paiement_mail": get(current_pref, "com_rappel_paiement_mail", False),
            "com_rappel_paiement_sms": get(current_pref, "com_rappel_paiement_sms", False),
            "com_rappel_paiement_postal": get(current_pref, "com_rappel_paiement_postal", False),
            "com_conf_paiement_mail": get(current_pref, "com_conf_paiement_mail", False),
            "com_conf_paiement_sms": get(current_pref, "com_conf_paiement_sms", False),
            "com_conf_paiement_postal": get(current_pref, "com_conf_paiement_postal", False),
            "com_conf_sepa_sms": get(current_pref, "com_conf_sepa_sms", False),
            "com_conf_sepa_postal": get(current_pref, "com_conf_sepa_postal", False),
            "com_panne_mail": get(current_pref, "com_panne_mail", False),
            "com_panne_sms": get(current_pref, "com_panne_sms", False),
            "com_conf_sepa_mail": get(current_pref, "com_conf_sepa_mail", False),
            "com_panne_evo_mail": get(current_pref, "com_panne_evo_mail", False),
            "com_panne_evo_sms": get(current_pref, "com_panne_evo_sms", False),
            "com_panne_fin_mail": get(current_pref, "com_panne_fin_mail", False),
            "com_panne_fin_sms": get(current_pref, "com_panne_fin_sms", False),
            "com_marketing_mail": get(current_pref, "com_marketing_mail", False),
            "com_ppp_mail": get(current_pref, "com_ppp_mail", False),
            "com_ppp_sms": get(current_pref, "com_ppp_sms", False),
            "com_smartportal_invitation_mail": get(current_pref, "com_smartportal_invitation_mail", False),
            "com_smartportal_bilan_mail": get(current_pref, "com_smartportal_bilan_mail", False),
            "com_smartportal_alert_mail": get(current_pref, "com_smartportal_alert_mail", False),
            "com_smartportal_alert_sms": get(current_pref, "com_smartportal_alert_sms", False),
        }
        return com_pref

    def get_bp_format(self):
        prefs = self.get()
        prefs["CondGen"] = False
        prefs["valid_phone"] = self.user.get("valid_phone")
        prefs["valid_contact_email"] = self.user.get("valid_contact_email")

        if self.user.get("Commune"):
            prefs["BP_COMMUNE"] = True
            for k, v in capitalizeKeys(self.user["Commune"], pascal_to_snake).items():
                if isinstance(v, list):
                    v = [str(i) for i in v]
                prefs[f"commune_{k}"] = v

        for k, v in copy(prefs).items():
            if isinstance(v, list):
                n = 0
                for value in prefs.pop(k):
                    prefs[f"{k}_list_{n}"] = value
                    n += 1

        prefs["valid_phone"] = get(prefs, "valid_phone", False)
        prefs["valid_contact_email"] = get(prefs, "valid_contact_email", False)

        return [
            {
                "CodePrefe": pref_key,
                "Prefe": (str(pref_val).lower() if isinstance(pref_val, bool) else str(pref_val)),
            }
            for pref_key, pref_val in prefs.items()
        ]

    def format(self, data):
        return data


def extract_from_ean_list(ean_list, key_to_extract, ean_to_match):
    value = [get(ean_item, key_to_extract) for ean_item in ean_list if get(ean_item, "Ean") == str(ean_to_match)] if ean_list else []
    return value[0] if value else None


class Partenaires(UserData):
    def attributeName(self):
        return "haugazelID"

    def format(self, data):
        return int(data)

    def add(self, value):
        lst = self.get()
        if value not in lst:
            lst.append(value)
        self.register(lst)
        return lst

    def remove(self, name, value):
        lst = self.get()
        lst_filtered = [elem for elem in lst if elem != value]
        if len(lst) == len(lst_filtered):
            raise NotFoundError("{} `{}` doesn't exist for user `{}`".format("haugazelID", value, self.user["uid"]))
        self.register(lst_filtered)
        return lst_filtered


class Permissions(UserData):
    def attributeName(self):
        return "permissions"

    def format(self, data):
        return data

    def addPermission(self, perm):
        list_ = [d for d in super().get() if d != perm]
        list_.append(perm)
        self.register(list_)
        return list_
