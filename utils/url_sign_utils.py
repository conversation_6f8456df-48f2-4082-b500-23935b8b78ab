import hmac
from os import urandom
from typing import Optional

from utils.aws_utils import get_secret_string


def _get_inline_url(url: str, params: Optional[dict] = None):
    return f"{url}?{'&'.join([f'{k}={params[k]}' for k in sorted(params)])}" if params else url


def generate_url_signature(method: str, url: str):
    key = get_secret_string("MyResaAPI/SignedUrlAuthorizerKey")

    return hmac.digest(key.encode(), f"{method.upper()} {url}".encode(), "sha3_256").hex()


def generate_signed_url(method: str, url: str, params: Optional[dict] = None):
    inline_url = _get_inline_url(url, params)
    signature = generate_url_signature(method, inline_url)

    return f"{inline_url}{'&' if params else '?'}Signature={signature}"


def generate_salted_signed_url(method: str, url: str, params: Optional[dict] = None):
    if params is None:
        params = {}
    params["Salt"] = urandom(16).hex()

    return generate_signed_url(method, url, params)


def check_signed_url(method: str, url: str, params: dict, signature: str):
    return generate_url_signature(method, _get_inline_url(url, params)) == signature


if __name__ == "__main__":
    method = "GET"
    protocol = "https"
    host = "5udreq87o4.execute-api.eu-west-1.amazonaws.com"
    path = "/v0/utilisateurs/edit/fug.commodol.eydn0/preferences"
    params = {"Preference": "test_pref", "Value": "true"}

    signed_url = generate_salted_signed_url(method, f"{protocol}://{host}{path}", params)
    print(signed_url)
