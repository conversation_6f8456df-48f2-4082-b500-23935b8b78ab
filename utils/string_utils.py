import re
import string
from random import choice


def capitalizeFirstLetter(str_):
    if len(str_) < 1:
        return str_
    return str_[0].upper() + (str_[1:] if len(str_) > 1 else "")


def bind(str_, char):
    """
    bind words using a separator
    """
    strs_ = str_.split(char)
    ret = []
    for s in strs_:
        ret.append(capitalizeFirstLetter(s))
    return "".join(ret)


def toPascalCase(str_):
    ret = bind(str_, "_")
    ret = bind(ret, "-")
    return ret


def format_template(self, template, data, err):
    try:
        result = template.format(data)
    except Exception:
        raise err
    return result


def multiple_replace(string, rep_dict):
    pattern = re.compile(
        "|".join([re.escape(k) for k in sorted(rep_dict, key=len, reverse=True)]),
        flags=re.DOTALL,
    )
    return pattern.sub(lambda x: rep_dict[x.group(0)], string)


def randomString(stringLength=10):
    """Generate a random string of letters and digits"""
    lettersAndDigits = string.ascii_letters + string.digits
    return "".join(choice(lettersAndDigits) for i in range(stringLength))


def strOrNone(s):
    if s is None:
        return None
    return str(s)


def ifstrip(input):
    return str(input).strip() if input is not None else None


def ifnempty(input):
    return str(input) if input is not None and len(str(input).strip()) > 0 else None


def ifsuffix(input, suffix):
    return input + " " + suffix if input is not None and len(input) > 0 else ""


def ifpreffix(input, preffix):
    return preffix + " " + input if input is not None and len(input) > 0 else ""


def prettify_int(input):
    """
    prettify string if number
    return a string
    """
    try:
        result = str(int(input))
    except Exception:
        result = input
    return result


def parse_keyvalue(content):
    myDict = {}
    if not content:
        return myDict
    for line in tuple(str(content).split("\n")):
        if "=" in line:
            a, b = tuple(line.split("="))
        else:
            a = line
            b = ""
        myDict = {**myDict, **{a: b}}
    return myDict


def format_keyvalue_dict(content):
    lines = []
    for key, value in content.items():
        value_ = str(value)
        key_ = str(key)
        lines.append(f"{key_}={value_}")
    return "\n".join(lines)


def clean_onboarding_string(s):
    print("Cleaning " + s)

    if s is None or len(s.strip()) == 0:
        return ""

    S = s.upper().strip()
    clean_chars = re.sub("[`,*\\|/'\"!#$%&+-.:;<=>?@^_`{|}~()]+", " ", S)
    end_literal_numr = re.sub("^\D*\d*", " ", clean_chars) if re.search(r"\d", clean_chars) else ""
    if end_literal_numr.strip() and len(end_literal_numr) < len(clean_chars):
        no_end_literal = clean_chars.strip()[: -len(end_literal_numr.strip())]
    else:
        no_end_literal = clean_chars

    repl = {
        "É": "E",
        "Á": "A",
        "Í": "I",
        "Ó": "O",
        "Ú": "U",
        "È": "E",
        "À": "A",
        "Ì": "I",
        "Ò": "O",
        "Ù": "U",
        "Ê": "E",
        "Â": "A",
        "Î": "I",
        "Ô": "O",
        "Û": "U",
    }

    no_accent = no_end_literal
    for accent, normal in repl.items():
        no_accent = no_accent.replace(accent, normal)

    final_result = no_accent.replace("  ", " ").replace("  ", " ")

    print("Cleaned " + final_result)

    return final_result


def format_number(integer_part: str, decimal_part: str, nb_integer: int | str = 0, nb_decimal: int | str = 0, separator: str = ".") -> str:
    """
    Parameters
    ----------
    integer_part: str
        The integer part of the number to format.
    decimal_part: str
        The decimal part of the number to format.
    nb_integer: int|str
        The total number of digits desired in the integer part.
    nb_decimal: int|str
        The total number of digits desired in the decimal part.
    separator: str, default=','
        The character that separates the integer and decimal parts.

    Returns
    -------
    str
        The formatted number as a string with the specified conditions applied.
    """

    formatted_number = "0"

    if nb_integer:
        if not isinstance(nb_integer, int):
            nb_integer = int(nb_integer)
        if nb_integer != 0:
            # Cut and zero fill the integer part to ensure it has nb_int digits
            formatted_integer = integer_part[-nb_integer:].rjust(nb_integer, "0")
            formatted_number = formatted_integer

    if nb_decimal:
        if not isinstance(nb_decimal, int):
            nb_decimal = int(nb_decimal)
        if nb_decimal != 0:
            # Cut and zero fill the decimal part to have nb_decimal digits after the decimal point
            cleaned_decimal = decimal_part.split(".")[-1]  # remove the 0. leading the decimal if any
            formatted_decimal = cleaned_decimal[:nb_decimal].ljust(nb_decimal, "0")
            formatted_number += separator + formatted_decimal

    return formatted_number
