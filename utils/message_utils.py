import base64
import email
import json
import os
import re
from base64 import b64decode
from datetime import datetime
from email.mime.application import MIMEApplication
from email.mime.multipart import MI<PERSON><PERSON><PERSON>ipart
from email.mime.text import MIMEText
from io import StringIO
from os import environ
from typing import Optional, Union, List

import sib_api_v3_sdk
from jinja2 import Environment, Undefined
from sib_api_v3_sdk.rest import ApiException

from utils.api import basic_auth_headers, api_caller
from utils.aws_utils import (
    load_s3_file,
    get_resource_key,
    get_secret,
    write_s3_file,
    get_secret_string,
)
from utils.dict_utils import get, capitalizeKeys
from utils.errors import BadRequestError, BadGatewayError
from utils.log_utils import log_err, log_info_json, log_err_json
from utils.sharepoint import SharepointHandler
from utils.type_utils import to_list
from utils.userdata import Preferences
from utils.validation import is_valid_email, isPhone, isTestPhone


class SilentUndefined(Undefined):
    """
    For Jinja2
    Dont break pageloads because vars arent there!
    """

    def __getitem__(self, key):
        return self

    def __getattr__(self, name: str):
        log_info_json({"JINJA2_ERROR": self._undefined_message, "Obj": self._undefined_obj})
        return self


def get_template_config(template_name):
    template_config = json.loads(load_s3_file(os.environ["BUCKET"], get_resource_key("template_config.json")))
    if template_name not in template_config:
        raise BadRequestError(message="Aucune configuration de template n'est définie pour ce nom de template")
    return template_config[template_name]


def check_comm_requirements(user_data, template_config, lang):
    preferences_comm = capitalizeKeys(Preferences(user_data).get())
    authentication_ok = user_data["valide"] or template_config["AllowGhost"]
    email = user_data.get("contact_email") or user_data.get("email")
    phone = get(user_data, "phone")
    email_format_ok = is_valid_email(email)
    phone_format_ok = isPhone(phone)
    send_email_ok = email_format_ok and preferences_comm.get(template_config.get("ComPref", "") + "Mail", True)
    send_sms_ok = phone_format_ok and preferences_comm.get(template_config.get("ComPref", "") + "Sms", True)
    language_mail = language_sms = (lang or preferences_comm.get("Langue") or "FR").upper()
    email_message = None
    sms_message = None
    message = None
    email_status_code = 200
    sms_status_code = 200
    status_code = 200
    if not authentication_ok:
        message = "Le message demandé ne peut pas être envoyé à des utilisateurs ghost."
        sms_status_code = 404
        email_status_code = 404
        status_code = 404
    else:
        if template_config.get("EmailId"):
            if not email:
                email_message = "Adresse Email non disponible pour l'utilisateur."
                email_status_code = 404
            elif not email_format_ok:
                email_message = "Le format de l'adresse email de l'utilisateur n'est pas correct"
                email_status_code = 500
            elif not send_email_ok:
                email_message = "Messages Email desactivés dans les préférences de l'utilisateur."
                email_status_code = 404
            elif language_mail not in template_config["EmailId"]:
                # Use default FR if language not available
                language_mail = "FR"
                # email_message = f'La langue "{language}" n\'est pas disponible pour ce template.'
                # email_status_code = 404
        else:
            email_message = "Ce template n'est pas destiné à etre envoyé par e-mail"
            email_status_code = 400

        if template_config.get("SmsId"):
            if not phone:
                sms_message = "Phone non disponible pour l'utilisateur."
                sms_status_code = 404
            elif not phone_format_ok:
                sms_message = "Le format de phone est incorrect"
                sms_status_code = 500
            elif not send_sms_ok:
                sms_message = "Messages Sms desactivés dans les préférences de l'utilisateur"
                sms_status_code = 404
            elif language_sms not in template_config["SmsId"]:
                # Use default FR if language not available
                language_sms = "FR"
                # sms_message = f'La langue "{language}" n\'est pas disponible pour ce template.'
                # sms_status_code = 404
        else:
            sms_message = "Ce template n'est pas destiné à etre envoyé par sms"
            sms_status_code = 400

        if email_message is not None and sms_message is not None:
            message = "Le message demandé ne peut être envoyé à cet utilisateur ni par email ni par sms"
            status_code = min(email_status_code, sms_status_code)
    return {
        "RecipientName": f"{user_data.get('firstname', '')} {user_data.get('lastname', '')}".strip(),
        "StatusCode": status_code,
        "Message": message,
        "Email": email,
        "EmailStatusCode": email_status_code,
        "EmailErrorMessage": email_message,
        "Phone": phone,
        "SmsStatusCode": sms_status_code,
        "SmsErrorMessage": sms_message,
        "PrefCom": preferences_comm.get("ComGlobal"),
        "PrefLangMail": language_mail,
        "PrefLangSms": language_sms,
    }


def render_template(template_content, template_name):
    """
    Prepares the message corresponding to a given template and parameters
    Templating using Jinja2.
    """

    template = load_s3_file(os.environ["BUCKET"], get_resource_key(f"templates/{template_name}"))

    message = Environment(undefined=SilentUndefined).from_string(template).render(params=template_content)

    return message


def send_sms(input_phone, template_name, template_content, options, api_config):
    """
    Send a sms using WS69
    The mail is sent only if:
    - The environment is production
    - The environment is not production, but we use a trash mail (and so we are doing testing)

    Else, the template data are stored in a S3 bucket
    """
    if environ.get("STAGE", "dev") == "production" or isTestPhone(input_phone):
        return _send_sms(input_phone, template_name, template_content, options, api_config)
    else:
        write_s3_file(
            environ["ENV_MESSAGE_LOG_BUCKET"],
            f"{datetime.now()}_sms_{input_phone}.json",
            json.dumps(template_content),
        )
        return {"SmsStatusCode": 200, "Phone": input_phone}


def _send_sms(input_phone, template_name, template_content, options, api_config):
    try:
        sms_content = render_template(template_content, template_name)
        request_url = f"/sms?Sandbox={str(api_config['sandbox']).lower()}"
        required_params = {"to": input_phone, "message": sms_content}
        options_params = {} if not options else {k: v for k, v in options.items() if k not in ["to", "message"]}
        if options:
            options.pop("message", None)
        request_body = json.dumps({**required_params, **options_params})
        sms_response = api_caller(
            method="POST",
            path=request_url,
            body=request_body,
            headers=basic_auth_headers("MyResaAPI/AWS/BasicAuthPassword"),
            raw=True,
        )
        if sms_response.status_code != 200:
            log_info_json({"SMS server error": sms_response.json()})
            raise BadGatewayError("SMS server error")
    except (BadRequestError, BadGatewayError) as e:
        log_info_json({"SMS server error": e})
        return {
            "SmsStatusCode": e.status,
            "Phone": input_phone,
            "SmsErrorMessage": e.message,
        }
    except Exception as e:
        log_info_json({"SMS server error": e})
        return {
            "SmsStatusCode": 500,
            "Phone": input_phone,
            "SmsErrorMessage": "Internal server error",
        }

    return {"SmsStatusCode": 200, "Phone": input_phone}


def get_yopmail(mail: str) -> str:
    mail_part = mail.split("@")

    if len(mail_part) != 2 or mail_part[1] == "yopmail.com":
        return mail

    return f"{mail_part[0][:25]}@yopmail.com"


def send_in_blue_mail(
    template_id: Union[int, str],
    template_data: dict,
    email: Union[str, List[str]],
    reply_mail: Optional[str] = None,
    name: Optional[str] = None,
    sandbox: Optional[bool] = False,
    attachments: Optional[list] = None,
    bcc: Optional[Union[str, List[str]]] = None,
    cc: Optional[Union[str, List[str]]] = None,
):
    """
    Send a mail using sendinblue services
    The mail is sent only if:
    - The environment is production
    - The environment is not production, but we use a trash mail (and so we are doing testing)

    Else, the template data are stored in a S3 bucket
    """
    if isinstance(template_id, str):
        # If template_id is a string, then it should be fetch from template config.
        template_config = get_template_config(template_id)
        template_id = template_config["EmailId"][environ["LANG"]]

    is_production = environ.get("STAGE", "dev") == "production"
    if not is_production:
        email = [get_yopmail(e) for e in to_list(email)]
        bcc = [get_yopmail(e) for e in to_list(bcc)]
        cc = [get_yopmail(e) for e in to_list(cc)]

    return _send_in_blue_mail(template_id, template_data, email, reply_mail, name, sandbox, attachments, bcc, cc)


def _send_in_blue_mail(
    template_id: int,
    template_data: dict,
    email: Union[str, List[str]],
    reply_mail: Optional[str] = None,
    name: Optional[str] = None,
    sandbox: Optional[bool] = False,
    attachments: Optional[list] = None,
    bcc: Optional[Union[str, List[str]]] = None,
    cc: Optional[Union[str, List[str]]] = None,
):
    """
    Parameters
    ----------
    template_id : int
        The ID of the email template to be sent.
    template_data : dict
        A dictionary containing variables to be used in the email template.
    email : Union[str, List[str]]
        Recipient email address or a list of email addresses.
    reply_mail : Optional[str], optional
        Reply-to email address, by default None.
    name : Optional[str], optional
        Name of the recipient, by default None.
    sandbox : Optional[bool], optional
        If True, the email will not be actually sent, for testing purposes, by default False.
    attachments : Optional[list], optional
        A list of attachments to be included in the email, by default None.
    bcc : Optional[Union[str, List[str]]], optional
        BCC recipient email address or a list of email addresses, by default None.
    cc : Optional[Union[str, List[str]]], optional
        CC recipient email address or a list of email addresses, by default None.
    """
    # Transform str / list input to list for easier processing
    email = to_list(email)
    bcc = to_list(bcc)
    cc = to_list(cc)

    # check the email address
    for _email in email:
        if not is_valid_email(_email, check_user=True):
            return {
                "EmailStatusCode": 400,
                "Email": _email,
                "EmailErrorMessage": "Invalid email address",
            }

    configuration = sib_api_v3_sdk.Configuration()
    configuration.api_key["api-key"] = get_secret_string("MyResaApi/sendinblueApiKey")
    adfs_secret = get_secret(os.environ.get("ADFS_WEB", "MyResaWeb/ADFS/Qual"))
    template_data["ClientId"] = adfs_secret.get("client_id")
    template_data["DomainName"] = adfs_secret.get("redirect_domain")
    template_data["AltDomainName"] = get(adfs_secret, "redirect_alt_domain", adfs_secret.get("redirect_domain"))

    api_instance = sib_api_v3_sdk.TransactionalEmailsApi(sib_api_v3_sdk.ApiClient(configuration))
    email_to = [{"email": _email} for _email in email]
    reply_to = None
    if name:
        email_to[0]["name"] = name
    if reply_mail:
        reply_to = {"email": reply_mail}
    send_email = sib_api_v3_sdk.SendSmtpEmail(
        to=email_to,
        reply_to=reply_to,
        template_id=template_id,
        params=template_data,
        attachment=attachments or None,
        bcc=[{"email": _bcc} for _bcc in bcc] if bcc else None,
        cc=[{"email": _cc} for _cc in cc] if cc else None,
    )

    try:
        if not sandbox:
            log_info_json({"SendInBlueData": template_data})
            resp = api_instance.send_transac_email(send_email)
            log_info_json({"SendInBlueResponse": resp})
    except ApiException as e:
        log_info_json({"SendInBlueError": e})
        try:
            return {
                "EmailStatusCode": e.status,
                "Email": email,
                "EmailErrorMessage": json.loads(e.body),
            }
        except ValueError:
            return {
                "EmailStatusCode": e.status,
                "Email": email,
                "EmailErrorMessage": e.body,
            }
    except (BadRequestError, BadGatewayError) as e:
        log_info_json({"SendInBlueError": e})
        return {
            "EmailStatusCode": e.status,
            "Email": email,
            "EmailErrorMessage": e.message,
        }
    except Exception as e:
        log_err(e)
        return {
            "EmailStatusCode": 500,
            "Email": email,
            "EmailErrorMessage": "Internal server error",
        }

    return {"EmailStatusCode": 200, "Email": email}


def save_send_in_blue_mail_to_sharepoint(
    template_id: int,
    template_data: dict,
    email: Union[str, List[str]],
    reply_mail: str,
    desc: str,
    valise: str,
    name: Optional[str] = None,
    not_sent: Optional[bool] = False,
    attachments: Optional[list] = None,
    bcc: Optional[Union[str, List[str]]] = None,
    cc: Optional[Union[str, List[str]]] = None,
):
    configuration = sib_api_v3_sdk.Configuration()
    configuration.api_key["api-key"] = get_secret_string("MyResaApi/sendinblueApiKey")
    api_instance = sib_api_v3_sdk.TransactionalEmailsApi(sib_api_v3_sdk.ApiClient(configuration))

    try:
        api_response = api_instance.get_smtp_template(template_id)
        if not name:
            name = f"{template_id}_{api_response.name}_{email}"
            if not_sent:
                name += "_NON_ENVOYE"

        html_content = jinja2_fill_sib_template(
            api_response.html_content,
            {"params": template_data, "contact": {"EMAIL": email}},
        )
        subject = jinja2_fill_sib_template(api_response.subject, {"params": template_data, "contact": {"EMAIL": email}})
        msg = mail_to_msg(
            api_response.sender.email,
            email,
            reply_mail,
            subject,
            html_content,
            attachments,
            bcc,
            cc,
        )

        SharepointHandler.send_valise_file_to_sharepoint(
            name,
            "eml",
            base64.b64encode(msg.encode("utf-8")).decode("utf-8"),
            valise,
            metadata={
                "ContentType": "autres",
                "Type_Autres": "Document",
                "Description_document": desc,
                "VALISE_OPERATIONNELLE": "0",
            },
            max_retry=2,
        )
    except ApiException as e:
        log_err_json(
            {
                "Message": "SendInBlue exception when calling SMTPApi->get_smtp_template.",
                "Exception": e,
            }
        )


def jinja2_fill_sib_template(template: str, data: dict) -> str:
    # fmt: off
    template = """{%- set last=[] -%}
{%- macro ifchanged() -%}
  {%- set next=caller() -%}
  {%- if last|length == 0 -%}
    {{- next -}}
  {%- else -%}
    {%- set prev=last.pop() -%}
    {%- if next != prev -%}
      {{- next -}}
    {%- endif -%}
  {%- endif -%}
  {%- do last.append(next) -%}
{%- endmacro -%}""" + template.replace(
        "forloop.Last", "loop.last"
    ).replace(
        "endifchanged", "endcall"
    ).replace(
        "ifchanged", "call ifchanged()"
    ).replace(
        'time_now|date:"2006"', "params.year"
    )
    # fmt: on

    matches = re.finditer(r'\|slice:"([^"]*)"', template)
    offset = 0

    for match in matches:
        template = f"{template[: match.start() - offset]}[{match.group(1)}]{template[match.end() - offset :]}"
        offset += 7  # count the removing of the |slice: part

    return Environment(extensions=["jinja2.ext.do"], undefined=SilentUndefined).from_string(template).render(**data)


def mail_to_msg(
    sender: str,
    recipient: Union[str, List[str]],
    reply_to: str,
    subject: str,
    mail_html: str,
    attachments: list = None,
    bcc: Union[str, List[str]] = None,
    cc: Union[str, List[str]] = None,
) -> str:
    # create a simple message
    msg = MIMEMultipart()
    msg["From"] = sender
    msg["To"] = ",".join(to_list(recipient))
    msg["Subject"] = subject
    if bcc:
        msg["Bcc"] = ",".join(to_list(bcc))
    if cc:
        msg["Cc"] = ",".join(to_list(cc))
    msg.attach(MIMEText(mail_html, "html"))

    if reply_to:
        msg.add_header("reply-to", reply_to)

    # Add attachments
    if attachments:
        for attachment in attachments:
            part = MIMEApplication(b64decode(attachment["content"]), Name=attachment["name"])
            part["Content-Disposition"] = f'attachment; filename="{attachment["name"]}"'
            msg.attach(part)

    # open a file and save mail to it
    with StringIO() as out:
        gen = email.generator.Generator(out)
        gen.flatten(msg)
        return out.getvalue()


def save_sms_to_sharepoint(
    template_id: str,
    template_data: dict,
    desc: str,
    valise: str,
    phone: str,
    name: str = None,
    not_sent: bool = False,
):
    sms_content = render_template(template_data, template_id)
    sms_txt = f"""----INFO----
TO:{phone}
----CONTENT----
{sms_content}"""

    if not name:
        if isinstance(template_id, str) and "/" in template_id:
            template_id = template_id.replace("sms/", "").replace("/", "_")
        name = f"{template_id}_sms_{phone}"
        if not_sent:
            name += "_NON_ENVOYE"

    SharepointHandler.send_valise_file_to_sharepoint(
        name,
        "txt",
        base64.b64encode(sms_txt.encode("utf-8")).decode("utf-8"),
        valise,
        metadata={
            "ContentType": "autres",
            "Type_Autres": "Document",
            "Description_document": desc,
            "VALISE_OPERATIONNELLE": "0",
        },
        max_retry=2,
    )
