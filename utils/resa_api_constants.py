storelocation_geocoding_api_root = "https://nominatim.openstreetmap.org/search"

errmsg_wrong_param = "La requête contient un paramètre %(param_name)s qui n'est pas attendu"
errmsg_required = "Le paramètre %(param_name)s est requis"
errmsg_type_list = "Le paramètre %(param_name)s est de type liste mais ne contient pas le séparateur %(sep)s"
errmsg_type = "Le paramètre %(param_name)s doit être de type %(param_type)s"
errmsg_range_min = "Le paramètre %(param_name)s doit être supérieur à %(range_min)s"
errmsg_range_max = "Le paramètre %(param_name)s doit être inférieur à %(range_max)s"
errmsg_range_list = "Les valeurs autorisées pour le paramètre %(param_name)s sont: à %(param_value_list)s"
errmsg_backend = "Internal server error"
errmsg_connection = "Erreur de connexion avec la base de données"
errmsg_not_found = "Aucun résultat retrouvé"

errmsg_dict = {
    "wrongParam": errmsg_wrong_param,
    "connection": errmsg_connection,
    "backend": errmsg_backend,
    "required": errmsg_required,
    "type": errmsg_type,
    "typeList": errmsg_type_list,
    "rangeMin": errmsg_range_min,
    "rangeMax": errmsg_range_max,
    "rangeList": errmsg_range_list,
}
