import redshift_connector

from utils.aws_utils import get_secret


def make_connection(db_credentials):
    conn_cred = get_secret(db_credentials["connexion-secret"])
    redshift_connector.paramstyle = "named"
    conn = redshift_connector.connect(host=db_credentials["ENDPOINT"], database="pte", port=db_credentials["PORT"], user=conn_cred["username"], password=conn_cred["password"])
    return conn
