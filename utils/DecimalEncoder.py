import json
from datetime import datetime
from decimal import Decimal as D


class DecimalEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, D):
            num = float(obj)
            if num.is_integer():
                num = int(obj)
            return num
        if isinstance(obj, datetime):
            return obj.__str__()
        return json.JSONEncoder.default(self, obj)
