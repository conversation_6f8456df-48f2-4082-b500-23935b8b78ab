import re
from typing import Union


def adapt_ep_equi_id(equi_id: Union[str, int], commune_id: Union[str, int] = None) -> str:
    equi_id = str(equi_id).strip().upper().replace(" ", "")

    if len(equi_id) >= 8:
        pass
    elif commune_id:
        if re.fullmatch(r"^[A-Z]{1}[0-9]{2,7}$", equi_id):
            # If start with letter, keep it then the commune_id and fill zero in between to 8 char
            letter = equi_id[0]
            equi_id = equi_id[1:]
            if equi_id.startswith(commune_id):
                equi_id = equi_id[len(commune_id) :]
            equi_id = f"{letter}{commune_id}{equi_id.zfill(7 - len(commune_id))}"
        else:
            # Fill zero in commune_id to 3 char and the last part to 5 char
            if equi_id.startswith(commune_id):
                equi_id = equi_id[len(commune_id) :]
            equi_id = f"{commune_id.zfill(3)}{equi_id.zfill(5)}"
    else:
        # If no commune_id, fill zero to 8 char
        equi_id = equi_id.zfill(8)

    return equi_id
