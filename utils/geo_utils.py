import urllib
from dataclasses import dataclass, field

import requests

from utils.resa_api_constants import storelocation_geocoding_api_root


@dataclass
class Address:
    road: str = field(default="")
    zip_code: any = field(default="")
    city: str = field(default="")
    number: any = field(default="")
    country: str = field(default="Belgique")
    lat: float = field(default=None)
    lng: float = field(default=None)

    def inline_format(self) -> str:
        return f"{self.road} {self.number}, {self.city} {self.zip_code}, {self.country}"

    def geocode(self) -> None:
        coords = geocode_from_text_adress(self.inline_format())
        self.lat = coords.get("lat")
        self.lng = coords.get("lon")


def geocode_from_text_adress(formatted_address: str) -> dict:
    geocoding_url = storelocation_geocoding_api_root + "?" + "q=" + urllib.parse.quote_plus(formatted_address) + "&format=json&addressdetails=1&limit=1"

    geocoding_headers = {
        "user-agent": "Mozilla/5.0 (iPad; U; CPU OS 3_2 like Mac OS X; en-us) AppleWebKit/531.21.10 "
        "(KHTML, like Gecko) Version/4.0.4 Mobile/7B334b Safari/531.21.102011-10-16 20:23:10",
        "accept-language": "en",
    }

    geocoding_request = requests.get(geocoding_url, headers=geocoding_headers)
    geocoding_response = geocoding_request.json()
    print(geocoding_response)

    lat = None
    lon = None
    if geocoding_response:
        lat = geocoding_response[0]["lat"]
        lon = geocoding_response[0]["lon"]

    return {"lat": lat, "lon": lon}
