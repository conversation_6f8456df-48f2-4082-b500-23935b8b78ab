NIGHT_ONLY_CODES = [
    "ECA_EX",
    "ECA_NP",
]


def get_code_cadran(register_config: dict) -> str:
    """
    Determine and return the appropriate cadran code based on the register configuration.

    Depending on register configuration, specific cadran code or label is returned.
    If applicable, a default value from "CodeCadran" or "CatCadran" is used if conditions are not met.

    Parameters
    ----------
    register_config : dict
        A dictionary containing the configuration of the register.
        The keys expected in the dictionary are:
        - "Smart" : bool
        - "lv_nb_lines" : int
        - "CatTarif" : str
        - "ATWRT" : str
        - "ETDZ_ZWNUMMER" : str
        - "ANZART" : str
        - "CatCadran" : str
        - "CodeCadran" : str

    Returns
    -------
    str
        A string representing the appropriate cadran code based on the input configuration.

    """
    if not register_config.get("Smart"):
        if register_config.get("lv_nb_lines") == 1:
            if register_config.get("CatTarif") == "ECA_EX":
                return "EXCL. DE NUIT"
            elif register_config.get("CatTarif") == "ECA_NP":
                return "EHP"
            else:
                return "TOTAL"
        if register_config.get("ATWRT") == "F":
            if register_config.get("ETDZ_ZWNUMMER") == "001":
                return "HAUT"
            elif register_config.get("ETDZ_ZWNUMMER") == "002":
                return "BAS"
        if register_config.get("lv_nb_lines") == 2 and register_config.get("ANZART") == "DD":
            if register_config.get("CatCadran") == "HI":
                return "JOUR(T1)"
            elif register_config.get("CatCadran") == "LO":
                return "NUIT(T2)"
        if register_config.get("CatCadran") == "HI":
            return "JOUR"
        elif register_config.get("CatCadran") == "LO":
            return "NUIT"
        elif register_config.get("CatCadran") == "TH":
            return "TOTAL"

    if not register_config.get("CodeCadran"):
        if register_config.get("CatCadran") == "TH":
            return "1.8.0"
        elif register_config.get("CatCadran") in ("NH", "HI"):
            return "1.8.1"
        elif register_config.get("CatCadran") in ("NL", "LO"):
            return "1.8.2"
        elif register_config.get("CatCadran") == "PH":
            return "1.8.3"
        elif register_config.get("CatCadran") == "PL":
            return "1.8.4"

    return register_config.get("CodeCadran") or register_config.get("CatCadran")
