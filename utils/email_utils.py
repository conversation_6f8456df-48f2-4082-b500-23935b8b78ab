import json
import os
import smtplib
from datetime import timedelta, date, datetime
from email import encoders
from email.mime.base import MIMEBase
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from email.mime.text import MIMEText

from utils.aws_utils import get_dynamodb_table, get_resource_key, load_s3_file
from utils.dict_utils import get
from utils.errors import ForbiddenError


class Email:
    SMTP = None
    sendAs = "<EMAIL>"

    def __init__(self):
        cred_file = json.loads(load_s3_file(os.environ["BUCKET"], get_resource_key("smtp-credentials.json")))
        self.SMTP = smtplib.SMTP(host=cred_file["SMTP"]["server"], port=cred_file["SMTP"]["port"])
        self.SMTP.starttls()
        self.SMTP.login(cred_file["SMTP"]["username"], cred_file["SMTP"]["password"])

    def send(
        self,
        subject,
        to,
        template_name,
        data=None,
        attachments=None,
        sandbox_mode=False,
    ):
        if data is None:
            data = {}
        if attachments is None:
            attachments = []
        template = load_s3_file(os.environ["BUCKET"], get_resource_key("templates/{}".format(template_name)))
        msg = MIMEMultipart()
        msg["From"] = self.sendAs
        msg["To"] = to
        msg["Subject"] = subject
        for key, value in data.items():
            template = template.replace("{" + str(key) + "}", str(value))
        msg.attach(MIMEText(template, "html"))
        for attachment in attachments:
            file_path = attachment["FilePath"]
            name = attachment["DocumentName"]
            extension = attachment["Extension"]
            full_name = f"{name}.{extension}"
            with open(file_path, "rb") as file:
                part = MIMEBase("application", "octet-stream")
                part.set_payload(file.read())
            encoders.encode_base64(part)
            part.add_header("Content-Disposition", f"attachement; filename= {full_name}")
            msg.attach(part)
        if not get(os.environ, "USE_MOCK") and not sandbox_mode:
            self.SMTP.send_message(msg)
        del msg

    def can_send_email(self, email, limit=10):
        table = get_dynamodb_table(os.environ["EmailSendsTable"])
        response = table.get_item(Key={"email": email})
        if not response or "Item" not in response:
            return True
        today = str(date.today())
        date_ = get(response["Item"], "date", None)
        if date_ != today:
            return True
        amount = get(response["Item"], "amount", 0)
        if amount < limit:
            return True
        raise ForbiddenError("Trop de mails ont été envoyé à cette adresse")

    def increment_email_send(self, email):
        table = get_dynamodb_table(os.environ["EmailSendsTable"])
        response = table.get_item(Key={"email": email})
        today = str(date.today())
        date_ = get(get(response, "Item", {}), "date", None)
        amount = get(get(response, "Item", {}), "amount", 0)
        if date_ != today:
            amount = 0
        table.put_item(
            Item={
                "email": email,
                "date": today,
                "amount": amount + 1,
                "TTL": int((datetime.now() + timedelta(days=1)).timestamp()),
            }
        )

    def send_email(self, subject, to, template, data=None):
        if data is None:
            data = {}
        msg = MIMEMultipart()
        msg["From"] = self.sendAs
        msg["To"] = to
        msg["Subject"] = subject
        for key, value in data.items():
            template = template.replace("{" + str(key) + "}", str(value))
        msg.attach(MIMEText(template, "html"))
        if not get(os.environ, "USE_MOCK"):
            self.SMTP.send_message(msg)
        del msg

    def __del__(self):
        self.SMTP.quit()
