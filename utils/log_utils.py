import json
import logging
import os
import sys
import time
import traceback
from datetime import datetime
from io import StringIO

import boto3

from utils.NoFailEncoder import NoFailEncoder

logger = logging.getLogger("MyResaAPILogger")
logger.propagate = False
logger.setLevel(logging.INFO)
logger_handler = logging.StreamHandler()
logger_handler.setFormatter(logging.Formatter("[%(levelname)s]\t%(message)s\n"))
logger.addHandler(logger_handler)


class LogTime:
    def __init__(self, title="undefined"):
        self.title = title

    def __enter__(self):
        self.timestamp1 = time.time()

    def __exit__(self, type, value, traceback):
        self.timestamp2 = time.time()
        diff = self.timestamp2 - self.timestamp1
        log_info("LogTime `" + str(self.title) + "` : " + str(diff) + " seconds")


class NoPrint:
    def __enter__(self):
        self._original_stdout = sys.stdout
        sys.stdout = open(os.devnull, "w")

    def __exit__(self, exc_type, exc_val, exc_tb):
        sys.stdout.close()
        sys.stdout = self._original_stdout


class Capturing(list):
    def __enter__(self):
        self._stdout = sys.stdout
        sys.stdout = self._stringio = StringIO()
        return self

    def __exit__(self, *args):
        self.extend(self._stringio.getvalue().splitlines())
        del self._stringio  # free up some memory
        sys.stdout = self._stdout


def log_msg_to_json(msg):
    try:
        if not isinstance(msg, (dict, list)):
            msg = {"message": msg}
        return json.dumps(msg, cls=NoFailEncoder)
    except:
        return msg


def log_err(*errmsg, print_traceback=True):
    """
    Log erreur dans systeme de log
    :param errmsg:
    :param print_traceback:
    :return:
    """
    logger.error(" ".join([str(s) for s in errmsg]))
    if print_traceback:
        exp_tb = traceback.format_exc()
        if exp_tb and exp_tb != "NoneType: None\n":
            logger.error(exp_tb)


def log_err_json(err_msg):
    """
    Log err json dans systeme de log
    :param err_msg:
    :return:
    """
    if isinstance(err_msg, dict):
        exp_tb = traceback.format_exc()
        if exp_tb and exp_tb != "NoneType: None\n":
            err_msg["traceback"] = exp_tb
        log_err(log_msg_to_json(err_msg), print_traceback=False)
    else:
        log_err(log_msg_to_json(err_msg))


def log_info(*info_msg):
    """
    Log info dans systeme de log
    :param info_msg:
    :return:
    """
    logger.info(" ".join([str(s) for s in info_msg]))


def log_info_json(info_msg):
    """
    Log info json dans systeme de log
    :param info_msg:
    :return:
    """
    log_info(log_msg_to_json(info_msg))


def log_warning(warning_msg):
    """
    Log warning dans systeme de log
    :param warning_msg:
    :return:
    """
    logger.warning(warning_msg)


def log_warning_json(warning_msg):
    """
    Log warning json dans systeme de log
    :param warning_msg:
    :return:
    """
    log_warning(log_msg_to_json(warning_msg))


class CloudwatchLogger:
    def __init__(self, logGroup):
        self.logs = []
        self.logGroup = logGroup
        self.stream = datetime.now().strftime("%Y-%m-%d %H-%M-%S")
        self.client = boto3.client("logs")
        self.sequenceToken = None
        if not logGroup:
            print("CloudwatchLogger has been initiated without logGroup")
        else:
            try:
                self.client.create_log_group(logGroupName=self.logGroup)
            except Exception as e:
                print(e)

            self.client.create_log_stream(logGroupName=self.logGroup, logStreamName=self.stream)
            resp = response = self.client.put_log_events(
                logGroupName=self.logGroup,
                logStreamName=self.stream,
                logEvents=[
                    {
                        "timestamp": int(datetime.now().timestamp() * 1000),
                        "message": "init",
                    }
                ],
            )
            self.sequenceToken = resp["nextSequenceToken"]

    def log(self, msg):
        self.logs.append({"timestamp": int(datetime.now().timestamp() * 1000), "message": msg})

    def flush(self):
        if len(self.logs):
            if self.logGroup:
                response = self.client.put_log_events(
                    logGroupName=self.logGroup,
                    logStreamName=self.stream,
                    logEvents=self.logs,
                    sequenceToken=self.sequenceToken,
                )
                self.sequenceToken = response["nextSequenceToken"]
            else:
                for log in self.logs:
                    print(log["message"])
        self.logs = []

    def __del__(self):
        self.flush()
