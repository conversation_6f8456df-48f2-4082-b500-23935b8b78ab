import datetime as dt
import json
import traceback
from builtins import ValueError
from decimal import Decimal
from typing import Union, Optional

from utils.errors import BadRequestError
from utils.log_utils import log_err


def binary_to_rows(binary_file):
    try:
        result = binary_file.decode("utf-8").splitlines()
        return result
    except Exception:
        errmsg = "ERROR 500: erreur dans le decodage du fichier"
        log_err(traceback.format_exc())
        raise Exception(errmsg)


def date_format(
    input_date,
    input_format,
    output_format="%d/%m/%Y",
    throw=True,
    err=ValueError("ERROR: 500 Des donnees de type date ont un format inattendu"),
):
    """
    Transforme une date dans le format souhaite ou genere une erreur.
    :param input_date:
    :param input_format:
    :return:
    """

    try:
        if input_date is None:
            result = ""
        else:
            result = dt.datetime.strptime(input_date, input_format).strftime(output_format)
    except ValueError:
        log_err(traceback.format_exc())
        if throw:
            raise err
        else:
            return None

    return result


def time_format(input_time, input_format, output_format="%H:%M:%S", throw=True):
    """
    Transforme un timestamp dans le format souhaite ou genere une erreur.
    :param input_time:
    :param input_format:
    :return:
    """

    try:
        if not input_time:
            result = ""
        else:
            result = dt.datetime.strptime(input_time, input_format).time().strftime(output_format)

    except ValueError:
        errmsg = "ERROR: 500 Des donnees de type time ont un format inattendu"
        log_err(traceback.format_exc())
        if throw:
            raise ValueError(errmsg)
        else:
            return None

    return result


def int_format(
    param_value,
    err=ValueError("ERROR: 500 Des donnees texte de type entier ont un format inattendu"),
):
    try:
        if param_value is None:
            return None
        if isinstance(param_value, str):
            param_value = param_value.replace(" ", "")
        return int(param_value)
    except Exception:
        raise err


def float_format(param_value: Union[str, float, None], field_name: Optional[str] = None, err: Optional[Exception] = None) -> Optional[float]:
    """
    @param param_value: The value to be converted to a float.
    @param field_name: The name of the field, if applicable.
    @param err: The exception to be raised in case of an error, if specified.
    @return: The value converted to a float.

    This method takes a value and attempts to convert it to a float.
    If the value is None, the method returns None.
    If the value is a string, any spaces are removed and any commas are replaced with periods before conversion.
    If the conversion is successful, the method returns the float value.

    If an error occurs during the conversion, the method handles the error based on the given parameters.
    If an 'err' parameter is specified, the error is raised using the specified exception class.
    If a 'field_name' parameter is set, a BadRequestError with a custom message and error code is raised,
    indicating that the field should be a float or a string float representation.
    If neither 'err' nor 'field_name' parameters are specified, a BadRequestError with a generic message and error code is raised.
    """
    try:
        if param_value is None:
            return None
        if isinstance(param_value, str):
            param_value = param_value.replace(" ", "").replace(",", ".")
        return float(param_value)
    except (ValueError, TypeError) as exc:
        if err:
            raise err from exc
        if field_name:
            raise BadRequestError(
                message=f"The field {field_name!r} should be a float, or a string float representation",
                error_code="INVALID_FLOAT_FORMAT",
            ) from exc
        else:
            raise BadRequestError(
                message="The field should be a float, or a string float representation",
                error_code="INVALID_FLOAT_FORMAT",
            ) from exc


def assertType(name, type_, value):
    if not isinstance(value, type_):
        raise BadRequestError("{} must be a `{}`".format(name, type_.__name__))
    return value


def int_or_none(input):
    if input is None:
        return None
    else:
        try:
            return int(input)
        except ValueError:
            return None


def windowsToUnixTimestamp(val):
    return (int(val) - 116444736000000000) // 10000


def to_list(obj):
    if obj is None:
        return []
    if isinstance(obj, (tuple, set)):
        return list(obj)
    if not isinstance(obj, list):
        return [obj]
    return obj


def format_decimal(dict):
    item = json.loads(json.dumps(dict), parse_float=Decimal)
    return item
