def make_connection(db_credentials):
    db_connectors = {
        "hana": lambda creds: __import__("hdbcli.dbapi").dbapi.connect(
            address=creds["ENDPOINT"],
            port=creds["PORT"],
            user=creds["USER"],
            password=creds["PASSWORD"],
        ),
        "mssql": lambda creds: __import__("utils.mssql_utils").mssql_utils.make_connection(creds),
        "postgres": lambda creds: __import__("utils.postgres_utils").postgres_utils.make_connection(creds),
        "redshift": lambda creds: __import__("utils.redshift_utils").redshift_utils.make_connection(creds),
    }

    connector = db_connectors.get(db_credentials["RDBMS"])
    if not connector:
        return None
    return connector(db_credentials)
