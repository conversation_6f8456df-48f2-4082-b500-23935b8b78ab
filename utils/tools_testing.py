import json
import logging
import os
import socket
import time
import traceback
import unittest
from datetime import datetime

import boto3
from boto3.dynamodb.conditions import Key

from utils.api import api_caller
from utils.aws_utils import get_dynamodb_table, get_secret, list_s3_files, load_s3_file
from utils.dict_utils import first, get
from utils.ldap_utils import LDAP
from utils.string_utils import format_keyvalue_dict, parse_keyvalue, randomString


def tag(tagname):
    def decorator(func):
        return unittest.skipIf("SKIP_" + tagname.upper() in os.environ, "Skipping " + tagname)(func)

    return decorator


def log_err(errmsg):
    """
    Log erreur dans systeme de log
    :param errmsg:
    :return:
    """
    print(errmsg)
    logger = logging.getLogger()
    logger.error(errmsg)
    logger.error(traceback.format_exc())


def log(info_msg):
    """
    Log info dans systeme de log
    :param info_msg:
    :return:
    """
    print(info_msg)
    logger = logging.getLogger()
    logger.info(info_msg)


def getEnvironment():
    env = os.environ["ENV"] if ("ENV" in os.environ) else "dev"
    return env if (env == "dev" or env == "qta") else "sandbox-" + env


def getLambdaFullName(name):
    return name + "-" + getEnvironment()


def getDynamoDBTable():
    dynamodb = boto3.resource("dynamodb")
    return dynamodb.Table("MyResaUser_dev")


def get_test_user_token():
    token = "Bearer " + get_secret("MyResaAPI/Test/AuthorizationToken")["TOKEN"]
    return token


def get_latest_doc_key(env):
    bucket = f"my-resa-api-{env}".lower()
    objects = list_s3_files(bucket)
    openapi_docs = [x.key for x in objects if x.key.endswith("myresaapi_openapi_doc.json") and len(x.key.split("/")) > 2]
    if len(openapi_docs) == 1:
        return openapi_docs[0]
    else:
        return max(*openapi_docs)


def get_openapi():
    bucket = f"my-resa-api-{os.environ['ENV']}".lower()
    key = get_latest_doc_key(os.environ["ENV"])
    openapi = load_s3_file(bucket, key)
    return json.loads(openapi.replace("#/components/schemas", "#/definitions"))


def get_schema(signature, method, response_id):
    doc_file = get_openapi()
    target_schema = doc_file["paths"][signature][method]["responses"][response_id]["content"]["application/json"]["schema"]
    schema_lib = doc_file["components"]["schemas"]
    json_schema = {
        **target_schema,
        "definitions": schema_lib,
        "schema": "$http://json-schema.org/draft-07/schema#",
    }
    return json_schema


def get_input_parameters(signature, method):
    doc_file_s3 = json.loads(load_s3_file(os.environ["DOC_BUCKET_NAME"], os.environ["DOC_BUCKET_FILE"]))

    query_string = ""
    path_string = ""
    if "parameters" in doc_file_s3["paths"][signature][method]:
        input_parameters = doc_file_s3["paths"][signature][method]["parameters"]
        try:
            query_string_list = [param["name"] + "=" + str(param["schema"]["example"]) for param in input_parameters if param["in"] == "query"]
        except KeyError:
            query_string_list = [param["name"] + "=" + str(param["example"]) for param in input_parameters if param["in"] == "query"]
        query_string = "&".join(query_string_list)

        try:
            path_string = str(
                next(
                    iter([param["schema"]["example"] for param in input_parameters if param["in"] == "path"]),
                    None,
                )
                or "",
            )
        except KeyError:
            path_string = str(
                next(
                    iter([param["example"] for param in input_parameters if param["in"] == "path"]),
                    None,
                )
                or "",
            )

    body_string = ""
    try:
        body_string = json.dumps(doc_file_s3["paths"][signature][method]["requestBody"]["content"]["application/json"]["example"])
    except Exception:
        pass

    return {"query": query_string, "path": path_string, "body": body_string}


def get_expected_response(signature, method, response_id):
    doc_file_s3 = json.loads(load_s3_file(os.environ["DOC_BUCKET_NAME"], os.environ["DOC_BUCKET_FILE"]))
    target_response = doc_file_s3["paths"][signature][method]["responses"][response_id]["content"]["application/json"]["example"]
    return target_response


def get_actual_response(url, response_id):
    doc_file = load_s3_file(os.environ["DOC_BUCKET_NAME"], os.environ["DOC_BUCKET_FILE"])
    doc_file_url = None
    result = None
    return result


def is_open(ip, port):
    timeout = 3
    s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    s.settimeout(timeout)
    try:
        s.connect((ip, int(port)))
        s.shutdown(socket.SHUT_RDWR)
        return True
    except:
        return False
    finally:
        s.close()


def check_host(ip, port):
    ipup = False
    retry = 5
    delay = 10
    for i in range(retry):
        if is_open(ip, port):
            ipup = True
            break
        else:
            time.sleep(delay)
    return ipup


class TestInfo:
    def __init__(self, testclass, base_path, isEndpoint, mappings=None):
        if mappings is None:
            mappings = {}
        self.base_path = base_path
        self.isEndpoint = isEndpoint
        self.mappings = mappings
        TestInfo.globals[testclass] = self


TestInfo.globals = {}


def EndpointCloudWatchHandler(path=None, mappings=None):
    if mappings is None:
        mappings = {}

    def wrapper(testclass):
        TestInfo(testclass, path, True, mappings)
        return testclass

    return wrapper


def DependencyCloudWatchHandler(name):
    def wrapper(testclass):
        TestInfo(testclass, name, False)
        return testclass

    return wrapper


def writeTestLog(testcase, status, logger):
    test_info = get(TestInfo.globals, type(testcase))
    if not test_info:
        return
    resultHandler = ResultHandler(test_info.base_path, test_info.mappings, testcase._testMethodName, testcase)
    if test_info.isEndpoint:
        log = '[ENDPOINT][{}] {} "{}{}"'.format(
            status,
            resultHandler.method,
            ("/" + resultHandler.base_path) if resultHandler.base_path else "",
            resultHandler.path,
        )
    else:
        log = f"[DEPENDENCY][{status}] {test_info.base_path} ({testcase._testMethodName})"
    logger.log(log)


class ResultHandler:
    def __init__(self, base_path, mappings, name, result):
        self.base_path = base_path
        self.mappings = mappings
        self.name = name
        self.result = result
        self.method = self.getMethod(name)
        self.path = self.getPath(name, self.method)

    def getMethod(self, name):
        for method in ["get", "post", "patch", "delete"]:
            if method in name:
                return method
        return None

    def getPath(self, name, method):
        path = name.split(f"{method}__")
        if len(path) > 1:
            if path[-1] in self.mappings:
                return f"/{self.mappings[path[-1]]}"
            else:
                return f"/{path[-1]}"
        else:
            return ""


class TestUserBuilder:
    def __init__(self, active_directory_credentials, user_db):
        dynamodb = boto3.resource("dynamodb")
        host = active_directory_credentials["host"]
        port = active_directory_credentials["port"]
        login = active_directory_credentials["login"]
        password = active_directory_credentials["password"]
        folder_dn = active_directory_credentials["folder_dn"]
        self.ldap = LDAP(host, port, login, password, folder_dn)  # should fail here if cannot connect AD
        self.db = dynamodb.Table(user_db)
        random = randomString()
        self.defaultEmail = f"testuser_{random}@mock.com"
        self.defaultPassword = "Welcome@2020"

    def create(self, ghost):
        try:
            email = self.defaultEmail
            uid = "ghost_test_" + randomString()
            user_data = {
                "uid": uid,
                "valide": not ghost,
                "ean": [],
                "preferences": {
                    "Langue": "FR",
                    "com_global": "mail",
                    "com_encod_index_mail": False,
                    "com_encod_index_sms": False,
                    "com_encod_index_postal": False,
                    "com_pass_index_mail": False,
                    "com_pass_index_sms": False,
                    "com_pass_index_postal": False,
                    "com_dossier_racc_mail": False,
                    "com_dossier_racc_sms": False,
                    "com_dossier_racc_postal": False,
                    "com_rappel_paiement_mail": False,
                    "com_rappel_paiement_sms": False,
                    "com_rappel_paiement_postal": False,
                    "com_conf_paiement_mail": False,
                    "com_conf_paiement_sms": False,
                    "com_conf_paiement_postal": False,
                    "com_conf_sepa_mail": False,
                    "com_conf_sepa_sms": False,
                    "com_conf_sepa_postal": False,
                    "com_panne_mail": False,
                    "com_panne_sms": False,
                    "com_panne_evo_mail": False,
                    "com_panne_evo_sms": False,
                    "com_panne_fin_mail": False,
                    "com_panne_fin_sms": False,
                    "com_marketing_mail": False,
                },
                "email": email,
                "dossiers": [],
                "pannes": [],
                "notifications": [],
                "phone": None,
                "firstname": "Test",
                "lastname": "User",
                "permissions": ["admin", "entrepreneur"],
                "adresse": {
                    "Rue": "mock street",
                    "NumRue": "5",
                    "Localite": "mock city",
                    "Cdpostal": "4000",
                },
                "haugazelID": [],
                "testUser": True,
                "createdAt": int(datetime.now().timestamp() * 1000),
            }
            table = get_dynamodb_table(os.environ["DYNAMODB"])
            if not ghost:
                firstname = get(user_data, "firstname")
                lastname = get(user_data, "lastname")
                phone = get(user_data, "phone")
                username = firstname[:3] + "." + lastname[:9] + "." + randomString(5)
                password = "Welcome@2020"
                fullname = firstname + " " + lastname
                existing = self.ldap.findUser("mail", email)
                user_data["bp"] = 0
                if existing:
                    self.delete(existing["uid"])
                uid = self.ldap.addUser(
                    {
                        "fullname": fullname,
                        "password": password,
                        "firstname": firstname,
                        "lastname": lastname,
                        "email": email,
                        "phone": phone,
                        "username": username,
                    },
                )
                user = self.ldap.findUser("uid", uid)
                description = parse_keyvalue(user["description"])
                description["testUser"] = "true"
                self.ldap.setUserAttribute(user["cn"], "description", format_keyvalue_dict(description))
                user_data["uid"] = uid
            table.put_item(Item=user_data)

            time.sleep(2)
        except Exception as e:
            self.delete(uid)
            raise e
        return uid

    def delete(self, uid):
        try:
            user = first(self.db.query(KeyConditionExpression=Key("uid").eq(uid))["Items"])
            self.db.delete_item(Key={"uid": uid})
            self.ldap.deleteUserByUid(uid)
        except Exception as e:
            if not self.is_ghost(uid):
                log_err(str(e))
                raise e

    def is_ghost(self, uid):
        return uid.startswith("ghost")

    def authenticate(self, email=None, password=None):
        email = email or self.defaultEmail
        password = password or self.defaultPassword
        call = api_caller(
            "post",
            "/utilisateurs/authenticate",
            body=json.dumps({"Username": email, "Password": password}),
        )
        return call["IdToken"]
