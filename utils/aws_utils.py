import json
import os
import traceback
import urllib
from collections.abc import Generator
from queue import Empty, Queue
from threading import Lock, Thread

import boto3
from botocore.config import Config
from botocore.exceptions import ClientError
from mypy_boto3_s3.service_resource import ObjectSummary, S3ServiceResource
from mypy_boto3_stepfunctions.client import SFNClient
from mypy_boto3_stepfunctions.type_defs import StartExecutionOutputTypeDef

from utils.dict_utils import first, get
from utils.log_utils import log_err, log_info, log_info_json

default_lambda_config = Config(retries={"total_max_attempts": 1}, read_timeout=780)


def _get_sercret(secret_name, region_name="eu-west-1"):
    local_filename = f"{os.environ.get('CACHE_BASE_DIR', '/tmp/MyResa')}/secret_manager/{secret_name}"
    try:
        with open(local_filename) as file:
            secret = file.read()
    except OSError:
        session = boto3.session.Session()
        client = session.client(service_name="secretsmanager", region_name=region_name)
        get_secret_value_response = client.get_secret_value(SecretId=secret_name)
        secret = get_secret_value_response["SecretString"]
        os.makedirs(os.path.dirname(local_filename), exist_ok=True)
        with open(local_filename, "w") as file:
            file.write(secret)
    return secret


def get_secret(secret_name, region_name="eu-west-1", default=None):
    try:
        secret = _get_sercret(secret_name, region_name)
        secret_dict = json.loads(secret)
    except ClientError as err:
        if err.response["Error"]["Code"] in (
            "ResourceNotFoundException",
            "AccessDeniedException",
        ):
            return default
        else:
            errmsg = "ERROR: 500 Error retrieving required credentials"
            log_err(traceback.format_exc())
            raise Exception(errmsg)
    except BaseException:
        errmsg = "ERROR: 500 Error retrieving required credentials"
        log_err(traceback.format_exc())
        raise Exception(errmsg)

    return secret_dict


def write_secret(secret_name, secret_value, region_name="eu-west-1"):
    client = boto3.client("secretsmanager")

    client.create_secret(Name=secret_name, SecretString=secret_value)


def get_secret_string(secret_name, region_name="eu-west-1"):
    try:
        secret = _get_sercret(secret_name, region_name)
    except BaseException:
        errmsg = "ERROR: 500 Error retrieving required credentials"
        log_err(traceback.format_exc())
        raise Exception(errmsg)

    return secret


def check_s3_file_exist(bucket_name, object_name):
    try:
        s3 = boto3.resource("s3")
        result = s3.Object(bucket_name, object_name).load()
        return True
    except Exception:
        return False


def load_s3_file(bucket_name: str, object_name: str, no_cache=False, write_cache=True, decode_utf=True) -> str:
    """
    Load a file from S3.
    Load a cached version of the S3 file stored in /tmp if it exists, else it downloads the file from S3 and store a copy on the /tmp folder
    @param bucket_name: The bucket to use
    @param object_name: The path/key to the object
    @return: The object as string
    """
    local_filename = f"{os.environ.get('CACHE_BASE_DIR', '/tmp/MyResa')}/s3/{bucket_name}/{object_name}"

    def _load_from_s3():
        s3 = boto3.resource("s3")
        result = s3.Object(bucket_name, object_name).get()["Body"].read()
        if write_cache:
            os.makedirs(os.path.dirname(local_filename), exist_ok=True)
            with open(local_filename, "bw") as file:
                file.write(result)
        return result

    try:
        if no_cache:
            result = _load_from_s3()
        else:
            with open(local_filename, "br") as file:
                result = file.read()
    except OSError:
        result = _load_from_s3()
    except Exception as e:
        log_err(traceback.format_exc())
        log_info(bucket_name)
        log_info(object_name)
        raise e

    if decode_utf:
        return result.decode("utf-8")
    else:
        return result


def upload_s3_file(bucket_name, object_name, data) -> dict:
    try:
        s3_client = boto3.client("s3")
        return s3_client.put_object(Bucket=bucket_name, Key=object_name, Body=data)
    except Exception as e:
        log_err(traceback.format_exc())
        log_info(bucket_name)
        log_info(object_name)
        raise e


def create_s3_presigned_url(bucket_name, object_name, expiration=3600, method="get", ContentType="*"):
    """
    Generate a presigned URL to share an S3 object

    :param bucket_name: Bucket to target
    :param object_name: Object (path) to target
    :param expiration: Time in seconds for the presigned URL to remain valid
    :param method: Method to allow
    :return: Presigned URL as string. If error, returns None.
    """
    # Generate a presigned URL for the S3 object
    s3_client = boto3.client("s3")
    try:
        params = {"Bucket": bucket_name, "Key": object_name}
        if method.lower() == "put":
            params["ContentType"] = ContentType
        response = s3_client.generate_presigned_url(
            f"{method.lower()}_object",
            Params=params,
            ExpiresIn=expiration,
            HttpMethod=method.upper(),
        )
    except ClientError:
        log_err(traceback.format_exc())
        log_info(bucket_name)
        log_info(object_name)
        return None

    # The response contains the presigned URL
    return response


def get_sns_subscriptions(sns_arn):
    try:
        sns = boto3.client("sns")
        result = sns.list_subscriptions_by_topic(TopicArn=sns_arn)
        return result["Subscriptions"]
    except Exception as e:
        log_err(traceback.format_exc())
        raise e


def publishSNS(TopicArn, message, subject="Error"):
    try:
        client = boto3.client("sns")
        client.publish(TopicArn=TopicArn, Message=message, Subject=subject)
    except Exception as e:
        log_err(traceback.format_exc())
        raise e


def binary_to_rows(binary_file):
    try:
        result = binary_file.decode("utf-8").splitlines()
        return result
    except Exception:
        errmsg = "ERROR 500: erreur dans le decodage du fichier"
        log_err(traceback.format_exc())
        raise Exception(errmsg)


def splitPath(path):
    bucket, key = path.split("/", 2)[-1].split("/", 1)
    return bucket, key


def write_s3_file(bucket_name, object_name, content):
    try:
        s3 = boto3.resource("s3")
        result = s3.Object(bucket_name, object_name).put(Body=content)
    except Exception as e:
        log_err(traceback.format_exc())
        raise Exception(e)


def delete_s3_file(bucket_name, object_name):
    try:
        s3 = boto3.resource("s3")
        result = s3.Object(bucket_name, object_name).delete()
    except Exception as e:
        log_err(traceback.format_exc())
        raise Exception(e)


def list_s3_files(bucket_name: str, prefix: str | None = None) -> Generator[ObjectSummary]:
    """
    List all files in a S3 bucket.

    Parameters
    ----------
    bucket_name : str
        The name of the S3 bucket.
    prefix : str, optional
        The prefix to filter the files, by default None

    Returns
    -------
    Generator[ObjectSummary]
        A generator of ObjectSummary objects.

    """
    try:
        s3: S3ServiceResource = boto3.resource("s3")
        bucket = s3.Bucket(bucket_name)
        object_list = bucket.objects.all()

        if prefix:
            object_list = object_list.filter(Prefix=prefix)

        yield from object_list
    except Exception:
        log_err(traceback.format_exc())
        log_info_json({"Bucket": bucket_name, "Prefix": prefix})
        raise


def invoke_lambda(**kwargs: any) -> dict:
    """
    Invoke an AWS Lambda function using the provided parameters.

    This function initializes a lambda client with a default timeout and retry config

    Parameters
    ----------
    kwargs : any
        Any keyword arguments that are directly passed to the boto3 client's
        `invoke` method. Refer to the boto3 documentation for details on
        the supported parameters.

    Returns
    -------
    dict
        The response from the Lambda `invoke` API call, as returned by
        the boto3 client.

    """
    client = boto3.client("lambda", config=default_lambda_config)
    return client.invoke(**kwargs)


def get_dynamodb_table(name):
    dynamodb = boto3.resource("dynamodb")
    return dynamodb.Table(name)


def put_dynamodb_item(table_name, item):
    """
    Insère un élément dans une table DynamoDB spécifiée.

    :param table_name: Le nom de la table DynamoDB.
    :param item: Un dictionnaire représentant l'élément à insérer. Doit inclure les clés primaires.
    """
    table = get_dynamodb_table(table_name)
    response = table.put_item(Item=item)
    return response


def get_dynamodb_items(table_name, key_value):
    table = get_dynamodb_table(table_name)
    try:
        response = table.get_item(Key=key_value)
        item = response.get("Item", None)
        return item
    except Exception as e:
        return f"Erreur lors de la récupération de l'élément: {e!s}"


def get_invoke_url(region, stage, apiId):
    return "https://" + apiId + ".execute-api." + region + ".amazonaws.com/" + stage


def get_resource_key(resource_name):
    return "resources/" + os.environ["API_VERSION"] + "/" + resource_name


def scanAll(tableName, scanAttr=None, nb_segments=16):
    # TODO: investiguate locking (what if items are added while scanning ?)
    if scanAttr is None:
        scanAttr = {}
    table = get_dynamodb_table(tableName)
    count = table.item_count
    items = []

    class AsyncScan(Thread):
        __lock = Lock()

        def __init__(self, segment, total_segments, ret):
            Thread.__init__(self)
            self.segment = segment
            self.total_segments = total_segments
            self.ret = ret
            self.start()

        def run(self):
            scan_ = scanAll_with_LastEvaluatedKey(
                table,
                {
                    **scanAttr,
                    "Segment": self.segment,
                    "TotalSegments": self.total_segments,
                },
            )
            self.ret[self.segment] = scan_

    ret = [None] * nb_segments
    threads = [AsyncScan(i, nb_segments, ret) for i in range(nb_segments)]
    [thread.join() for thread in threads]
    for seg in ret:
        for item in seg:
            items.append(item)
    return items


def scan_all_generator_parallel(table_name, scan_attr=None, nb_segments=16):
    if scan_attr is None:
        scan_attr = {}
    queue = Queue()

    class AsyncScan(Thread):
        __lock = Lock()

        def __init__(self, segment, total_segments):
            Thread.__init__(self)
            self.segment = segment
            self.total_segments = total_segments

        def run(self):
            for user in scan_all_generator(
                table_name,
                {
                    **scan_attr,
                    "Segment": self.segment,
                    "TotalSegments": self.total_segments,
                },
            ):
                queue.put(user)

    [AsyncScan(i, nb_segments).start() for i in range(nb_segments)]

    while True:
        try:
            yield queue.get(True, 10)
        except Empty:
            break


def scan_all_generator(table_name, scan_attr=None):
    scan_attr = scan_attr or {}
    last_evaluated_key = None

    table = get_dynamodb_table(table_name)

    while True:
        params = scan_attr
        if last_evaluated_key:
            params["ExclusiveStartKey"] = last_evaluated_key
        scan_ = table.scan(**params)
        last_evaluated_key = get(scan_, "LastEvaluatedKey")
        for item in get(scan_, "Items", []):
            yield item

        if not last_evaluated_key:
            break


def scanAll_with_LastEvaluatedKey(table, scanAttr=None):
    if scanAttr is None:
        scanAttr = {}
    items = []
    LastEvaluatedKey = None

    def scan():
        nonlocal LastEvaluatedKey
        params = scanAttr
        if LastEvaluatedKey:
            params["ExclusiveStartKey"] = LastEvaluatedKey
        scan_ = table.scan(**params)
        LastEvaluatedKey = get(scan_, "LastEvaluatedKey")
        for item in get(scan_, "Items", []):
            items.append(item)

    scan()
    while LastEvaluatedKey:
        scan()
    return items


def create_presigned_url(bucket_name, object_name, filename=None, expiration=3600):
    """
    Generate a presigned URL to share an S3 object

    :param bucket_name: string
    :param object_name: string
    :param expiration: Time in seconds for the presigned URL to remain valid
    :return: Presigned URL as string. If error, returns None.
    """
    # Generate a presigned URL for the S3 object
    s3_client = boto3.client("s3")
    try:
        params = {"Bucket": bucket_name, "Key": object_name}
        if filename:
            params["ResponseContentDisposition"] = "attachment; filename = {}".format(urllib.parse.quote(filename, safe=""))

        response = s3_client.generate_presigned_url("get_object", Params=params, ExpiresIn=expiration)
    except ClientError as e:
        log_err(e)
        return None

    # The response contains the presigned URL
    return response


def create_presigned_post(bucket_name, object_name, fields=None, conditions=None, expiration=3600):
    """
    Generate a presigned URL S3 POST request to upload a file

    :param bucket_name: string
    :param object_name: string
    :param fields: Dictionary of prefilled form fields
    :param conditions: List of conditions to include in the policy
    :param expiration: Time in seconds for the presigned URL to remain valid
    :return: Dictionary with the following keys:
        url: URL to post to
        fields: Dictionary of form fields and values to submit with the POST
    :return: None if error.
    """
    # Generate a presigned S3 POST URL
    s3_client = boto3.client("s3")
    try:
        response = s3_client.generate_presigned_post(
            bucket_name,
            object_name,
            Fields=fields,
            Conditions=conditions,
            ExpiresIn=expiration,
        )
    except ClientError as e:
        log_err(e)
        return None

    # The response contains the presigned URL and required fields
    return response


def send_sqs(queue, payload):
    sqs = boto3.client("sqs")
    message = json.dumps(payload)
    sqs.send_message(QueueUrl=queue, MessageBody=message)


def get_last_log(logGroup):
    cloudwatch = boto3.client("logs")
    describe_data = cloudwatch.describe_log_streams(logGroupName=logGroup, orderBy="LastEventTime", descending=True, limit=1)
    logStreamName = get(first(get(describe_data, "logStreams", []), {}), "logStreamName")
    if not logStreamName:
        return None
    data = cloudwatch.get_log_events(logGroupName=logGroup, logStreamName=logStreamName)
    return [get(x, "message") for x in get(data, "events", []) if get(x, "message")]


def load_env():
    env_dict = json.loads(load_s3_file(os.environ["BUCKET"], os.environ["ENV_FILE"]))
    for env, var in env_dict.items():
        if env not in os.environ:
            os.environ[env] = str(var)


def start_state_machine(state_machine_name: str, input_: dict | None = None) -> StartExecutionOutputTypeDef:
    """Start a Step Function execution."""
    sfn: SFNClient = boto3.client("stepfunctions")

    if input_ is None:
        input_ = {}

    return sfn.start_execution(
        stateMachineArn=f"arn:aws:states:eu-west-1:{os.environ['ACCOUNT_ID']}:stateMachine:{state_machine_name}-{os.environ['STAGE']}:{os.environ['API_STAGE']}",
        input=json.dumps(input_),
    )
