import json
from datetime import datetime
from decimal import Decimal


class NoFailEncoder(json.JSONEncoder):
    def default(self, obj):
        try:
            if isinstance(obj, Decimal):
                num = float(obj)
                if num.is_integer():
                    num = int(obj)
                return num
            if isinstance(obj, (datetime, bytes)):
                return obj.__str__()
            return json.JSONEncoder.default(self, obj)
        except TypeError:
            if hasattr(obj, "__dict__"):
                return vars(obj)
            else:
                return repr(obj)
