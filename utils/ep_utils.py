from pyproj import Transformer

wgs86_to_lambert_transformer = Transformer.from_crs(crs_from=4326, crs_to=6190)
lambert_to_wgs86_transformer = Transformer.from_crs(crs_from=6190, crs_to=4326)


def lambert_to_wgs86(colamb_x, colamb_y):
    return lambert_to_wgs86_transformer.transform(colamb_x, colamb_y)


def wgs86_to_lambert(lat, long):
    return wgs86_to_lambert_transformer.transform(lat, long)


def hash_(x):
    return str(hash(x)).encode()
