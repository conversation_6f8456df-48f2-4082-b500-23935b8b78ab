"""
Smart consumption utilities for processing energy consumption data.

This module provides utilities for processing smart meter consumption data,
including date range validation, data processing, and CSV export functionality.
It supports both electricity and gas consumption data with localization
support for French and German languages.
"""

import os
from datetime import datetime, timedelta

import pytz
from dateutil.relativedelta import relativedelta

from utils.dict_utils import get, keydefaultdict

# Belgium timezone for localizing datetime objects
belgium_tz = pytz.timezone("Europe/Brussels")

# Localized meter names for CSV export
CSV_METER_NAME: dict = {
    "FR": "Compteur communicant",
    "DE": "Kommunikationsfähiger Zähler",
}

# Localized CSV headers for different languages
CSV_HEADERS: dict = {
    "FR": ["Du (date)", "De (heure)", "Au (date)", "À (heure)", "Code EAN", "Compteur", "Type de compteur", "Registre", "Volume", "Unité", "Statut de validation"],
    "DE": ["Vom (Datum)", "Um (Uhrzeit)", "Bis zum (Datum)", "<PERSON> (Uhrzeit)", "EAN-Code", "Zähler", "Zählertyp", "Register", "Menge", "Einheit", "Validierungsstatus"],
}

# Mapping of reading type IDs to localized registry names
CSV_REGISTRY_MAP: dict = {
    "FR": keydefaultdict(
        lambda x: x,  # return key as value if missing
        {
            "1.8.0": "Prélèvement Total",
            "1.8.1": "Prélèvement Jour",
            "1.8.2": "Prélèvement Nuit",
            "2.8.0": "Injection Total",
            "2.8.1": "Injection Jour",
            "2.8.2": "Injection Nuit",
        },
    ),
    "DE": keydefaultdict(
        lambda x: x,  # return key as value if missing
        {
            "1.8.0": "Entnahme gesamt",
            "1.8.1": "Entnahme Tag",
            "1.8.2": "Entnahme Nacht",
            "2.8.0": "Einspeisung gesamt",
            "2.8.1": "Einspeisung Tag",
            "2.8.2": "Einspeisung Nacht",
        },
    ),
}

# Mapping of measurement states to localized status descriptions
CSV_STATUS_MAP: dict = {
    "FR": keydefaultdict(
        lambda x: x,  # return key as value if missing
        {
            "valide": "Lu",
            "estimated": "Estimé",
            "edited": "Corrigé",
            "": "Pas de consommation",
            None: "Pas de consommation",
        },
    ),
    "DE": keydefaultdict(
        lambda x: x,  # return key as value if missing
        {
            "valide": "Abgelesen",
            "estimated": "Geschätzt",
            "edited": "Berichtigt",
            "": "Kein Verbrauch",
            None: "Kein Verbrauch",
        },
    ),
}

# Mapping of energy types to Redshift tables for different aggregation types
DICT_TYPE_TO_TABLE: dict = {
    "elec": {
        "HOURLY": "public.cumulativeactiveenergyp15m",
        "DAILY": "public.cumulativeactiveenergyp1d",
        "MONTHLY": "public.cumulativeactiveenergyp1d",
    },
    "gaz": {
        "HOURLY": "public.cumulativestoredvolumep1h",
        "DAILY": "public.cumulativestoredvolumep1d",
        "MONTHLY": "public.cumulativestoredvolumep1d",
    },
}


def get_valid_date_ranges_from_params(energy: str, start_date: str, end_date: str, contract_date: list[tuple[str, str]]) -> list:
    """
    Calculate valid date ranges based on energy type, requested dates, and contract periods.

    This function determines the intersection of requested date ranges with active
    contract periods, applying energy-specific time adjustments (e.g., gas readings
    are typically taken at 6 AM).

    Parameters
    ----------
    energy : str
        Type of energy consumption data ('elec' for electricity, 'gaz' for gas).
        Gas readings will have their time adjusted to 6 AM.
    start_date : str
        Start date in ISO format (YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS).
    end_date : str
        End date in ISO format (YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS).
    contract_date : list[tuple[str, str]]
        List of contract period tuples, each containing (start_date, end_date)
        in YYYYMMDD format.

    Returns
    -------
    list[tuple[datetime, datetime]]
        List of valid date range tuples, each containing timezone-aware datetime
        objects representing the intersection of requested dates and contract periods.
        Returns empty list if no valid ranges exist.

    """
    start_date = datetime.fromisoformat(start_date)
    end_date = datetime.fromisoformat(end_date)

    # In case of gaz, set the data date to 6h
    if energy == "gaz":
        start_date = start_date.replace(hour=6)
        end_date = end_date.replace(hour=6)

    valid_date_ranges = []

    for _contract_start, _contract_end in contract_date:
        contract_start = datetime.strptime(_contract_start, "%Y%m%d")
        contract_end = datetime.strptime(_contract_end, "%Y%m%d")

        valid_start = max(start_date, contract_start)
        valid_end = min(end_date, contract_end)

        if valid_start <= valid_end:
            valid_date_ranges.append((belgium_tz.localize(valid_start), belgium_tz.localize(valid_end)))

    return valid_date_ranges if valid_date_ranges else []


def process_data_response(data: list[dict], energy: str, start_date: str, end_date: str, slot_type: str) -> list[dict]:
    """
    Process raw smart meter data response into consumption records with time slots.

    This function transforms raw meter reading data into consumption records by
    calculating consumption differences between consecutive readings and aligning
    them with predefined time slots. It handles multiple reading types and ensures
    data completeness by filling missing time slots with empty records.

    Parameters
    ----------
    data : list[dict]
        List of raw meter reading dictionaries. Each dictionary should contain
        keys like 'readingtypeid', 'measuredatetime', 'measurevalue', 'ean',
        'meterid', 'readingfrequency', and 'measureunit'.
    energy : str
        Type of energy consumption data ('elec' for electricity, 'gaz' for gas).
        Gas readings will have their time adjusted to 6 AM.
    start_date : str
        Start date in ISO format (YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS).
    end_date : str
        End date in ISO format (YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS).
    slot_type : str
        Type of time slot aggregation. Options are:
        - 'HOURLY': 15-minute intervals for electricity, 1-hour for gas
        - 'DAILY': 1-day intervals
        - 'MONTHLY': 1-month intervals

    Returns
    -------
    list[dict]
        List of processed consumption records. Each record contains the original
        meter data plus calculated 'consumption' and 'measuredatetime_from' fields.
        Missing time slots are filled with empty records containing None values
        for consumption-related fields.

    """
    if not data:
        return []

    # Pre-calculate time slots
    time_slots = _get_time_slots(
        energy,
        start_date,
        end_date,
        slot_type,
    )

    # Group data by reading type for O(1) lookup
    rows_by_type = {}
    for row in data:
        reading_type = row["readingtypeid"]
        if reading_type not in rows_by_type:
            rows_by_type[reading_type] = []
        rows_by_type[reading_type].append(row)

    # Sort each group by measurement time
    for reading_type in rows_by_type:
        rows_by_type[reading_type].sort(key=lambda x: x["measuredatetime"])

    result = []
    previous_row = {}

    # for each reading type, we loop through all time slots
    for reading_type_id in rows_by_type:
        type_rows = rows_by_type[reading_type_id]
        row_index = 0
        time_slots_index = 0
        # we then iterate through all data rows and all time slots at the same time
        # this is more humanly complex, but avoid having to do a search loop after and having an O(n²) complexity
        while time_slots_index < len(time_slots):
            slot_start, slot_end = time_slots[time_slots_index]

            # create empty record for the missing slot in case there are no more data rows left
            if row_index >= len(type_rows):
                result.append(_create_empty_record(type_rows[0], reading_type_id, slot_start, slot_end))
                time_slots_index += 1
                continue

            row = type_rows[row_index]

            # calculate consumption and measuredatetime_from if consumption is already present, it means the row stayed the same and is already processed
            if "consumption" not in row:
                localized_time = belgium_tz.localize(row["measuredatetime"]) if row["measuredatetime"].tzinfo is None else row["measuredatetime"].astimezone(belgium_tz)
                row["measuredatetime"] = localized_time.isoformat()

                group_key = (row["ean"], row["meterid"], row["readingfrequency"], row["readingtypeid"])
                if group_key in previous_row:
                    row["consumption"] = round(row["measurevalue"] - previous_row[group_key]["measurevalue"], 3)
                    row["measuredatetime_from"] = previous_row[group_key]["measuredatetime"]
                else:
                    row["consumption"] = None
                    row["measuredatetime_from"] = None
                previous_row[group_key] = row

            # drop first empty consumption of each reading type
            # or drop rows that are before the current slot (this happens when we switch between summer and winter time, what a mess)
            if row["consumption"] is None or slot_start > row["measuredatetime_from"]:
                row_index += 1
                continue

            # keep the data row that falls within the current slot and move to the next one
            if slot_start <= row["measuredatetime_from"] < slot_end:
                result.append(row)
                row_index += 1
            # else, create an empty record for the missing slot and keep looking at the same data row
            else:
                result.append(_create_empty_record(type_rows[0], reading_type_id, slot_start, slot_end))

            time_slots_index += 1

    return result


def _get_time_slots(energy: str, start_date: str, end_date: str, slot_type: str) -> list[tuple[str, str]]:
    """
    Generate time slots between start and end dates based on energy type and slot frequency.

    Creates a sequence of time intervals for data aggregation based on the specified
    slot type and energy source. Electricity data uses 15-minute intervals for hourly
    slots, while gas uses 1-hour intervals.

    Parameters
    ----------
    energy : str
        Type of energy consumption ('elec' for electricity, 'gaz' for gas).
        Affects the time delta for hourly slots.
    start_date : str
        Start date in ISO format (YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS).
    end_date : str
        End date in ISO format (YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS).
    slot_type : str
        Type of time slot aggregation. Options are:
        - 'HOURLY': 15-minute intervals for electricity, 1-hour for gas
        - 'DAILY': 1-day intervals
        - 'MONTHLY': 1-month intervals

    """
    start_date = datetime.fromisoformat(start_date)
    end_date = datetime.fromisoformat(end_date) + relativedelta(days=1)

    if slot_type == "HOURLY":
        delta = timedelta(minutes=15) if energy == "elec" else timedelta(hours=1)
    elif slot_type == "DAILY":
        delta = timedelta(days=1)
    else:  # MONTHLY
        delta = relativedelta(months=1)

    slots = []
    current = start_date
    while current < end_date:
        next_slot = min((current + delta).replace(day=1), end_date) if slot_type == "MONTHLY" else min(current + delta, end_date)
        slots.append((belgium_tz.localize(current).isoformat(), belgium_tz.localize(next_slot).isoformat()))
        current = next_slot

    return slots


def _create_empty_record(template: dict, reading_type_id: str, start_time: str, end_time: str) -> dict:
    """
    Create an empty consumption record for missing time slots.

    Generates a placeholder record with null consumption values for time periods
    where no meter readings are available. This ensures data continuity and
    consistent time series structure.

    Parameters
    ----------
    template : dict
        Template record containing meter metadata (meterid, ean, readingfrequency,
        measureunit). Used to maintain consistent meter identification across
        empty records.
    reading_type_id : str
        Reading type identifier (e.g., '1.8.0' for total consumption).
    start_time : str
        Start time of the empty slot in ISO format with timezone.
    end_time : str
        End time of the empty slot in ISO format with timezone.

    Returns
    -------
    dict
        Empty consumption record with meter metadata from template and None
        values for consumption-related fields (measurevalue, consumption,
        measurestate, standardizationtimestamp).

    """
    return {
        "meterid": template["meterid"],
        "measuredatetime": end_time,
        "measuredatetime_from": start_time,
        "measurevalue": None,
        "readingtypeid": reading_type_id,
        "measurestate": None,
        "ean": template["ean"],
        "readingfrequency": template["readingfrequency"],
        "standardizationtimestamp": None,
        "measureunit": template["measureunit"],
        "consumption": None,
    }


def data_to_csv(processed_data: list[dict]) -> str:
    """
    Convert processed consumption data into a localized CSV-formatted string.

    Transforms processed meter consumption data into a CSV format suitable for
    export, with localized headers and field mappings based on the system
    language setting (LANG environment variable).

    Parameters
    ----------
    processed_data : list[dict]
        List of processed consumption records. Each record should contain
        fields like 'measuredatetime', 'measuredatetime_from', 'ean', 'meterid',
        'readingtypeid', 'consumption', 'measureunit', and 'measurestate'.

    Returns
    -------
    str
        A CSV-formatted string with semicolon (`;`) delimiter containing:
        - Localized headers as the first row
        - One row per consumption record with formatted dates and localized values
        - Date format: DD-MM-YY, Time format: HH:MM:SS

    """
    csv_rows = [CSV_HEADERS[os.environ["LANG"]]]
    for row in processed_data:
        from_date_str = from_time_str = to_date_str = to_time_str = ""
        if "measuredatetime_from" in row:
            from_date = datetime.fromisoformat(row["measuredatetime_from"])
            from_date_str = from_date.strftime("%d-%m-%y")
            from_time_str = from_date.strftime("%H:%M:%S")
        if "measuredatetime" in row:
            to_date = datetime.fromisoformat(row["measuredatetime"])
            to_date_str = to_date.strftime("%d-%m-%y")
            to_time_str = to_date.strftime("%H:%M:%S")
        csv_rows.append(
            [
                from_date_str,
                from_time_str,
                to_date_str,
                to_time_str,
                get(row, "ean", ""),
                get(row, "meterid", ""),
                CSV_METER_NAME[os.environ["LANG"]],
                CSV_REGISTRY_MAP[os.environ["LANG"]][row.get("readingtypeid", "")],
                get(row, "consumption", "0.0"),
                get(row, "measureunit", ""),
                CSV_STATUS_MAP[os.environ["LANG"]][row.get("measurestate", "")],
            ],
        )
    return "\n".join([";".join(map(str, row)) for row in csv_rows])
