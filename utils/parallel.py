from threading import Thread


class Parallel(Thread):
    def __init__(self, fn, result=None, key="__default__"):
        Thread.__init__(self)
        if result is None:
            result = {}
        self.result = result
        self.key = key
        self.fn = fn
        self.start()

    def run(self):
        self.result[self.key] = self.fn()

    def get(self):
        self.join()
        return self.result.get(self.key)


def join(threads):
    for t in threads:
        t.join()


def concurrently(*lambdas):
    result = {}
    threads = []
    for i, l in enumerate(lambdas):
        result[str(i)] = None  # Init result value to avoid missing result if thread encounter an exception
        threads.append(Parallel(l, result, str(i)))
    join(threads)
    return tuple([item[1] for item in sorted(list(result.items()))])
