import os

import ldap3

from utils.aws_utils import get_secret
from utils.dict_utils import first, get
from utils.errors import HttpError, NotFoundError, EmailAlreadyUsedError
from utils.log_utils import log_err


class LDAP_CONN:
    conn = None

    host = None
    port = None
    login = None
    password = None
    folder_dn = None

    @staticmethod
    def loadFromSecret(secretName):
        secret = get_secret(secretName)
        host = secret["host"]
        port = secret["port"]
        login = secret["login"]
        password = secret["password"]
        folder_dn = secret["folder_dn"]
        return LDAP_CONN(host, port, login, password, folder_dn)

    def __init__(self, host, port, login, password, folder_dn):
        self.host = host
        self.port = port
        self.login = login
        self.password = password
        self.folder_dn = folder_dn
        url = "ldaps://" + self.host + ":" + str(self.port)
        # url = 'ldaps://localhost:8443'
        server = ldap3.Server(url, get_info=all, use_ssl=True)
        self.conn = ldap3.Connection(server, user=self.login, password=self.password, auto_bind=True)

    def throw(self, statement, err_msg="LDAP error"):
        if not statement:
            log_err(str(self.conn.result))

            mapping = {
                "000021C8": HttpError(409, "user already exist"),
                "0000052D": HttpError(409, "Password doesn't meet policy"),
                "00000056": HttpError(
                    409,
                    "Mot de passe déjà utilisé. Veuillez encoder un nouveau mot de passe.",
                ),
            }

            for key, err in mapping.items():
                if key in self.conn.result["message"]:
                    raise err

            raise HttpError(500, err_msg + " (LDAP " + self.conn.result["description"] + ")")

    def addUser(self, user):
        fullname = user["fullname"]
        objectClass = ["person", "organizationalPerson", "user", "top"]
        email = user["email"]
        uid = user["username"]
        user_dn = "CN=" + uid + "," + self.folder_dn
        userPrincipalName = user["email"]
        entry = {
            "uid": uid,
            "cn": uid,
            "name": fullname,
            "givenName": user["firstname"],
            "sn": user["lastname"],
            "mail": email,
            "sAMAccountName": user["username"],
            "userPrincipalName": userPrincipalName,
            "gidNumber": "513",
            "description": "ENV={}\n".format(get(os.environ, "STAGE", "UNKNOWN")),
        }
        if get(user, "phone"):
            entry = {**entry, "telephoneNumber": user["phone"]}
        if get(user, "phone_fixe"):
            entry = {**entry, "homePhone": user["phone_fixe"]}

        self.throw(
            self.conn.add(dn=user_dn, object_class=objectClass, attributes=entry),
            "Unable to register user",
        )
        self.throw(
            self.conn.extend.microsoft.unlock_account(user_dn),
            "Unable to unlock account",
        )
        self.throw(
            self.conn.extend.microsoft.modify_password(user_dn, user["password"]),
            "Unable to set password",
        )
        self.throw(
            self.conn.modify(user_dn, changes={"userAccountControl": (ldap3.MODIFY_REPLACE, [512])}),
            "Unable to set userAccountControl",
        )
        return uid

    def findUser(self, attribute, fullname):
        self.conn.search(
            search_base=self.folder_dn,
            search_scope=ldap3.SUBTREE,
            search_filter="(" + attribute + "=" + fullname + ")",
            attributes="*",
        )

        try:
            resp = self.conn.response[len(self.conn.response) - 1]
            if len(resp["attributes"]["cn"]) <= 0:
                #                    raise NotFoundError('Attribute `{}` not found in AD'.format(fullname))
                return None
            if len(resp["attributes"]["uid"]) <= 0:
                #                raise NotFoundError('Attribute `{}` not found in AD unable to retrieve uid'.format(fullname))
                return None
        except (Exception, IndexError):
            # raise NotFoundError('Attribute `{}` not found in AD'.format(fullname))
            return None

        return {attr: first(value) for attr, value in resp["attributes"].items()}

    def list_users_common_info(self):
        users = []
        cookie = None
        first_run = True

        while cookie or first_run:
            first_run = False
            self.conn.search(
                search_base=self.folder_dn,
                search_scope=ldap3.SUBTREE,
                search_filter="(uid=*)",
                attributes=[
                    "telephoneNumber",
                    "givenName",
                    "sn",
                    "uid",
                    "mail",
                    "pwdLastSet",
                ],
                paged_cookie=cookie,
                paged_size=1000,
            )
            for entry in self.conn.response:
                user_info = {}
                for info in entry["attributes"]:
                    if len(entry["attributes"][info]) >= 1:
                        user_info[info] = entry["attributes"][info][0]
                    else:
                        user_info[info] = None
                users.append(user_info)
            cookie = self.conn.result["controls"]["1.2.840.113556.1.4.319"]["value"]["cookie"]

        return users

    def setUserAttribute(self, uid, attribute, value):
        # change uid to cn
        cn = self.findUser("cn", uid)
        if not cn:
            raise NotFoundError("Uid `{}` not found in AD".format(uid))
        cn = cn["cn"]
        user_dn = "CN=" + cn + "," + self.folder_dn
        changes = dict()
        changes[attribute] = [(ldap3.MODIFY_REPLACE, [value])]
        if not self.conn.modify(dn=user_dn, changes=changes):
            log_err(str(self.conn.result))
            raise HttpError(500, "Unable to modify user : " + self.conn.result["description"])

    def changePassword(self, user_dn, new_password, old_password=None):
        user_dn = "CN=" + user_dn + "," + self.folder_dn
        self.throw(
            self.conn.extend.microsoft.modify_password(user_dn, new_password=new_password, old_password=old_password),
            "Unable to set password",
        )

    def deleteUserByUid(self, uid):
        user = self.findUser("uid", uid)
        if not user:
            return False
        cn = user["cn"]
        user_dn = "CN=" + cn + "," + self.folder_dn
        self.conn.delete(user_dn)
        return True

    def close(self):
        if self.conn:
            self.conn.unbind()

    def __del__(self):
        self.close()

    def __enter__(self):
        return self

    def __exit__(self, type, value, traceback):
        self.close()


class LDAP_MOCK_CONN(LDAP_CONN):
    conn = None

    host = None
    port = None
    login = None
    password = None
    folder_dn = "OU=UserAccounts,OU=ORG,DC=resaclients,DC=intra"

    @staticmethod
    def loadFromSecret(secretName=""):
        return LDAP_MOCK_CONN("host", "port", "login", "password", "folder_dn")

    def __init__(self, host, post, login, password, folder_dn):
        self.host = "localhost"
        self.port = 636
        self.login = "login"
        self.password = "password"
        url = "ldaps://" + self.host + ":" + str(self.port)
        server = ldap3.Server("my_fake_server")
        self.conn = ldap3.Connection(
            server,
            user=self.login,
            password=self.password,
            client_strategy=ldap3.MOCK_SYNC,
        )
        self.conn.bind()


LDAP = LDAP_MOCK_CONN if get(os.environ, "USE_MOCK") else LDAP_CONN


def check_if_mail_already_used(email: str) -> None:
    """
    @param email: The mail to check
    @raise EmailAlreadyUsedError: If mail already used
    """
    ldap = LDAP.loadFromSecret(os.environ["AD_SECRET"])
    try:
        if ldap.findUser("mail", email):
            raise EmailAlreadyUsedError
    finally:
        del ldap
