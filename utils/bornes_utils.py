import os
import uuid
from datetime import datetime
from decimal import Decimal
from typing import List

import boto3
import requests

from utils.aws_utils import get_dynamodb_table, upload_s3_file
from utils.errors import BadRequestError, InternalServerError
from utils.models.borne_recharge_model import Born<PERSON><PERSON><PERSON><PERSON><PERSON>, BorneRechargeInstant
from utils.type_utils import float_format

table = get_dynamodb_table(os.environ["BornesRechargeTable"])


def convert_decimals_to_floats(d):
    for key, value in d.items():
        if isinstance(value, Decimal):
            d[key] = float(value)
        elif isinstance(value, dict):
            convert_decimals_to_floats(value)


def get_borne_recharge_from_db(uuid, table):
    try:
        response = table.query(
            KeyConditionExpression=boto3.dynamodb.conditions.Key("Uuid").eq(str(uuid)),
            IndexName="uuid-index",
        )
    except ValueError:
        raise BadRequestError("Error querying database", error_code="DB_PROBLEM")

    items = response.get("Items", [])
    if not items:
        raise BadRequestError("Empty data", error_code="NO_DATA")

    return BorneRecharge.from_dict(items[0])


def verify_ean_access(user, ean_from_body, ean_from_db):
    # todo : remove str conversion when ean str fix is live
    user_ean = [str(ean) for ean in user.ean_ids]
    if str(ean_from_body) not in user_ean or str(ean_from_db) not in user_ean:
        raise BadRequestError("EAN ACCESS DENIED", error_code="NOT_AUTHORIZED")
    return True


def filtrer_borne_non_supprimees(bornes: List[BorneRecharge]) -> List[BorneRecharge]:
    bornes_filtrees = []
    for borne in bornes:
        if borne.supprimer:
            break
        bornes_filtrees.append(borne)
    return bornes_filtrees


def process_bornes(
    list_bornes: List[List[BorneRecharge]],
) -> List[BorneRechargeInstant]:
    borne_data = []

    for bornes in list_bornes:
        bornes_filtrees = filtrer_borne_non_supprimees(bornes)

        if bornes_filtrees:
            borne = process_borne_history(bornes_filtrees)
            borne_data.append(borne)
    return borne_data


def process_borne_history(bornes_filtrees: List[BorneRecharge]) -> BorneRechargeInstant:
    date_aujourdhui = datetime.now().strftime("%Y-%m-%d")

    borne_instant = BorneRechargeInstant.from_dict(bornes_filtrees[0].to_dict())
    # Fetch date and default status based on history of activation / deactivation
    for borne in bornes_filtrees:
        if borne.type_demande.valeur == "ACTIVATE" and borne_instant.borne.date_activation is None:
            borne_instant.borne.date_activation = borne.borne.date
            borne_instant.borne.active = True
        elif borne.type_demande.valeur == "DEACTIVATE" and borne_instant.borne.date_desactivation is None:
            borne_instant.borne.date_desactivation = borne.borne.date
            borne_instant.borne.active = False

    # Reevaluate status based on date_activation / date_desactivation
    if borne_instant.borne.date_activation is not None and borne_instant.borne.date_desactivation is not None:
        if borne_instant.borne.date_activation < borne_instant.borne.date_desactivation:
            borne_instant.borne.active = borne_instant.borne.date_activation <= date_aujourdhui < borne_instant.borne.date_desactivation
        else:
            borne_instant.borne.active = borne_instant.borne.date_activation <= date_aujourdhui or date_aujourdhui < borne_instant.borne.date_desactivation
    elif borne_instant.borne.date_activation is not None:
        borne_instant.borne.active = borne_instant.borne.date_activation <= date_aujourdhui
    elif borne_instant.borne.date_desactivation is not None:
        borne_instant.borne.active = borne_instant.borne.date_desactivation > date_aujourdhui

    return borne_instant


def upload_borne_file(borne_recharge: BorneRecharge):
    if borne_recharge.borne.photo and not borne_recharge.borne.photo.startswith("s3://"):
        # get file from photo and set in photo_b64
        try:
            file_name = f"WS153/{borne_recharge.borne.photo.split('?')[0].split('/')[-1]}"
            file_data = requests.get(borne_recharge.borne.photo).content
            upload_s3_file(os.environ["BUCKET"], file_name, file_data)
            borne_recharge.borne.photo = f"s3://{os.environ['BUCKET']}/{file_name}"
        except:
            raise InternalServerError("Échec sur le fichier", error_code="FILE_ERROR")


def process_and_putDb(borne_recharge: BorneRecharge) -> BorneRecharge:
    borne_recharge.date_creation = datetime.now().isoformat()
    borne_recharge.uuid = uuid.uuid4().hex
    borne_recharge.ean = borne_recharge.borne.ean

    upload_borne_file(borne_recharge)

    # Insert in dynamo
    table.put_item(Item=borne_recharge.to_dict())

    borne_recharge.borne.puissance = float_format(
        borne_recharge.borne.puissance,
        err=BadRequestError(
            message="Puissance should be a float or a str with a float inside it",
            error_code="PUISSANCE_SHOULD_BE_FLOAT",
        ),
    )

    return borne_recharge
