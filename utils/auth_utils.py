import base64
import json
import os
from datetime import datetime

import unidecode
from boto3.dynamodb.conditions import Key, Attr

from utils.aws_utils import get_secret, load_s3_file, get_dynamodb_table
from utils.dict_utils import get
from utils.errors import (
    ForbiddenError,
    UnauthorizedError,
    HttpError,
    NotFoundError,
    BadRequestError,
)
from utils.log_utils import log_err
from utils.string_utils import clean_onboarding_string
from utils.token_utils import (
    decode_token,
    verify_token,
    checkForInvalidation,
    getToken,
    removeTokenType,
    decode_simple_jwt,
)


def getSessionId(headers):
    headers = headers or {}
    return get(headers, "SessionId", get(headers, "sessionid", None))


def get_token_data_from_query_string(event):
    token_ = get(
        event["queryStringParameters"],
        "Token",
        err=BadRequestError("No Token found in queryStrings"),
    )
    checkForInvalidation(token_)
    token = decode_simple_jwt(token_)
    return token


def get_user_data_from_query_string(event):
    token = get_token_data_from_query_string(event)
    if token["expirationTimestamp"] < datetime.now().timestamp():
        raise ForbiddenError(
            "Votre demande d’activation est expirée. Merci de recommencer votre inscription.",
            error_code="TOKEN_EXPIRED",
        )

    return loadUserByUID(token["uid"])


def getUserData(event, allow_ghost=True, only_ghost=False, require_permission=None):
    token = getToken(get(event, "headers", {}))
    uid = None
    isGhost = False
    if token and not only_ghost:
        isGhost = False
        checkForInvalidation(token)
        jsonwebkey = json.loads(load_s3_file(os.environ["BUCKET"], "resources/AD_publickey.json"))
        if not verify_token(token, jsonwebkey):
            raise UnauthorizedError("Token de connexion invalide", error_code="INVALID_TOKEN")
        payload = decode_token(token)
        if not payload:
            raise UnauthorizedError("Token de connexion invalide", error_code="INVALID_TOKEN")
        if "unique_name" not in payload:
            raise HttpError(
                500,
                "Bad Token : The provided token is not an id_token",
                error_code="INVALID_TOKEN",
            )

        def remove_prefix(text, prefix):
            return text[len(prefix) :] if text.startswith(prefix) else text

        uid = remove_prefix(payload["unique_name"], "RESACLIENTS\\")
    elif allow_ghost:
        isGhost = True
        uid = getSessionId(get(event, "headers", {}))
    print("uid = ", uid)
    if not uid:
        sess = "or SessionId " if allow_ghost else ""
        raise UnauthorizedError("No Token {}provided".format(sess), error_code="MISSING_TOKEN")
    table = get_dynamodb_table(os.environ["DYNAMODB"])
    list_ = table.query(
        KeyConditionExpression=Key("uid").eq(uid),
        FilterExpression=Key("valide").eq(not isGhost),
    )["Items"]
    if not list_:
        raise UnauthorizedError(f"Utilisateur inconnu : {uid}", error_code="UNKNOWN_USER")
    user = list_.pop()
    if require_permission:
        if require_permission not in get(user, "permissions", []):
            raise ForbiddenError("Insufficient permission", error_code="PERMISSINON_MISSING")
    return user


def getUserId(event, allow_ghost=True):
    token = getToken(get(event, "headers", {}))
    uid = None
    if token:
        checkForInvalidation(token)
        jsonwebkey = json.loads(load_s3_file(os.environ["BUCKET"], "resources/AD_publickey.json"))
        if not verify_token(token, jsonwebkey):
            raise UnauthorizedError("Token de connexion invalide")
        payload = decode_token(token)
        if not payload:
            raise UnauthorizedError("Token de connexion invalide")
        if "unique_name" not in payload:
            raise HttpError(500, "Bad Token : The provided token is not an id_token")

        def remove_prefix(text, prefix):
            return text[len(prefix) :] if text.startswith(prefix) else text

        uid = remove_prefix(payload["unique_name"], "RESACLIENTS\\")
    elif allow_ghost:
        uid = getSessionId(get(event, "headers", {}))

    if not uid:
        raise UnauthorizedError(f"No Token {'or SessionId ' if allow_ghost else ''}provided")

    return uid


def loadUserByUID(uid):
    table = get_dynamodb_table(os.environ["DYNAMODB"])
    try:
        list_ = get(table.query(KeyConditionExpression=Key("uid").eq(uid)), "Items", [])
    except:
        list_ = []
    if not list_:
        raise NotFoundError(f"Utilisateur inconnu : {uid}")
    return list_.pop()


def loadValideUserByEmail(email):
    table = get_dynamodb_table(os.environ["DYNAMODB"])
    list_ = table.query(
        KeyConditionExpression=Key("email").eq(email) & Attr("valide").eq(True),
        IndexName="email-index",
    )["Items"]
    if not list_:
        raise NotFoundError("Unkown email `{}`".format(email))
    return list_.pop()


def matches_user(field_name, field_value, user_data):
    if not user_data or field_name not in user_data or not user_data[field_name]:
        return False
    if field_name == "ean":
        try:
            return True in [int(user_data["ean"][i]["ean"]) == int(field_value) for i in range(len(user_data["ean"]))]
        except Exception as e:
            log_err(e)
            pass
    elif field_name == "haugazelID":
        try:
            db_value = user_data["haugazelID"]
            id_partenaire_list = [int(item) for item in db_value] if isinstance(db_value, list) else [int(db_value)]
            return int(field_value) in id_partenaire_list
        except Exception as e:
            log_err(e)
            pass
    else:
        return user_data[field_name] == field_value


def load_env_file():
    return json.loads(load_s3_file(os.environ["BUCKET"], os.environ["ENV_FILE"]))


def check_basic_credentials(authorization_header, secret_name):
    if not authorization_header or not authorization_header.startswith("Basic"):
        return False
    username_password_client = str.encode(removeTokenType(authorization_header, "Basic"))
    secret = get_secret(secret_name)
    username_password_server = base64.standard_b64encode(str.encode(secret["DecodedUsername"] + ":" + secret["DecodedPassword"]))
    allow = username_password_client == username_password_server
    return allow


def get_basic_credentials(secret_name):
    secret = get_secret(secret_name)
    username_password = base64.standard_b64encode(str.encode(secret["DecodedUsername"] + ":" + secret["DecodedPassword"]))
    return "Basic " + username_password.decode()


def is_same_user(user_data, sap_user):
    if not user_data or not sap_user:
        return False

    neutralize = lambda s: unidecode.unidecode("".join(e.lower() for e in s if e.isalpha()))

    sap_full_name = clean_onboarding_string(neutralize(get(sap_user, "Prenom", "") + get(sap_user, "Nom", "")))
    my_resa_full_name = clean_onboarding_string(neutralize(get(user_data, "firstname", "") + get(user_data, "lastname", "")))
    name_match = my_resa_full_name and my_resa_full_name == sap_full_name

    sap_email = get(sap_user, "Email")
    my_resa_email = get(user_data, "email")
    email_match = my_resa_email and my_resa_email == sap_email

    sap_phone = get(sap_user, "Tel", "")[-8:] if len(get(sap_user, "Tel", "")) >= 8 else None
    my_resa_phone = get(user_data, "phone", "")[-8:] if len(get(user_data, "phone", "")) >= 8 else None
    phone_match = my_resa_phone and my_resa_phone == sap_phone

    print("Name condition:" + str(name_match))
    print("MyResa:" + str(my_resa_full_name) + " vs SAP: " + str(sap_full_name))
    print("Mail condition:" + str(email_match))
    print("MyResa:" + str(my_resa_email) + " vs SAP: " + str(sap_email))
    print("Phone condition:" + str(phone_match))
    print("MyResa:" + str(my_resa_phone) + " vs SAP: " + str(sap_phone))

    return name_match and (email_match or phone_match)
