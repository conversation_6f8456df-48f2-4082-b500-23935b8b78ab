from datetime import timedelta

from dateutil.easter import easter


def last_day_of_month(any_day):
    # this will never fail
    # get close to the end of the month for any day, and add 4 days 'over'
    next_month = any_day.replace(day=28) + timedelta(days=4)
    # subtract the number of remaining 'overage' days to get last day of current month, or said programattically said, the previous day of the first of next month
    return next_month - timedelta(days=next_month.day)


def add_business_days(input_date, add_days):
    if add_days == 0:
        return input_date
    blank_days = get_blank_days(input_date.year)
    unit = 1 if add_days > 0 else -1
    business_days_to_add = abs(add_days)
    current_date = input_date
    while business_days_to_add > 0:
        current_date += timedelta(days=unit)
        weekday = current_date.weekday()
        if weekday >= 5 or current_date in blank_days:  # sunday = 6
            continue
        business_days_to_add -= 1

    return current_date


def get_blank_days(year):
    import datetime

    return [
        datetime.date(year, 1, 1),
        datetime.date(year, 5, 1),
        datetime.date(year, 5, 1),
        easter(year) + timedelta(days=1),
        easter(year) + timedelta(days=39),
        easter(year) + timedelta(days=50),
        datetime.date(year, 7, 21),
        datetime.date(year, 8, 15),
        datetime.date(year, 11, 1),
        datetime.date(year, 12, 25),
    ]
