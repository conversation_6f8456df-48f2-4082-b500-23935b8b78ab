import json
from dataclasses import dataclass, field
from typing import Optional, Self

from dataclasses_json import Exclude, LetterCase, config, dataclass_json

from utils.auth_utils import getUserData
from utils.DecimalEncoder import DecimalEncoder


@dataclass_json
@dataclass
class Contract:
    ctr_from: str
    ctr_to: str
    num_ctr: int


@dataclass_json
@dataclass
class Ean:
    ean: str
    contract: Optional[list[Contract]] = None
    meterid: Optional[list[str]] = None
    energy: Optional[str] = None
    address_hash: Optional[str] = None
    smart: Optional[bool] = None


@dataclass_json
@dataclass
class Dossier:
    id: str


@dataclass_json(letter_case=LetterCase.PASCAL)
@dataclass
class Adresse:
    localite: Optional[str] = None
    num_rue: Optional[str] = None
    cd_postal: str = field(metadata=config(field_name="Cdpostal"), default=None)
    rue: Optional[str] = None
    code_pays: Optional[str] = None


@dataclass_json(letter_case=LetterCase.PASCAL)
@dataclass
class Commune:
    id: str
    localite: str
    code_postaux: list[int] = field(default_factory=list)
    fonction: Optional[str] = None
    departement: Optional[str] = None
    admin: bool = False
    actif: bool = True
    roles: list[str] = field(default_factory=list)


@dataclass_json
@dataclass
class SmsValidation:
    code: Optional[str] = None
    expire: Optional[str] = None
    phone: Optional[str] = None
    sent: Optional[str] = None
    type: Optional[str] = None


@dataclass_json
@dataclass
class User:
    uid: str
    bp: Optional[int] = None
    lastname: Optional[str] = None
    firstname: Optional[str] = None
    email: Optional[str] = None
    contact_email: Optional[str] = None
    phone: Optional[str] = None
    phone_fixe: Optional[str] = None
    commune: Optional[Commune] = field(metadata=config(field_name="Commune"), default=None)
    adresse: Optional[Adresse] = None
    created_at: Optional[int] = field(metadata=config(field_name="createdAt"), default=None)
    launched_activation: bool = False
    valide: bool = False
    sms_validation: Optional[list[SmsValidation]] = None
    sms_validation_last_try: Optional[str] = None
    valid_phone: bool = False
    valid_contact_email: bool = False
    preferences: dict = field(default_factory=dict)
    consentements: list[dict] = field(default_factory=list)
    ean: list[Ean] = field(default_factory=list)
    dossiers: list[Dossier] = field(default_factory=list)
    ean_ids: list[str] = field(default_factory=list, metadata=config(exclude=Exclude.ALWAYS))
    dossiers_ids: list[str] = field(default_factory=list, metadata=config(exclude=Exclude.ALWAYS))
    commune_id: Optional[str] = None

    def __post_init__(self) -> None:
        for ean in self.ean or []:
            self.ean_ids.append(ean.ean)
            if ean.energy == "01":
                ean.energy = "elec"
            elif ean.energy == "02":
                ean.energy = "gaz"
        for dossier in self.dossiers or []:
            self.dossiers_ids.append(dossier.id)
        if self.commune:
            self.commune_id = self.commune.id

    @classmethod
    def from_event(cls, event, allow_ghost=True, only_ghost=False, require_permission=None) -> Self:
        user = getUserData(
            event,
            allow_ghost=allow_ghost,
            only_ghost=only_ghost,
            require_permission=require_permission,
        )
        user_json = json.dumps(user, cls=DecimalEncoder)
        return cls.from_json(user_json)
