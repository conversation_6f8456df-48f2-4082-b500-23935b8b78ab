import os
import unittest

from utils.models.connection_model import Connection, Address, Meter, Company


class TestConnectionModel(unittest.TestCase):
    def setUp(self):
        self.connection = Connection(
            energy_type="energy_type",
            work_type="work_type",
            applicant_type="applicant_type",
            act_as="act_as",
            name="name",
            firstname="firstname",
            email="email",
            phone="phone",
            address=Address(street="Street", number="1", postcode="12345", city="City", country="FR"),
            meters=[Meter(ean="ean", number="number")],
        )

    def test_sect_activite_returns_01_with_energytype_ELEC(self):
        self.connection.energy_type = "ELEC"
        self.assertEqual(self.connection.sect_activite, "01")

    def test_sect_activite_returns_02_with_energytype_GAZ(self):
        self.connection.energy_type = "GAZ"
        self.assertEqual(self.connection.sect_activite, "02")

    def test_sect_activite_returns_none_with_other_energytype(self):
        self.connection.energy_type = "OTHER"
        self.assertIsNone(self.connection.sect_activite)

    def test_to_sap_format_ws35(self):
        os.environ["LANG"] = "FR"
        # Test the to_sap_format_ws35 method
        result = self.connection.to_sap_format_ws35()
        self.assertEqual(result["Partenaire"][0]["Nom"], self.connection.name)
        self.assertEqual(result["Partenaire"][0]["Prenom"], self.connection.firstname)
        self.assertEqual(result["Partenaire"][0]["Langue"], os.environ["LANG"])
        self.assertEqual(result["Partenaire"][0]["Gsm"], self.connection.phone)
        self.assertEqual(result["Partenaire"][0]["Email"], self.connection.email)
        self.assertEqual(result["Partenaire"][0]["TypeURD"], self.connection.applicant_type)
        self.assertEqual(result["Partenaire"][0]["NumTVA"], "")
        self.assertEqual(result["Partenaire"][0]["AssujetiTVA"], "N")
        self.assertEqual(result["Partenaire"][0]["NomEntreprise"], "")
        self.assertEqual(result["Partenaire"][0]["FormeJuridique"], "")
        self.assertEqual(result["Partenaire"][0]["Adresse"]["Rue"], self.connection.address.street)
        self.assertEqual(result["Partenaire"][0]["Adresse"]["NumRue"], self.connection.address.number)
        self.assertEqual(result["Partenaire"][0]["Adresse"]["NumCmpt"], self.connection.address.box)
        self.assertEqual(result["Partenaire"][0]["Adresse"]["CdPostal"], self.connection.address.postcode)
        self.assertEqual(result["Partenaire"][0]["Adresse"]["Localite"], self.connection.address.city)
        self.assertEqual(result["Partenaire"][0]["Adresse"]["Pays"], self.connection.address.country)
        self.assertEqual(result["Adresse"]["Rue"], self.connection.address.street)
        self.assertEqual(result["Adresse"]["NumRue"], self.connection.address.number)
        self.assertEqual(result["Adresse"]["NumCmpt"], self.connection.address.box)
        self.assertEqual(result["Adresse"]["CdPostal"], self.connection.address.postcode)
        self.assertEqual(result["Adresse"]["Localite"], self.connection.address.city)
        self.assertEqual(result["Demande"][0]["SectActivite"], self.connection.sect_activite)
        self.assertEqual(result["Demande"][0]["Ean"], self.connection.meters[0].ean)
        self.assertEqual(result["Demande"][0]["Details"][0]["Valeur"], self.connection.work_type)
        self.assertEqual(result["Demande"][0]["Compteur"][0]["DetailsCompteurs"][0]["ValeurDetailCpt"], self.connection.meters[0].number)

        # Check if optional fields are properly handled
        self.connection.company = Company(name="Corp", legal_status="SA", tva="12345")
        self.connection.contact = Address(street="Street", number="1", box="A", postcode="12345", city="City", country="FR")

        result = self.connection.to_sap_format_ws35()
        self.assertEqual(result["Partenaire"][0]["NumTVA"], self.connection.company.tva)
        self.assertEqual(result["Partenaire"][0]["AssujetiTVA"], "Y")
        self.assertEqual(result["Partenaire"][0]["NomEntreprise"], self.connection.company.name)
        self.assertEqual(result["Partenaire"][0]["FormeJuridique"], self.connection.company.legal_status)

        self.assertEqual(result["Partenaire"][0]["Adresse"]["Rue"], self.connection.contact.street)
        self.assertEqual(result["Partenaire"][0]["Adresse"]["NumRue"], self.connection.contact.number)
        self.assertEqual(result["Partenaire"][0]["Adresse"]["NumCmpt"], self.connection.contact.box)
        self.assertEqual(result["Partenaire"][0]["Adresse"]["CdPostal"], self.connection.contact.postcode)
        self.assertEqual(result["Partenaire"][0]["Adresse"]["Localite"], self.connection.contact.city)
        self.assertEqual(result["Partenaire"][0]["Adresse"]["Pays"], self.connection.contact.country)


if __name__ == "__main__":
    unittest.main()
