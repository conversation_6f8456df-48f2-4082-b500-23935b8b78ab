import os
import uuid
from datetime import datetime
from decimal import Decimal

from utils.aws_utils import get_dynamodb_table


def create_notification(user_data, type_, priority, message, link=None):
    table = get_dynamodb_table(os.environ["DYNAMODB"])
    notifications = list(user_data["notifications"])

    notifItem = {
        "uid": str(uuid.uuid4()),
        "message": message,
        "type": type_,
        "priority": priority,
        "link": link,
        "read_timestamp": None,
        "timestamp": Decimal(datetime.now().timestamp()),
    }
    notifications.append(notifItem)

    response = table.update_item(
        Key={"uid": user_data["uid"]},
        UpdateExpression="SET #notifications = :notificationsValue",
        ExpressionAttributeNames={"#notifications": "notifications"},
        ExpressionAttributeValues={":notificationsValue": notifications},
    )
    return notifItem
