import json
import os
import traceback

"""
#To handle errors in handler : 
try:
    ...
except HttpError as e:
        return e.response()
except Exception as e:
    log_err(e)
    return HttpError().response()
"""


def format_validation_error(data):
    def _flatten_dict_errors(data, parent_key="", sep="."):
        items = []
        for key, value in data.items():
            new_key = f"{parent_key}{sep}{key}" if parent_key else key
            if isinstance(value, dict):
                items.extend(_flatten_dict_errors(value, new_key, sep=sep).items())
            elif isinstance(value, list):
                errors = " | ".join(value)
                items.append((new_key, errors))
        return dict(items)

    flattened_errors = _flatten_dict_errors(data)
    formatted_text = "\n".join([f"{key} : {value}" for key, value in flattened_errors.items()])
    return formatted_text


class HttpError(Exception):
    status = None
    message = None
    headers = None

    def __init__(self, status=500, message="Internal server error", headers=None, error_code=None, extra=None):
        if headers is None:
            headers = {}
        self.status = status
        self.message = message
        self.headers = headers
        self.error_code = error_code
        self.extra = extra

    def response(self):
        print(f"HTTPError {self.status} : {self.message}")

        body = {"Error": self.status, "Message": str(self.message)}

        if os.environ["IS_PROD"] != "true":
            body["StackTrace"] = traceback.format_exc()
        if self.error_code:
            body["ErrorCode"] = self.error_code
        if self.extra:
            body["Extra"] = self.extra

        return {
            "isBase64Encoded": False,
            "statusCode": self.status,
            "headers": {
                "Content-Type": "application/json",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Credentials": True,
                **self.headers,
            },
            "body": body,
        }

    def __str__(self):
        string = f"Error {self.status} : {self.message}"

        if self.error_code:
            string += f"\n  ErrorCode : {self.error_code}"
        if self.extra:
            string += f"\n  Extra : {json.dumps(self.extra, indent=2)}"
        return string


class BadRequestError(HttpError):
    def __init__(self, message="Bad Request", headers=None, error_code=None, extra=None):
        super().__init__(400, message, headers, error_code, extra)


class UnauthorizedError(HttpError):
    def __init__(self, message="Unauthorized", headers=None, error_code=None, extra=None):
        super().__init__(401, message, headers, error_code, extra)


class ForbiddenError(HttpError):
    def __init__(self, message="Forbidden", headers=None, error_code=None, extra=None):
        super().__init__(403, message, headers, error_code, extra)


class NotFoundError(HttpError):
    def __init__(self, message="Not Found", headers=None, error_code=None, extra=None):
        super().__init__(404, message, headers, error_code, extra)


class NotAcceptableError(HttpError):
    def __init__(self, message="Not Acceptable", headers=None, error_code=None, extra=None):
        super().__init__(406, message, headers, error_code, extra)


class ConflictError(HttpError):
    def __init__(self, message="Conflict", headers=None, error_code=None, extra=None):
        super().__init__(409, message, headers, error_code, extra)


class InternalServerError(HttpError):
    def __init__(self, message="Internal Server Error", headers=None, error_code=None, extra=None):
        super().__init__(500, message, headers, error_code, extra)


class NotImplementedError(HttpError):
    def __init__(self, message="Not Implemented", headers=None, error_code=None):
        super().__init__(501, message, headers, error_code)


class BadGatewayError(HttpError):
    def __init__(self, message="Bad Gateway", headers=None, error_code=None, extra=None):
        super().__init__(502, message, headers, error_code, extra)


# endregion base HTTP errors


class EmailAlreadyUsedError(ConflictError):
    def __init__(
        self,
        message="Cet adresse e-mail a déjà été utilisée pour la création d'un compte.",
        headers=None,
    ):
        super().__init__(message, headers)


INVALID_USER_RIGHTS = ForbiddenError(
    "This user isn't allowed to call this endpoint.",
    error_code="INVALID_USER_RIGHTS",
)
INVALID_EAN_FOR_USER = ForbiddenError(
    "This EAN doesn't belong to the given user.",
    error_code="INVALID_EAN_FOR_USER",
)
