import traceback

from utils.log_utils import log_err


def format_store_schedule(schedule_input):
    try:
        days_in_schedule = schedule_input.split("@")
        days_in_schedule.update((k, v.replace()) for k, v in days_in_schedule.items())
    except Exception:
        errmsg = "ERROR 500: erreur dans le encodage d une donnee de type jour de la semaine dans horaire de point de recharge"
        log_err(traceback.format_exc())
        raise ValueError(errmsg)

    try:
        days_in_schedule = days_in_schedule.map(lambda s: s.replace)
        schedule_output = days_in_schedule
    except Exception:
        errmsg = "ERROR 500: erreur dans le encodage d une donnee de type heure du jour dans horaire de point de recharge"
        log_err(traceback.format_exc())
        raise ValueError(errmsg)

    return schedule_output
