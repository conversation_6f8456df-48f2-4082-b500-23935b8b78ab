import contextlib
import json
from os import environ
from time import sleep

from utils.api import api_caller, basic_auth_headers
from utils.aws_utils import invoke_lambda
from utils.DecimalEncoder import DecimalEncoder
from utils.dict_utils import first, get
from utils.errors import HttpError
from utils.log_utils import log_err, log_info
from utils.userdata import Preferences, UserData

SAP_EDIT_USER_MAX_RETRY = 5


def get_user_prefs_json(user: UserData):
    user_prefs = Preferences(user)
    user_prefs_dict = user_prefs.get()

    return json.dumps(
        {
            "Langue": get(user_prefs_dict or {}, "Langue", "FR"),
            "Bp": int(get(user, "bp", 0)),
            "BpNom": get(user, "lastname"),
            "BpPrenom": get(user, "firstname"),
            "BpCodePays": get(get(user, "adresse", {}), "CodePays", "BE"),
            "BpRue": get(get(user, "adresse", {}), "Rue", ""),
            "BpNumrue": get(get(user, "adresse", {}), "NumRue", "0"),
            "BpCdpostal": get(get(user, "adresse", {}), "Cdpostal", "0000"),
            "BpLocalite": get(get(user, "adresse", {}), "Localite", ""),
            "BpEmail": get(user, "email"),
            "BpEmailCom": get(user, "contact_email"),
            "BpTelFixe": get(user, "phone_fixe"),
            "BpTelPortable": get(user, "phone"),
            "BpPreferences": user_prefs.get_bp_format(),
            "NumCmpt": None,
            "IdRadRue": None,
            "IdRadLocalite": None,
            "IdAdresse": None,
            "Ean": [str(ean["ean"]) for ean in (user.get("ean") or [])],
        },
        cls=DecimalEncoder,
    )


def error_handling_edit_user(e, args_slicing, user, retry):
    if (
        e.args
        and isinstance(e.args, tuple)
        and (
            "Erreur pendant la mise \u00e0 jour de l'adresse" in e.args[args_slicing]
            or f"Le partenaire {user.get('bp')} est actuellement bloqu\u00e9 par vous" in e.args[args_slicing]
        )
        and retry < SAP_EDIT_USER_MAX_RETRY
    ):
        log_info(f"Update error, retry after 2sec. Nbr retry : {retry}")
        sleep(2 * retry + 1)
        return sap_edit_user(user, retry + 1)
    else:
        log_err(e)
        raise HttpError(
            500,
            "Une erreur technique empêche momentanément la modification et l’enregistrement de vos données. Veuillez réessayer ultérieurement.",
        )


def sap_edit_user(user, retry=0):
    if not get(user, "bp"):
        return
    try:
        lambda_result = invoke_lambda(
            FunctionName=environ["PASSTHROUGH_LAMBDA_ARN"],
            InvocationType="RequestResponse",
            Payload=json.dumps(
                {
                    "resource": "/sap/utilisateur/modification",
                    "path": f"/{environ['API_VERSION']}/sap/utilisateur/modification",
                    "httpMethod": "POST",
                    "headers": basic_auth_headers(),
                    "queryStringParameters": None,
                    "multiValueQueryStringParameters": None,
                    "pathParameters": None,
                    "stageVariables": {"version": environ["API_VERSION"]},
                    "requestContext": {
                        "resourcePath": "/sap/utilisateur/modification",
                        "httpMethod": "POST",
                        "path": f"/{environ['API_VERSION']}/sap/utilisateur/modification",
                        "stage": environ["API_VERSION"].replace(".", "-"),
                    },
                    "isBase64Encoded": False,
                    "body": get_user_prefs_json(user),
                },
            ).encode("utf8"),
        )
        payload = json.loads(lambda_result["Payload"].read())
        status_code = payload.get("statusCode")
        result = payload.get("body")
        print(result)
        with contextlib.suppress(Exception):
            result = json.loads(result)
        if not 200 <= status_code < 300:
            raise HttpError(status_code, result)

    except HttpError as e:
        error_handling_edit_user(e, 1, user, retry)
    except Exception as e:
        error_handling_edit_user(e, 0, user, retry)


def generate_or_find_bp(user):
    bp = find_bp(user)
    if not bp:
        bp = generate_bp(user)
    else:
        user["bp"] = int(bp)
        user["preferences"]["BP_CONNECTE"] = True
        sap_edit_user(user)
    return int(bp)


def find_bp(user):
    adresse = get(user, "adresse", {})
    resp = api_caller(
        "get",
        "/sap/utilisateur/recherche",
        {
            "BpNom": get(user, "lastname"),
            "BpPrenom": get(user, "firstname"),
            "BpEmail": get(user, "email"),
            "BpTelPortable": get(user, "phone") if get(user, "valid_phone") else None,
            "BpTelFixe": get(user, "phone_fixe"),
            "BpRue": get(adresse, "Rue"),
            "BpNumrue": get(adresse, "NumRue"),
            "BpCdpostal": get(adresse, "Cdpostal"),
            "BpLocalite": get(adresse, "Localite"),
        },
        None,
        basic_auth_headers(),
    )["Data"]
    if len(resp) > 1:
        # TODO: handle multiple bp found
        log_err(f"Multiple bp found for user {user.get('email')} : " + str([r["Bp"] for r in resp]))
    resp = first(resp)
    if not resp:
        return None
    bp = resp["Bp"]
    return bp


def generate_bp(user):
    try:
        user["preferences"] = {
            **(user or {}).get("preferences", {}),
            "BP_ORIGINE": "MyRESA",
        }
        lambda_result = invoke_lambda(
            FunctionName=environ["PASSTHROUGH_LAMBDA_ARN"],
            InvocationType="RequestResponse",
            Payload=json.dumps(
                {
                    "resource": "/sap/utilisateur/creation",
                    "path": f"/{environ['API_VERSION']}/sap/utilisateur/creation",
                    "httpMethod": "POST",
                    "headers": basic_auth_headers(),
                    "queryStringParameters": None,
                    "multiValueQueryStringParameters": None,
                    "pathParameters": None,
                    "stageVariables": {"version": environ["API_VERSION"]},
                    "requestContext": {
                        "resourcePath": "/sap/utilisateur/creation",
                        "httpMethod": "POST",
                        "path": f"/{environ['API_VERSION']}/sap/utilisateur/creation",
                        "stage": environ["API_VERSION"].replace(".", "-"),
                    },
                    "isBase64Encoded": False,
                    "body": get_user_prefs_json(user),
                },
            ).encode("utf8"),
        )
        payload = json.loads(lambda_result["Payload"].read())
        status_code = payload.get("statusCode")
        result = payload.get("body")
        print(result)
        try:
            result = json.loads(result)
        except:
            pass
        if not 200 <= status_code < 300:
            raise HttpError(status_code, result)
        bp = result["Bp"]
        return bp
    except Exception as e:
        log_err(e)
        raise HttpError(500, "WS73 failed")


def sap_delete_user(bp):
    try:
        lambda_result = invoke_lambda(
            FunctionName=environ["PASSTHROUGH_LAMBDA_ARN"],
            InvocationType="RequestResponse",
            Payload=json.dumps(
                {
                    "resource": "/sap/utilisateur/suppression",
                    "path": f"/{environ['API_VERSION']}/sap/utilisateur/suppression",
                    "httpMethod": "DELETE",
                    "headers": basic_auth_headers(),
                    "queryStringParameters": None,
                    "multiValueQueryStringParameters": None,
                    "pathParameters": None,
                    "stageVariables": {"version": environ["API_VERSION"]},
                    "requestContext": {
                        "resourcePath": "/sap/utilisateur/suppression",
                        "httpMethod": "POST",
                        "path": f"/{environ['API_VERSION']}/sap/utilisateur/suppression",
                        "stage": environ["API_VERSION"].replace(".", "-"),
                    },
                    "isBase64Encoded": False,
                    "body": json.dumps({"Bp": bp}),
                },
            ).encode("utf8"),
        )
        payload = json.loads(lambda_result["Payload"].read())
        status_code = payload.get("statusCode")
        result = payload.get("body")
        print(result)
        with contextlib.suppress(Exception):
            result = json.loads(result)
        if not 200 <= status_code < 300:
            raise HttpError(status_code, result)
        return result
    except Exception as e:
        log_err(e)
        raise HttpError(500, "WS75 failed")
