import base64
import datetime as dt
import json
import xml.etree.ElementTree as ET
from datetime import datetime
from os import environ
from random import randint
from time import sleep
from urllib.parse import quote_plus

import boto3
import requests
import xmltodict
from boto3.dynamodb.conditions import Key
from requests import ConnectTimeout, ReadTimeout

from utils.aws_utils import invoke_lambda
from utils.dict_utils import first, get
from utils.errors import NotFoundError
from utils.log_utils import log_err, log_info, log_info_json
from utils.sentry.sentry_utils import (
    capture_exception_in_sentry,
    capture_message_in_sentry,
)
from utils.type_utils import to_list

SHAREPOINT_SECRET = environ.get("SHAREPOINT_SECRET")
IS_PROD = environ.get("IS_PROD") == "true"


class SharepointHandler:
    _secret: dict = None

    @classmethod
    def secret(cls) -> dict:
        if not cls._secret:
            cls._secret = cls.get_secret()
        return cls._secret

    @classmethod
    def check_if_sharepoint_valise_exist(cls, valise_id, max_retry: int = 5, timeout: tuple = (8, 20)):
        valise = cls.get_valise(valise_id)
        url = cls.get_url_from_secret("SearchValise/SearchValise.SearchValise.svc")

        payload = f"""
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
   <soapenv:Header/>
   <soapenv:Body>
      <tem:Search>
         <tem:VALISE_UID>{valise}</tem:VALISE_UID>
      </tem:Search>
   </soapenv:Body>
</soapenv:Envelope>
        """.strip().encode("utf-8")

        headers = {
            "Content-Type": "text/xml; charset=UTF-8",
            "Connection": "keep-alive",
            "SOAPAction": "http://tempuri.org/ISearchValise/Search",
        }

        log_info_json({"execute": IS_PROD, "payload": payload, "headers": headers})

        retry = max_retry
        response = requests.request("POST", url, headers=headers, data=payload, timeout=timeout)
        while (not (200 <= response.status_code < 300) or "Error" in response.text) and retry > 0:
            sleep(randint(2, 10))
            response = requests.request("POST", url, headers=headers, data=payload, timeout=timeout)
            retry -= 1
        if not (200 <= response.status_code < 300) or "Error" in response.text:
            log_err(response.text)
        response_text = response.text

        return f"<a:Comment>Error : impossible de trouver la valise : {valise}</a:Comment>" not in response_text

    @classmethod
    def create_sharepoint_valise_if_missing(cls, valise_id, max_retry: int = 5, timeout: tuple = (10, 20)):
        if cls.check_if_sharepoint_valise_exist(valise_id):
            return

        valise = cls.get_valise(valise_id)
        url = cls.get_url_from_secret("CreateValise/CreateValise.CreateValise.svc")
        valise_type, valise_id, valise_env, valise_env_version = valise.split(" ")[1].split("-")

        payload = f"""
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/" xmlns:cre="http://schemas.datacontract.org/2004/07/CreateValise">
   <soapenv:Body>
      <tem:CreateValises>
         <tem:VALISE_UID>{valise}</tem:VALISE_UID>
         <tem:THESYSTEM>{valise_env}</tem:THESYSTEM>
         <tem:MANDT>{valise_env_version}</tem:MANDT>
         <tem:OBJ_TYPE>Inconnu</tem:OBJ_TYPE>
         <tem:ID>{valise_id}</tem:ID>
         <tem:THEMETADATA>
            <cre:TheMetadataClass>
               <cre:THEID>VALISE_TYPE</cre:THEID>
               <cre:THEVALUE>Valise_{valise_type}</cre:THEVALUE>
            </cre:TheMetadataClass>
            <cre:TheMetadataClass>
               <cre:THEID>Valise_Date</cre:THEID>
               <cre:THEVALUE>{datetime.now().strftime("%Y%m%d")}</cre:THEVALUE>
            </cre:TheMetadataClass>
         </tem:THEMETADATA>
      </tem:CreateValises>
   </soapenv:Body>
</soapenv:Envelope>
        """.strip().encode("utf-8")

        headers = {
            "Content-Type": "text/xml; charset=UTF-8",
            "Connection": "keep-alive",
            "SOAPAction": "http://tempuri.org/ICreateValise/CreateValises",
        }

        log_info_json({"execute": IS_PROD, "payload": payload, "headers": headers})
        if IS_PROD:
            retry = max_retry
            response = requests.request("POST", url, headers=headers, data=payload, timeout=timeout)
            while (not (200 <= response.status_code < 300) or "Error" in response.text) and retry > 0:
                sleep(randint(2, 10))
                response = requests.request("POST", url, headers=headers, data=payload, timeout=timeout)
                retry -= 1
            if not (200 <= response.status_code < 300) or "Error" in response.text:
                log_err(response.text)
        else:
            log_info("Not creating valise in Sharepoint")

    @classmethod
    def send_valise_file_to_sharepoint(
        cls,
        file_name,
        extension,
        file_data,
        valise_id,
        prepend_date=True,
        metadata: dict = None,
        max_retry: int = 5,
        timeout: tuple = (10, 20),
    ):
        if metadata is None:
            metadata = {}
        value = cls.get_value(file_name, extension)
        valise = cls.get_valise(valise_id)
        url = cls.get_url_from_secret("ManageDocument/ManageDocument.ManageDocument.svc")
        if prepend_date:
            value = cls.get_document(value)

        if "Title" not in metadata:
            metadata["Title"] = value

        payload = f"""
<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
    <s:Body>
        <PostDocument xmlns="http://tempuri.org/">
            <data xmlns:a="http://schemas.datacontract.org/2004/07/ManageDocument" xmlns:i="http://www.w3.org/2001/XMLSchema-instance">
                <a:Document>{value}</a:Document>
                <a:Extension>{extension}</a:Extension>
                <a:FileData>{file_data}</a:FileData>
                <a:MetaDataList>
                    {"".join([f"<a:PostDocumentData.MetaDatas><a:TheId>{id}</a:TheId><a:TheValue>{val}</a:TheValue></a:PostDocumentData.MetaDatas>" for id, val in metadata.items()])}
                </a:MetaDataList>
                <a:Valise>{valise}</a:Valise>
            </data>
        </PostDocument>
    </s:Body>
</s:Envelope>
        """.strip().encode("utf-8")

        headers = {
            "Content-Type": "text/xml; charset=UTF-8",
            "Connection": "keep-alive",
            "SOAPAction": "http://tempuri.org/IManageDocument/PostDocument",
        }

        log_info_json({"execute": IS_PROD, "payload": payload, "headers": headers})
        if IS_PROD:
            try:
                retry = max_retry
                response = requests.request("POST", url, headers=headers, data=payload, timeout=timeout)
                while (not (200 <= response.status_code < 300) or "Error" in response.text) and retry > 0:
                    sleep(randint(2, 10))
                    response = requests.request("POST", url, headers=headers, data=payload, timeout=timeout)
                    retry -= 1
                if not (200 <= response.status_code < 300) or "Error" in response.text:
                    log_err("Unable to send file to SharePoint")
                    log_err(response.text)
                    capture_message_in_sentry(
                        f"send_valise_file_to_sharepoint : [{response.status_code}] {response.text}",
                        level="error",
                    )
                response_text = response.text
            except (ConnectTimeout, ReadTimeout) as e:
                log_err(e)
                response_text = None
                capture_exception_in_sentry(e)
        else:
            log_info("Not sending to Sharepoint")
            response_text = None

        return response_text

    @classmethod
    def download_valise_file_from_sharepoint(cls, document_url, timeout: tuple = (10, 20)):
        url = cls.get_url_from_secret("FindDocument/FindDocument.FindDocument.svc")

        payload = f"""
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
   <soapenv:Header/>
   <soapenv:Body>
      <tem:FindDocuments>
         <tem:URL>{document_url}</tem:URL>
         <tem:FichierInclus>true</tem:FichierInclus>
      </tem:FindDocuments>
   </soapenv:Body>
</soapenv:Envelope>
        """.strip().encode("utf-8")

        headers = {
            "Content-Type": "text/xml; charset=UTF-8",
            "Connection": "keep-alive",
            "SOAPAction": "http://tempuri.org/IFindDocument/FindDocuments",
        }

        log_info_json({"execute": IS_PROD, "payload": payload, "headers": headers})

        data = None
        try:
            response = requests.request("POST", url, headers=headers, data=payload, timeout=timeout)
            xml_root = ET.fromstring(response.text)

            try:
                data = base64.b64decode(xml_root[0][0][0][1].text.encode("utf8"))
            except:
                log_err("Unable to get sharepoint file data")
                log_err(response.text)
        except (ConnectTimeout, ReadTimeout) as e:
            log_err(e)
            capture_exception_in_sentry(e)

        return data

    @classmethod
    def list_valise_file_from_sharepoint(cls, valise_id, type_filter=None, timeout: tuple = (10, 20)):
        valise = cls.get_valise(valise_id)
        url = cls.get_url_from_secret("ListDocuments/ListDocuments.svc")

        payload = f"""
        <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/" xmlns:lis="http://schemas.datacontract.org/2004/07/ListDocuments">
            <soapenv:Header/>
            <soapenv:Body>
              <tem:GetListDocuments>
                 <tem:data>
                    <lis:Valise>{valise}</lis:Valise>
                 </tem:data>
              </tem:GetListDocuments>
            </soapenv:Body>
        </soapenv:Envelope>
        """.strip().encode("utf-8")

        headers = {
            "Content-Type": "text/xml; charset=UTF-8",
            "Connection": "keep-alive",
            "SOAPAction": "http://tempuri.org/IListDocuments/GetListDocuments",
        }

        log_info_json({"payload": payload, "headers": headers})

        data = {}
        try:
            response = requests.request("POST", url, headers=headers, data=payload, timeout=timeout)
            if not 200 <= response.status_code < 300:
                log_err(f"{url} respond with status code {response.status_code!s}")
                log_err(response.text)
                raise NotFoundError(f"Cannot find documents in valise {valise}")
            print(response)
            print(response.text)

            try:
                data = xmltodict.parse(response.text)
            except:
                log_err("Unable to get sharepoint file data")
                log_info(response.text)
        except (ConnectTimeout, ReadTimeout) as e:
            log_err(e)
            capture_exception_in_sentry(e)

        files_data = data.get("s:Envelope", {}).get("s:Body", {}).get("GetListDocumentsResponse", {}).get("GetListDocumentsResult", {}).get("a:ListDocumentsData", [])
        files_data = to_list(files_data)

        files = []
        for file in files_data:
            if type_filter:
                for meta in file.get("a:MetaDataList", {}).get("a:ListDocumentsData.MetaDatas"):
                    if meta.get("a:TheId", "") == "Type_Correspondance_Out" and (meta.get("a:TheValue") or "").upper() == type_filter.upper():
                        files.append(file.get("a:Document", ""))
            else:
                files.append(file.get("a:Document", ""))

        return files

    @classmethod
    def get_value(cls, full_document, file_extension):
        """
        Get the name of the document, without the extension
        """
        if file_extension in full_document:
            return ".".join(full_document.split(".")[:-1])
        else:
            return full_document

    @classmethod
    def get_document(cls, document):
        return f"{dt.date.today().strftime('%Y%m%d')}_{document}"

    @classmethod
    def get_valise(cls, valise: str) -> str:
        if not valise.endswith(f"-{cls.secret()['env']}"):
            valise = f"{valise}-{cls.secret()['env']}"
        return valise

    @classmethod
    def get_url_from_secret(cls, path):
        return f"http://{cls.secret()['host']}:{cls.secret()['port']}/{path}"

    @classmethod
    def get_secret(cls):
        session = boto3.session.Session()
        client = session.client(service_name="secretsmanager", region_name="eu-west-1")

        try:
            get_secret_value_response = client.get_secret_value(SecretId=SHAREPOINT_SECRET)
            return json.loads(get_secret_value_response["SecretString"])
        except Exception as e:
            raise e


def submit_document_to_sharepoint(document_id):
    MD_TABLE = environ["MD_TABLE"]
    BUCKET = environ["BUCKET_NAME_UPLOADS"]
    dynamodb = boto3.resource("dynamodb")
    table = dynamodb.Table(MD_TABLE)
    document = first(
        get(
            table.query(
                KeyConditionExpression=Key("document_id").eq(document_id),
                FilterExpression=Key("submitted").eq(False),
            ),
            "Items",
            [],
        ),
    )
    if not document:
        raise Exception(f"document {document_id} not found or already submitted")
    table.update_item(
        Key={"document_id": document_id},
        UpdateExpression="SET submitted=:s",
        ExpressionAttributeValues={":s": True},
    )
    invoke_lambda(
        FunctionName=environ["SharepointUpload_LAMBDA_ARN"],
        InvocationType="Event",
        Payload=json.dumps(
            {
                "Records": [
                    {
                        "s3": {
                            "bucket": {"name": environ["BUCKET_NAME_UPLOADS"]},
                            "object": {"key": quote_plus(document["resource"])},
                        },
                    },
                ],
            },
        ).encode("utf8"),
    )
