import os
import unittest

from utils import validation


class TestValidEmail(unittest.TestCase):
    def test_is_valid_email_with_valid_email(self):
        email = "<EMAIL>"
        result = validation.is_valid_email(email)
        self.assertTrue(result)

    def test_is_valid_email_with_invalid_email(self):
        email = "aaa@invalid"
        result = validation.is_valid_email(email)
        self.assertFalse(result)

    def test_is_valid_email_with_non_string_input(self):
        email = 123
        result = validation.is_valid_email(email)
        self.assertFalse(result)

    def test_is_valid_email_with_check_user(self):
        email = "<EMAIL>"
        result = validation.is_valid_email(email, check_user=True)
        self.assertTrue(result)  # This might change based on the actual SMTP server

    def test_is_valid_email_with_invalid_user_check(self):
        email = "<EMAIL>"
        stage = os.environ.get("STAGE", "dev")
        os.environ["STAGE"] = "production"  # set production to enable address checking
        result = validation.is_valid_email(email, check_user=True)
        os.environ["STAGE"] = stage  # restore the existing stage

        if result:
            self.skipTest("the test timed out")
        self.assertFalse(result)  # This might change based on the actual SMTP server


if __name__ == "__main__":
    unittest.main()
