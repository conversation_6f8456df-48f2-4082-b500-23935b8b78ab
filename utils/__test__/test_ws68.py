import unittest

from utils.api import api_caller
from utils.mocking import mock_api


@mock_api
class TestWS68(unittest.TestCase):
    def test_single_mail(self):
        response = api_caller(
            "POST",
            "/envMessage",
            body={
                "Langue": "FR",
                "Header": {
                    "TEMPLATE_ID": "MY_TEST_TEMPLATE",
                    "EMAIL": "<EMAIL>",
                    "CCI": "<EMAIL>",
                    "CC": "<EMAIL>",
                },
            },
            raw=True,
        )
        self.assertEqual(response.status_code, 404)

    def test_single_mail_no_user_check(self):
        response = api_caller(
            "POST",
            "/envMessage",
            body={
                "Langue": "FR",
                "Header": {
                    "TEMPLATE_ID": "MY_TEST_TEMPLATE",
                    "EMAIL": "<EMAIL>",
                    "CCI": "<EMAIL>",
                    "CC": "<EMAIL>",
                    "NO_USER_CHECK": "Y",
                },
            },
            raw=True,
        )
        self.assertEqual(response.status_code, 200)

    def test_single_mail_but_list(self):
        response = api_caller(
            "POST",
            "/envMessage",
            body={
                "Langue": "FR",
                "Header": {
                    "TEMPLATE_ID": "MY_TEST_TEMPLATE",
                    "EMAIL": ["<EMAIL>"],
                    "CCI": ["<EMAIL>"],
                    "CC": ["<EMAIL>"],
                    "NO_USER_CHECK": "Y",
                },
            },
            raw=True,
        )
        self.assertEqual(response.status_code, 200)

    def test_multi_mail(self):
        response = api_caller(
            "POST",
            "/envMessage",
            body={
                "Langue": "FR",
                "Header": {
                    "TEMPLATE_ID": "MY_TEST_TEMPLATE",
                    "EMAIL": ["<EMAIL>", "<EMAIL>"],
                    "CCI": ["<EMAIL>", "<EMAIL>"],
                    "CC": ["<EMAIL>", "<EMAIL>"],
                    "NO_USER_CHECK": "Y",
                },
            },
            raw=True,
        )
        self.assertEqual(response.status_code, 200)

    def test_no_multi_mail_without_no_user_check(self):
        response = api_caller(
            "POST",
            "/envMessage",
            body={
                "Langue": "FR",
                "Header": {
                    "TEMPLATE_ID": "MY_TEST_TEMPLATE",
                    "EMAIL": ["<EMAIL>", "<EMAIL>"],
                    "CCI": ["<EMAIL>", "<EMAIL>"],
                    "CC": ["<EMAIL>", "<EMAIL>"],
                },
            },
            raw=True,
        )
        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.json()["ErrorCode"], "MULTIPLE_EMAIL_USER_CHECK")


if __name__ == "__main__":
    unittest.main()
