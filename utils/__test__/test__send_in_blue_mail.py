import os
import unittest

from utils.log_utils import LogTime
from utils.message_utils import _send_in_blue_mail


class TestSendInBlueMail(unittest.TestCase):
    base_data = {
        "template_id": 319,
        "template_data": {
            "Langue": "FR",
            "Header": {
                "TEMPLATE_ID": "MY_TEST_TEMPLATE",
                "EAN": "541460900000575415",
                "EMAIL": "<EMAIL>",
                "MOBILE_PHONE": "+32412345678",
                "NO_USER_CHECK": "Y",
            },
            "Content": {
                "SomeTemplateVar": "Could be anything",
                "SomeTemplateDict": {"SomeTemplateVar": "Could be anything"},
                "SomeTemplateList": [
                    "Could be anything, event another dict or list",
                    "Could be anything, event another dict or list",
                ],
            },
            "year": "2024",
            "env": "qta",
            "Unsubscribe": "",
        },
        "email": "<EMAIL>",
        "reply_mail": None,
        "name": "",
        "sandbox": True,
        "attachments": [],
        "bcc": None,
    }

    def test_success(self):
        mails = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
        ]

        for mail in mails:
            with LogTime(f"check mail {mail!r}"):
                self.base_data["email"] = mail
                result = _send_in_blue_mail(**self.base_data)
                self.assertEqual(result["EmailStatusCode"], 200)

    def test_success_with_alias(self):
        self.base_data["email"] = "<EMAIL>"
        result = _send_in_blue_mail(**self.base_data)
        self.assertEqual(result["EmailStatusCode"], 200)

    def test_invalid_email_bad_user(self):
        self.base_data["email"] = "<EMAIL>"
        stage = os.environ.get("STAGE", "dev")
        os.environ["STAGE"] = "production"  # set production to enable address checking
        result = _send_in_blue_mail(**self.base_data)
        os.environ["STAGE"] = stage  # restore the existing stage

        if result["EmailStatusCode"] == 200:
            self.skipTest("the test timed out")
        self.assertEqual(result["EmailStatusCode"], 400)

    def test_invalid_email_bad_domain(self):
        self.base_data["email"] = "<EMAIL>"
        result = _send_in_blue_mail(**self.base_data)
        self.assertEqual(result["EmailStatusCode"], 400)


if __name__ == "__main__":
    unittest.main()
