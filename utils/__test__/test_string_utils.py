import unittest

from utils.string_utils import format_number


class TestFormatNumber(unittest.TestCase):
    def test_format_number_default(self):
        self.assertEqual(format_number("123", "456", 3, 2), "123.45")
        self.assertEqual(format_number("123", "456", "03", "02"), "123.45")

    def test_format_number_decimal_with_dot(self):
        self.assertEqual(format_number("123", "0.456", 3, 2), "123.45")
        self.assertEqual(format_number("123", "0.456", "03", "02"), "123.45")

    def test_format_number_specific_seperator(self):
        self.assertEqual(format_number("123", "456", 3, 2, ","), "123,45")
        self.assertEqual(format_number("123", "456", "03", "02", ","), "123,45")

    def test_format_number_fill_integer(self):
        self.assertEqual(format_number("23", "456", 3, 2), "023.45")
        self.assertEqual(format_number("23", "456", "03", "02"), "023.45")

    def test_format_number_fill_decimal(self):
        self.assertEqual(format_number("123", "45", 3, 3), "123.450")
        self.assertEqual(format_number("123", "45", "03", "03"), "123.450")

    def test_format_number_fill_both(self):
        self.assertEqual(format_number("123", "45", 5, 3), "00123.450")
        self.assertEqual(format_number("123", "45", "05", "03"), "00123.450")

    def test_format_number_trim_integer(self):
        self.assertEqual(format_number("0000123", "45", 3, 2), "123.45")
        self.assertEqual(format_number("0000123", "45", "03", "02"), "123.45")

    def test_format_number_trim_decimal(self):
        self.assertEqual(format_number("123", "4567", 3, 2), "123.45")
        self.assertEqual(format_number("123", "4567", "03", "02"), "123.45")

    def test_format_number_trim_both(self):
        self.assertEqual(format_number("0000123", "4567", 3, 2), "123.45")
        self.assertEqual(format_number("0000123", "4567", "03", "02"), "123.45")

    def test_format_number_zero_integer(self):
        self.assertEqual(format_number("0000123", "4567", 0, 2), "0.45")
        self.assertEqual(format_number("0000123", "4567", "00", "02"), "0.45")

    def test_format_number_zero_decimal(self):
        self.assertEqual(format_number("0000123", "4567", 3, 0), "123")
        self.assertEqual(format_number("0000123", "4567", "03", "00"), "123")

    def test_format_number_zero_both(self):
        self.assertEqual(format_number("0000123", "4567", 0, 0), "0")
        self.assertEqual(format_number("0000123", "4567", "00", "00"), "0")
