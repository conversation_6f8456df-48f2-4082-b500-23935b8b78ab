import unittest

from utils.errors import BadRequestError
from utils.type_utils import float_format


class TestFloatFormat(unittest.TestCase):
    def test_float_format_with_none_value(self):
        self.assertIsNone(float_format(None))

    def test_float_format_without_optional_args(self):
        self.assertEqual(float_format(123.456), 123.456)

    def test_float_format_with_valid_string(self):
        self.assertEqual(float_format("123.456"), 123.456)

    def test_float_format_with_str_spaces(self):
        result = float_format("   10. 5   ")
        self.assertEqual(result, 10.5)

    def test_float_format_with_str_commas(self):
        result = float_format("10,5")
        self.assertEqual(result, 10.5)

    def test_float_format_with_invalid_string(self):
        with self.assertRaises(BadRequestError):
            float_format("invalid")

    def test_float_format_with_custom_exception(self):
        with self.assertRaises(ValueError) as e:
            float_format("invalid", err=ValueError("Invalid value"))
        self.assertEqual(str(e.exception), "Invalid value")

    def test_float_format_with_field_name(self):
        with self.assertRaises(BadRequestError) as e:
            float_format("invalid", field_name="test_field")
        self.assertEqual(str(e.exception.message), "The field 'test_field' should be a float, or a string float representation")
        self.assertEqual(e.exception.error_code, "INVALID_FLOAT_FORMAT")


if __name__ == "__main__":
    unittest.main()
