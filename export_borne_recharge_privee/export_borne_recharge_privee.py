import json
from datetime import datetime
from decimal import Decimal

import openpyxl
from openpyxl.styles import PatternFill, Alignment, Font

from utils.aws_utils import get_dynamodb_table
from utils.dict_utils import flatten_dict


def convert_to_serializable(o):
    if isinstance(o, datetime):
        return o.isoformat()
    elif isinstance(o, Decimal):
        return str(o)  # Convertir Decimal en str pour la sérialisation
    return o


def apply_style_to_headers(sorted_keys, ws):
    header_fill = PatternFill(start_color="FF8517", end_color="FF8517", fill_type="solid")
    header_font = Font(bold=True, color="000000")  # Texte en gras et noir
    alignment = Alignment(horizontal="center")
    ws.append(sorted_keys)
    for col in range(1, len(sorted_keys) + 1):
        cell = ws.cell(row=1, column=col)
        cell.fill = header_fill
        cell.font = header_font
        cell.alignment = alignment
        ws.column_dimensions[cell.column_letter].width = 40


def main():
    table = get_dynamodb_table("BornesRechargeTable_prd")

    response = table.scan()

    items = response["Items"]

    # Continuer à récupérer les données si la réponse est paginée
    while "LastEvaluatedKey" in response:
        response = table.scan(ExclusiveStartKey=response["LastEvaluatedKey"])
        items.extend(response["Items"])

    for item in items:
        item["DateCreation"] = datetime.strptime(item["DateCreation"], "%Y-%m-%dT%H:%M:%S.%f")
        item["Borne"]["Date"] = datetime.strptime(item["Borne"]["Date"], "%Y-%m-%d")

    # Filtrer les bornes dont la 'Borne.Date' est dans le futur
    items_present = [item for item in items if item["Borne"]["Date"] <= datetime.now()]

    # Tri des éléments par Borne.Ean, Borne.Serial, et DateCreation de manière décroissante
    items_sorted = sorted(
        items_present,
        key=lambda x: (x["Borne"]["Ean"], x["Borne"]["Serial"], x["DateCreation"]),
        reverse=True,
    )

    # Utiliser un dictionnaire pour conserver uniquement la dernière déclaration de chaque borne
    bornes_dernieres = {}
    for item in items_sorted:
        cle = (item["Borne"]["Ean"], item["Borne"]["Serial"])
        if cle not in bornes_dernieres:
            bornes_dernieres[cle] = flatten_dict(item)

    create_file(bornes_dernieres)


def create_file(bornes_dernieres):
    asked_headers = [
        "Borne/Adresse/CodePostal",
        "Borne/Adresse/Commune",
        "TypeDemande",
        "Borne/Date",
        "Borne/Utilisation",
        "Borne/Marque",
        "Borne/Modele",
        "Borne/Puissance",
        "Borne/Bidirectionnelle",
    ]
    wb = openpyxl.Workbook()
    ws = wb.active

    # Appliquer le style aux en-têtes
    apply_style_to_headers(asked_headers, ws)

    # Liste pour les données JSON
    liste_pour_json = []

    # Itération sur les données pour le traitement et l'ajout dans Excel et la préparation pour JSON
    for d in bornes_dernieres.values():
        row = []
        borne_json = {}
        for header in asked_headers:
            value = d.get(header, "")

            # Traitement spécial pour 'TypeDemande'
            if header == "TypeDemande":
                if value in [
                    "La mise en service d'une borne de recharge",
                    "Die Inbetriebnahme einer Ladestation",
                ]:
                    value = "En service"
                elif value in [
                    "La mise hors service d'une borne de recharge",
                    "Die Außerbetriebnahme einer Ladestation",
                ]:
                    value = "hors service"

            row.append(value)
            borne_json[header.replace("/", "_")] = value

        # Ajouter la ligne dans Excel
        ws.append(row)
        # Ajouter le dictionnaire dans la liste pour JSON
        liste_pour_json.append(borne_json)

    # Sauvegarde du fichier Excel
    wb.save("bornes_declaree.xlsx")

    # Conversion de la liste pour JSON et sauvegarde
    with open("bornes_declaree.json", "w") as fichier_json:
        json.dump(liste_pour_json, fichier_json, default=convert_to_serializable)


if __name__ == "__main__":
    main()
