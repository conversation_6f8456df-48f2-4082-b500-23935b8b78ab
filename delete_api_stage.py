import boto3

from utils.aws_utils import default_lambda_config


class AWSCleaner:
    def __init__(self, api_id, env, domain_name, dry_run=True):
        self.api_id = api_id
        self.env = env
        self.domain_name = domain_name
        self.client_api = boto3.client("apigateway")
        self.client_lambda = boto3.client("lambda", config=default_lambda_config)
        self.dry_run = dry_run
        self.lambda_functions = self._get_lambda_functions()

    def _get_lambda_functions(self):
        lambda_functions = []
        with open("../lambdas", "r") as lambdas:
            for _lambda in lambdas:
                lambda_functions.append(_lambda.strip())
        return lambda_functions

    def delete_lambda_aliases(self, list_stages):
        for stage_name in list_stages:
            for function_name in self.lambda_functions:
                full_function_name = f"{function_name}-{self.env}"
                try:
                    self.client_lambda.get_function(FunctionName=full_function_name, Qualifier=stage_name)
                except Exception as e:
                    print(f"{full_function_name}:{stage_name} error => {e}")
                else:
                    if not self.dry_run:
                        try:
                            self.client_lambda.delete_alias(FunctionName=full_function_name, Name=stage_name)
                            print(f"Deleted Lambda alias for {full_function_name}:{stage_name}")
                        except Exception as e:
                            print(f"Error deleting Lambda alias for {full_function_name}:{stage_name}: {e}")
                    else:
                        print(f"Test Dry Deleted Lambda alias for {full_function_name}:{stage_name}")

    def delete_unused_lambda_versions(self):
        for function_name in self.lambda_functions:
            full_function_name = f"{function_name}-{self.env}"
            try:
                self._delete_function_versions_without_alias(full_function_name)
            except Exception as e:
                print(f"Error checking/deleting unused versions for {full_function_name}: {e}")

    def _delete_function_versions_without_alias(self, function_name):
        aliases = self.client_lambda.list_aliases(FunctionName=function_name)["Aliases"]
        versions_with_alias = {alias["FunctionVersion"] for alias in aliases}
        versions = self.client_lambda.list_versions_by_function(FunctionName=function_name)["Versions"]

        for version in versions:
            if version["Version"] not in versions_with_alias and version["Version"] != "$LATEST":
                version_number = version["Version"]
                if not self.dry_run:
                    self.client_lambda.delete_function(FunctionName=function_name, Qualifier=version_number)
                    print(f"Deleted unused Lambda version {version_number} for {function_name}")
                else:
                    print(f"Test Dry Deleted unused Lambda version {version_number} for {function_name}")

    def delete_api_stages(self, list_stages):
        if not list_stages or not self.api_id:
            print("No stages or API ID provided for deletion.")
            return

        # Supprimer les aliases de chaque fonction Lambda pour tous les stages
        self.delete_lambda_aliases(list_stages)

        # Supprimer les versions de Lambda sans alias après avoir traité tous les stages
        self.delete_unused_lambda_versions()

        # Supprimer les stages de l'API Gateway
        for stage_name in list_stages:
            if not self.dry_run:
                try:
                    self.client_api.delete_base_path_mapping(
                        domainName=self.domain_name,
                        basePath=stage_name.replace("-", ".").replace(".RC", "-RC"),
                    )
                    print(f"Deleted API base path: {stage_name}")
                except Exception as e:
                    print(f"Error with API base path: {stage_name}: {e}")
                try:
                    self.client_api.delete_stage(restApiId=self.api_id, stageName=stage_name)
                    print(f"Deleted API Gateway stage: {stage_name}")
                except Exception as e:
                    print(f"Error with API Gateway stage: {stage_name}: {e}")
            else:
                print(f"Test Dry Deleted API Gateway stage: {stage_name}")


if __name__ == "__main__":
    # Configuration
    list_stages = ["5-1-0-RC-1"]

    # Production
    # cleaner = AWSCleaner("581l4val82", "production", "api.resa.be", dry_run=False)

    # QLA
    # cleaner = AWSCleaner("47qzmkq04f", "qla", "api-qla.resa.be", dry_run=False)

    # QTA
    cleaner = AWSCleaner("kppa652ewa", "qta", "api-acceptance.resa.be", dry_run=False)

    # Instantiate and use the cleaner
    cleaner.delete_api_stages(list_stages)
