from boto3.dynamodb.conditions import Key

from utils.aws_utils import get_dynamodb_table

table = get_dynamodb_table("SmartConsoAlerts_PRD")
table_user = get_dynamodb_table("MyResaUser_prd")


def get_all_uids():
    key_name = "Uid"
    uids = []
    response = table.scan(ProjectionExpression=key_name)
    uids.extend(item[key_name] for item in response["Items"])

    while "LastEvaluatedKey" in response:
        response = table.scan(
            ProjectionExpression=key_name,
            ExclusiveStartKey=response["LastEvaluatedKey"],
        )
        uids.extend(item[key_name] for item in response["Items"])

    return uids


def get_user_by_uid(uid):
    response = table_user.query(
        KeyConditionExpression=Key("uid").eq(uid),
    )
    return response["Items"][0]


def extract_pref_comalert_sms(data: dict) -> bool:
    return data.get("preferences", {}).get("com_smartportal_alert_sms", False)


def change_alert_sms(pref, state=False):
    pref["com_smartportal_alert_sms"] = state
    return pref


mail_list = []
modif_list = []
all_uids = get_all_uids()
for uid in all_uids:
    user_data = get_user_by_uid(uid)
    if extract_pref_comalert_sms(user_data):
        mail_list.append(user_data["email"])
        modif_list.append({"uid": user_data["uid"], "preferences": change_alert_sms(user_data["preferences"])})
print(modif_list)
