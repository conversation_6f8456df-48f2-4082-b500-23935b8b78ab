import json
import os
from multiprocessing import Process
from os import environ
from time import sleep

import boto3
from boto3.dynamodb.conditions import Attr, Key
from hdbcli import dbapi

from tools.local_flask.gateway import start_local_flask
from utils.api import api_caller
from utils.aws_utils import get_secret, load_s3_file, scanAll

dynamodb = boto3.resource("dynamodb")
table = dynamodb.Table(os.environ["DYNAMODB"])


def check_smart_with_db(smart_ean_cpt: dict):
    env = json.loads(load_s3_file(environ["BUCKET"], environ["ENV_FILE"]))
    db_credentials = get_secret(env["HANA_SECRET"])
    hana_schema = db_credentials["DATABASE"]
    conn = dbapi.connect(
        address=db_credentials["ENDPOINT"],
        port=db_credentials["PORT"],
        user=db_credentials["USER"],
        password=db_credentials["PASSWORD"],
    )
    cursor = conn.cursor()
    cursor.execute(
        f"""
WITH counters AS (
    SELECT ANLAGE, EXT_UI AS "Ean"
    FROM {hana_schema}.EUITRANS
    join {hana_schema}.EUIINSTLN
        on EUIINSTLN.INT_UI = EUITRANS.INT_UI
    WHERE
        EXT_UI IN ('{"','".join(smart_ean_cpt)}')
), get_counter_info AS (
    SELECT ANLAGE, SERNR AS "NumCpt"
     FROM
        (SELECT ANLAGE , LOGIKNR
        FROM {hana_schema}.EASTL
        WHERE AB <= TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD')
          AND TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD') <= BIS
        ) AS EASTL
        INNER JOIN
        (SELECT
            EQUNR AS EGERH_EQUNR,
            ZWGRUPPE AS EGERH_ZWGRUPPE,
            LOGIKNR AS EGERH_LOGIKNR,
            DEVLOC AS DEVLOC,
            CAP_ACT_GRP
        FROM {hana_schema}.EGERH
        WHERE AB <= TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD') AND TO_VARCHAR(CURRENT_DATE, 'YYYYMMDD') <= BIS
        ) AS EGERH
        ON EASTL.LOGIKNR = EGERH.EGERH_LOGIKNR
        JOIN {hana_schema}.EQUI
            ON EQUI.EQUNR = EGERH_EQUNR
    WHERE CAP_ACT_GRP IN('9000','9001', '9002')
)
SELECT "Ean", "NumCpt"
FROM counters
JOIN get_counter_info ON get_counter_info.ANLAGE = counters.ANLAGE
"""
    )
    results = cursor.fetchall()

    for result in results:
        smart_ean_cpt[str(result[0])] = result[1]


def check_smart_with_ws(smart_ean_cpt: dict):
    # Start local API with flask to receive request
    flask = Process(target=start_local_flask, args=(8012,))
    flask.start()
    sleep(3)

    # Fetch data
    for ean in list(smart_ean_cpt):
        if smart_ean_cpt[ean] is None:
            query = api_caller(method="get", path="/ean", params={"Ean": ean}, throw=False)
            if query:
                for ean_info in query.get("ListeEan", []):
                    smart_ean_cpt[str(ean_info["Ean"])] = ean_info["NumCpt"] if ean_info["CptSmart"] == "X" else False

    # Stop flask
    sleep(5)
    flask.terminate()


def export():
    users = scanAll(
        os.environ["DYNAMODB"],
        {
            "ProjectionExpression": "bp, email, firstname, lastname, ean",
            "FilterExpression": ~(Key("uid").begins_with("ghost_") | Key("uid").begins_with("Tes.User.") | Key("uid").begins_with("Tes.Test."))
            & Attr("valide").eq(True)
            & Attr("bp").exists()
            & Attr("ean").ne(None),
        },
    )

    with open("export_user_with_smart_counter_output/export.csv", "w") as output:
        output.write("BP;Mail;Prenom;Nom;Ean;Compteur\n")

        smart_ean_cpt = {str(i["ean"]): None for user in users for i in user["ean"]}
        print(f"ean : {list(smart_ean_cpt)}")

        # check_smart_with_ws(smart_ean_cpt)
        check_smart_with_db(smart_ean_cpt)

        for user in users:
            for ean in list(set([str(i["ean"]) for i in user["ean"]])):
                if smart_ean_cpt[ean]:
                    output.write(f"{user['bp']};{user['email']};{user['firstname']};{user['lastname']};{ean};{smart_ean_cpt[ean]}\n")


if __name__ == "__main__":
    # Create output dir
    if not os.path.exists("export_user_with_smart_counter_output"):
        os.makedirs("export_user_with_smart_counter_output")

    # Run data export
    export()
