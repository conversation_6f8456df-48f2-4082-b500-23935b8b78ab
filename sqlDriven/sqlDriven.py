#!/usr/bin/env python3
import contextlib
import json
import os

import utils.resa_api_constants as constants
from utils.aws_handler_decorator import aws_lambda_handler
from utils.aws_utils import load_s3_file
from utils.DecimalEncoder import DecimalEncoder
from utils.dict_utils import get
from utils.errors import BadRequestError, HttpError
from utils.log_utils import LogTime


def import_controller(elt):
    mod = __import__("controllers." + elt)
    mod = getattr(mod, elt)
    return getattr(mod, elt)


@aws_lambda_handler
def handler(event, context):
    cursor = None
    is_api_call = "httpMethod" in event and "methodArn" not in event
    try:
        with contextlib.suppress(ValueError, TypeError):
            event["body"] = json.loads(event.get("body", "{}") or "{}")
        if is_api_call:
            path = event["resource"]
            method = event["httpMethod"].upper()
        else:
            path = "tasks"
            method = event.get("task")

        mapping_s3 = load_s3_file(os.environ["MAPPING_BUCKET_NAME"], os.environ["MAPPING_BUCKET_FILE"])
        mapping = json.loads(mapping_s3)
        path_data = mapping[path][method]

        controller_name = path_data.get("controller", "Controller")
        controller = import_controller(controller_name)
        instance = controller(event, path_data)
        try:
            request_params = instance.validate_request_params()
            request_params = {
                "Langue": get(os.environ, "LANG", "FR", default_on_empty=True),
                **request_params,
            }
        except ValueError as e:
            raise BadRequestError(str(e), headers=instance.headers()) from e
        sql_statement = instance.load_sql_file(instance.sql_file())
        sql_statement = instance.apply_hana_env(sql_statement)

        try:
            with LogTime("Open connection"):
                cursor = instance.db_connection()
        except Exception as e:
            raise HttpError(500, constants.errmsg_dict["connection"], instance.headers()) from e
        with LogTime("Execute query"):
            cursor = instance.execute_query(sql_statement, request_params)
        data = instance.to_response(cursor, request_params)
        response = {
            "isBase64Encoded": instance.base64,
            "statusCode": 200,
            "headers": instance.headers(),
            "body": data if instance.base64 or instance.raw_response else json.dumps(data, cls=DecimalEncoder),
        }
    finally:
        with contextlib.suppress(Exception):
            cursor.close()

    return response


if __name__ == "__main__":
    print(
        json.dumps(
            handler(
                {
                    "headers": {},
                    "body": {},
                    "resource": "/gazMeter/{sn}/pin",
                    "method": "get",
                    "queryStringParameters": {"phone": "+32123456789"},
                    "pathParameters": {"sn": "7FLO2120246368"},
                    "httpMethod": "GET",
                },
                None,
            ),
        ),
    )
    """
    print(json.dumps(handler(
        {'resource': '/ep',
         'method': 'get',
         'queryStringParameters': {'Lat0': 50.0, 'Long0': 5.665, 'Lat1': 55.0, 'Long1': 6.0, 'PageSize': 100},
         'httpMethod': 'GET'}, None)))
     """
    """
     print(json.dumps(handler(
        {'resource': '/pannes/{Id}',
         'method': 'get',
         'pathParameters': {'id' : '000411080303'},
         'httpMethod': 'GET'}, None)))
    
    """
"""
    541456700003233803
    541456700000048189
    541460900002617502 -> Gaz
print(
        json.dumps(
            handler(
                {   'headers' : {},
                    'body': {},
                    'resource': '/ean/{Ean}',
                    'method': 'get',
                    'queryStringParameters': {},
                    'pathParameters': {
                        'Ean': 541460900002617502
                    },
                    'httpMethod': 'GET'
                }, None)))
"""
