import os
import pickle
from typing import Any

from controllers.NoPagination import NoPagination
from dateutil.relativedelta import relativedelta

from utils.aws_utils import upload_s3_file
from utils.log_utils import LogTime
from utils.smart_conso_utils import DICT_TYPE_TO_TABLE, get_valid_date_ranges_from_params


class ExportEanData(NoPagination):
    """
    Extend the functionality of ws205 to export the data to a CSV file and store it in S3.

    This class processes EAN data in chunks to avoid memory issues and uses
    tempfile.mkstemp for safe temporary file handling during data processing.

    Attributes
    ----------
    BUCKET : str
        S3 bucket name for temporary storage.
    BUCKET_DIR : str
        Directory path within the bucket for exports.
    export_id : str
        Unique identifier for the export operation.
    eans : list
        List of EAN data to process.
    temp_files : dict
        Dictionary tracking temporary files for cleanup.

    Notes
    -----
    This class automatically handles cleanup of temporary files through
    the __del__ method and explicit cleanup_temp_files() method.

    """

    BUCKET = f"{os.environ['BUCKET']}-temp-storage"
    BUCKET_DIR = "exports_ean_index"

    def __init__(self, event: dict, linking: dict) -> None:
        super().__init__(event, linking)

        self.export_id = event["export_id"]
        self.eans = event["eans"]
        self.start_date = event["start_date"]
        self.end_date = event["end_date"]
        self.temp_files: dict[str, str] = {}  # Track temporary files for cleanup

        self.type_params = "HOURLY"
        self.energy = "elec"
        self.event.setdefault("headers", {})["Accept"] = "text/csv"

    def validate_request_params(self) -> dict:
        """
        Fill in request parameters to do a year of hourly data.

        Returns
        -------
        dict
            Dictionary containing validated request parameters including
            Type, Ean, MeterId, StartDate, EndDate, Energy, and ContractDate.

        Notes
        -----
        Sets up parameters for hourly data export covering
        the last 366 days from the current date.

        """
        self.params = {
            "Type": "HOURLY",
            "Ean": "",
            "MeterId": "",
            "StartDate": self.start_date,
            "EndDate": self.end_date,
            "Energy": self.energy,
            "ContractDate": [],
        }
        return self.params

    def execute_query(self, sql_statement, request_params):
        return super().execute_query(self.build_query(sql_statement), request_params)

    def apply_hana_env(self, sql_statement):
        red_table = DICT_TYPE_TO_TABLE[self.energy][self.type_params]

        # Set the table, but keep the dynamic_clause
        return sql_statement.format(red_table=red_table, dynamic_clause="{dynamic_clause}")

    def build_query(self, sql_statement: str):
        """
        Build the dynamic WHERE clause for the query.

        Parameters
        ----------
        sql_statement : str
            The base SQL statement to be modified.

        Returns
        -------
        str
            The modified SQL statement with the dynamic WHERE clause.

        """
        dynamic_where_clause = ""

        for ean_details in self.eans:
            ean = ean_details["ean"]
            contract_date = ean_details["contract_date"]
            valid_ranges = get_valid_date_ranges_from_params(
                "elec",
                self.start_date,
                self.end_date,
                contract_date,
            )

            if not valid_ranges:
                continue

            date_conditions = [
                f"(measureDateTime BETWEEN '{start.strftime('%Y-%m-%d %H:%M:%S %z')}' AND '{(end + relativedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S %z')}')"
                for start, end in valid_ranges
            ]

            if dynamic_where_clause:
                dynamic_where_clause += f"\n  OR ( ean = '{ean}' AND ({' OR '.join(date_conditions)}))"
            else:
                dynamic_where_clause = f"(ean = '{ean}' AND ({' OR '.join(date_conditions)}))"

        return sql_statement.format(dynamic_clause=dynamic_where_clause)

    def to_response(self, cursor: Any, params: dict | None = None) -> None:
        """
        Get the data page by page and store it raw in S3 each time to not use too much memory.

        Parameters
        ----------
        cursor : Any
            Database cursor for fetching data.
        params : dict, optional
            Additional parameters for data processing, by default None.

        Notes
        -----
        This method processes data in chunks to avoid memory issues when dealing
        with large datasets. Each chunk is written to a temporary file using
        tempfile.mkstemp for safe temporary file handling.

        """
        page = 1
        page_size = 100000
        ean_cpt_list = []
        current_ean_cpt = None
        current_data = None

        result = cursor.fetchmany(page_size)
        while result:
            with LogTime(f"Data processing page {page}"):
                data = [self.row_processor(cursor, row) for row in result]

                # Group data by EAN and meter
                data_by_ean_cpt = {}
                for row in data:
                    ean_cpt = f"{row['ean']}_{row['meterid']}"
                    if ean_cpt not in data_by_ean_cpt:
                        data_by_ean_cpt[ean_cpt] = []
                    data_by_ean_cpt[ean_cpt].append(row)

                # Store data for each EAN/meter combination
                for ean_cpt, data in data_by_ean_cpt.items():
                    if ean_cpt != current_ean_cpt:
                        # New EAN/meter, upload current data (if not first)
                        if current_ean_cpt is not None:
                            # Upload the data to S3
                            upload_s3_file(
                                bucket_name=self.BUCKET,
                                object_name=f"{self.BUCKET_DIR}/{self.export_id}/raw/{current_ean_cpt}.raw",
                                data=pickle.dumps(data, protocol=pickle.HIGHEST_PROTOCOL),
                            )
                            ean_cpt_list.append(current_ean_cpt)

                        # Reset current EAN/meter
                        current_ean_cpt = ean_cpt
                        current_data = data
                    else:
                        # Same EAN/meter, append data to current data
                        current_data += data

                page += 1
                result = cursor.fetchmany(page_size)

        # Upload the last data
        if current_ean_cpt is not None:
            upload_s3_file(
                bucket_name=self.BUCKET,
                object_name=f"{self.BUCKET_DIR}/{self.export_id}/raw/{current_ean_cpt}.raw",
                data=pickle.dumps(current_data, protocol=pickle.HIGHEST_PROTOCOL),
            )
            ean_cpt_list.append(current_ean_cpt)

        return ean_cpt_list
