from datetime import datetime

from controllers.NoPagination import NoPagination
from dateutil.relativedelta import relativedelta

from utils.aws_utils import get_resource_key
from utils.dict_utils import get
from utils.errors import INVALID_EAN_FOR_USER, BadRequestError, ForbiddenError
from utils.log_utils import LogTime
from utils.models.user import User
from utils.smart_conso_utils import belgium_tz, data_to_csv, get_valid_date_ranges_from_params, process_data_response, DICT_TYPE_TO_TABLE


class ws205(NoPagination):
    def __init__(self, event, linking):
        super().__init__(event, linking)
        self.params = super().validate_request_params()
        self.type_params = get(self.params, "Type", "MONTHLY")

    def headers(self):
        header = super().headers()
        accept = get(self.event, "headers", {}).get("Accept")
        if accept == "text/csv":
            header["Content-Type"] = accept
            header["Content-Disposition"] = "attachment; filename=data.csv"
        return header

    def data_to_response(self, processed_data: list[dict]) -> list[dict]:
        """
        Convert a list of processed data dictionaries into a list of PascalCase formatted response.

        Returns
        -------
        list[dict]
            A list of dictionaries where the keys are in PascalCase format, containing processed data.

        """
        return [
            {
                "MeterId": data.get("meterid"),
                "MeasureDateTime": data.get("measuredatetime"),
                "ConsumptionStartFrame": data.get("measuredatetime_from"),
                "MeasureValue": data.get("measurevalue"),
                "ReadingTypeId": data.get("readingtypeid"),
                "MeasureState": data.get("measurestate"),
                "Ean": data.get("ean"),
                "ReadingFrequency": data.get("readingfrequency"),
                "StandardizationTimestamp": data.get("standardizationtimestamp"),
                "MeasureUnit": data.get("measureunit"),
                "Consumption": data.get("consumption"),
            }
            for data in processed_data
        ]

    def to_response(self, cursor, params=None):
        data: list = super().to_response(cursor, params)
        with LogTime("Process data"):
            processed_data = process_data_response(
                data,
                self.params.get("Energy"),
                self.params["StartDate"],
                self.params["EndDate"],
                self.type_params,
            )
        header = self.headers()
        if header["Content-Type"] == "text/csv":
            self.raw_response = True
            return data_to_csv(processed_data)
        else:
            return self.data_to_response(processed_data)

    def validate_request_params(self):
        accepted_type_list = ["MONTHLY", "DAILY", "HOURLY"]
        start_date = self.params["StartDate"]
        end_date = self.params["EndDate"]

        if self.type_params not in accepted_type_list:
            raise BadRequestError(message=f"Type params should be in {accepted_type_list}", error_code="TYPE_NOT_RECOGNIZED")

        try:
            start_date = datetime.fromisoformat(start_date)
            end_date = datetime.fromisoformat(end_date)
        except ValueError as e:
            raise BadRequestError(message="Date must be in ISO 8601 format", error_code="DATE_NOT_ISO8601") from e

        if start_date > end_date:
            raise BadRequestError(message="StartDate must be earlier than EndDate", error_code="INVALID_DATE_RANGE")

        if self.type_params == "HOURLY" and start_date.date() != end_date.date():
            raise BadRequestError(message="For HOURLY type, StartDate and EndDate must be the same day", error_code="HOURLY_INVALID_DATE_RANGE")

        self.validate_user_ean()

        return self.params

    def validate_user_ean(self) -> None:
        """
        Validate if the given EAN belongs to a user and update relevant parameters.

        Raises
        ------
        ForbiddenError
            If the provided EAN does not belong to the user.
        BadRequestError
            If the given EAN is not linked to an energy type.

        """
        user = User.from_event(self.event, allow_ghost=False)
        if str(self.params.get("Ean")) not in user.ean_ids:
            raise INVALID_EAN_FOR_USER
        for ean_obj in user.ean:
            if ean_obj.ean == self.params["Ean"]:
                self.params["Energy"] = ean_obj.energy
                self.params["ContractDate"] = []
                for contract in ean_obj.contract:
                    self.params["ContractDate"].append((contract.ctr_from, contract.ctr_to))
                break
        else:
            raise BadRequestError(message="This EAN has no Energy type linked to it", error_code="ENERGY TYPE NOT FOUND")

    def sql_file(self):
        """Return the sql filename to load"""
        return {k: get_resource_key(v) for k, v in self.linking["sql_template"].items()}

    def load_sql_file(self, filename):
        if self.type_params == "MONTHLY":
            return super().load_sql_file(filename["monthly"])
        else:
            return super().load_sql_file(filename["daily"])

    def build_query(self, sql_statement):
        valid_ranges = get_valid_date_ranges_from_params(self.params.get("Energy"), self.params["StartDate"], self.params["EndDate"], self.params["ContractDate"])
        if not valid_ranges:
            raise ForbiddenError("The user's contract has no access to the asked date", error_code="CONTRACT_HAS_NO_ACCESS")

        if self.type_params == "MONTHLY":
            monthly_dates = set()
            for start, end in valid_ranges:
                current = start
                i = 1
                while current <= end:
                    monthly_dates.add(current)
                    current = start + relativedelta(months=i)
                    # The first start date is fixed by the valid_range (contracts) but the next one should be reset to the first date of the month
                    if current.day != 1:
                        current = current.replace(day=1)
                    # Preserve time zone when shifting date
                    current = belgium_tz.localize(current.replace(tzinfo=None))
                    i += 1
                    if current > end:
                        current = end + relativedelta(days=1)
                        monthly_dates.add(current)
                        break

            date_conditions = []
            for idx, date in enumerate(sorted(monthly_dates)):
                if idx + 1 < len(monthly_dates):
                    next_date = sorted(monthly_dates)[idx + 1]
                    date_conditions.append(
                        f"(SELECT min(measuredatetime) AS measure_date FROM ean_data WHERE measuredatetime BETWEEN '{date.strftime('%Y-%m-%d %H:%M:%S %z')}' "
                        f"AND '{next_date.strftime('%Y-%m-%d %H:%M:%S %z')}')",
                    )
                if idx == len(monthly_dates) - 1:
                    previous_date = sorted(monthly_dates)[idx - 1] if idx > 0 else sorted(monthly_dates)[0]
                    date_conditions.append(
                        f"(SELECT max(measuredatetime) AS measure_date FROM ean_data WHERE measuredatetime BETWEEN '{previous_date.strftime('%Y-%m-%d %H:%M:%S %z')}' "
                        f"AND '{date.strftime('%Y-%m-%d %H:%M:%S %z')}')",
                    )
            dynamic_where_clause = "\n  union".join(date_conditions)
        else:
            date_conditions = [
                (f"(measureDateTime BETWEEN '{start.strftime('%Y-%m-%d %H:%M:%S %z')}' AND '{(end + relativedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S %z')}')")
                for start, end in valid_ranges
            ]
            dynamic_where_clause = "\n  AND (" + " OR ".join(date_conditions) + ")"
        return sql_statement.format(dynamic_clause=dynamic_where_clause)

    def execute_query(self, sql_statement, request_params):
        return super().execute_query(self.build_query(sql_statement), request_params)

    def apply_hana_env(self, sql_statement):
        energy_params = get(self.params, "Energy", "elec")
        red_table = DICT_TYPE_TO_TABLE[energy_params][self.type_params]

        # Set the table, but keep the dynamic_clause
        return sql_statement.format(red_table=red_table, dynamic_clause="{dynamic_clause}")
