import re
from os import environ

import boto3

if __name__ == "__main__":
    environ.setdefault("AWS_PROFILE", "resa")
    environ.setdefault("AWS_DEFAULT_REGION", "eu-west-1")
    queue = "https://sqs.eu-west-1.amazonaws.com/204480941676/userActivation-production_queue"
    sqs = boto3.client("sqs")

    with open("export.txt", "r") as messages:
        for message in messages:
            # replace API version to latest
            message = re.sub('"(https://api.*\.resa\.be/)[^"]+"', r'"\1latest"', message)
            sqs.send_message(QueueUrl=queue, MessageBody=message)
