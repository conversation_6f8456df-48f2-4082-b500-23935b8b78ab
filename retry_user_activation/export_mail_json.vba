Const MACRO_NAME = "Export Outlook Folders to File"
 
Sub ExportMain()
ExportToFile "C:\Users\<USER>\Desktop\export_mail\export.txt", "<EMAIL>\User activation error"
MsgBox "Process complete.", vbInformation + vbOKOnly, MACRO_NAME
End Sub
Sub ExportToFile(strFilename As String, strFolderPath As String)
Dim olkMsg As Object
Dim olkFld As Object
Dim fso As Object
Dim oFile As Object
 
If strFilename <> "" Then
If strFolderPath <> "" Then
Set olkFld = OpenOutlookFolder(strFolderPath)
If TypeName(olkFld) <> "Nothing" Then

Set fso = CreateObject("Scripting.FileSystemObject")
Set oFile = fso.CreateTextFile(strFilename)

For Each olkMsg In olkFld.Items
'Only export messages, not receipts or appointment requests, etc.
If olkMsg.Class = olMail Then
'Add a line for each json in body
oFile.WriteLine Mid(olkMsg.Body, InStr(1, olkMsg.Body, "{"), InStr(1, olkMsg.Body, "}") - InStr(1, olkMsg.Body, "{") + 1)
End If

Next
Set olkMsg = Nothing
oFile.Close
Else
MsgBox "The folder '" & strFolderPath & "' does not exist in Outlook.", vbCritical + vbOKOnly, MACRO_NAME
End If
Else
MsgBox "The folder path was empty.", vbCritical + vbOKOnly, MACRO_NAME
End If
Else
MsgBox "The filename was empty.", vbCritical + vbOKOnly, MACRO_NAME
End If
 
Set olkMsg = Nothing
Set olkFld = Nothing
Set fso = Nothing
Set oFile = Nothing
End Sub
 
Public Function OpenOutlookFolder(strFolderPath As String) As Outlook.MAPIFolder
Dim arrFolders As Variant
Dim varFolder As Variant
Dim bolBeyondRoot As Boolean
 
On Error Resume Next
If strFolderPath = "" Then
Set OpenOutlookFolder = Nothing
Else
Do While Left(strFolderPath, 1) = "\"
strFolderPath = Right(strFolderPath, Len(strFolderPath) - 1)
Loop
arrFolders = Split(strFolderPath, "\")
For Each varFolder In arrFolders
Select Case bolBeyondRoot
Case False
Set OpenOutlookFolder = Outlook.Session.Folders(varFolder)
bolBeyondRoot = True
Case True
Set OpenOutlookFolder = OpenOutlookFolder.Folders(varFolder)
End Select
If Err.Number <> 0 Then
Set OpenOutlookFolder = Nothing
Exit For
End If
Next
End If
On Error GoTo 0
End Function