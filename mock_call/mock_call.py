import json
import os

from utils.aws_handler_decorator import aws_lambda_handler
from utils.aws_utils import load_s3_file, get_resource_key


@aws_lambda_handler
def handler(event, context):
    method = event["httpMethod"]
    path = event["resource"]

    linking_file = json.loads(load_s3_file(os.environ["BUCKET"], get_resource_key("linking.json")))  # pylint: disable=no-member
    linking = linking_file[path][method.upper()]

    mock_return = linking["return"]
    return {
        "isBase64Encoded": False,
        "statusCode": mock_return["code"],
        "headers": {},
        "body": json.dumps(mock_return["body"]),
    }
