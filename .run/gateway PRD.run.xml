<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="gateway PRD" type="PythonConfigurationType" factoryName="Python" folderName="Gateway">
    <module name="AWS_API Web" />
    <option name="ENV_FILES" value="" />
    <option name="INTERPRETER_OPTIONS" value="" />
    <option name="PARENT_ENVS" value="true" />
    <envs>
      <env name="PYTHONUNBUFFERED" value="1" />
      <env name="AD_SECRET" value="MyResaAPI/ActiveDirectory/prd" />
      <env name="ADFS_SECRET" value="MyResaAPI/ADFS/prd" />
      <env name="ADFS_WEB" value="MyResaWeb/ADFS/PRD" />
      <env name="API_REGION" value="eu-west-1" />
      <env name="API_STAGE" value="3.11.0" />
      <env name="API_URL" value="https://127.0.0.1:8011" />
      <env name="API_VERSION" value="3.11.0" />
      <env name="AsynchroneProcessesTable" value="AsynchroneProcesses_prd" />
      <env name="AsyncProcessLambda" value="arn:aws:lambda:eu-west-1:204480941676:function:AsyncProcess-prd" />
      <env name="AWS_DEFAULT_REGION" value="eu-west-1" />
      <env name="AWS_PROFILE" value="resa" />
      <env name="BornesRechargeTable" value="BornesRechargeTable_prd" />
      <env name="BUCKET" value="my-resa-api-production" />
      <env name="BUCKET_NAME" value="my-resa-api-production-uploads" />
      <env name="BUCKET_NAME_SHAREPOINT_UPLOADS" value="my-resa-api-production-sharepoint-uploads" />
      <env name="BUCKET_NAME_UPLOADS" value="my-resa-api-production-uploads" />
      <env name="CURL_CA_BUNDLE" value="" />
      <env name="DIGACERT_STORE_CERT_LAMBDA_ARN" value="arn:aws:lambda:eu-west-1:204480941676:function:digacertStoreCert-prd" />
      <env name="DOMAIN_NAME" value="api.resa.be" />
      <env name="DossierConfActionsTable" value="DossierConfirmationActions_prd" />
      <env name="DYNAMODB" value="MyResaUser_prd" />
      <env name="DYNAMODB_TOKEN_INVALIDATION" value="TokenInvalidation" />
      <env name="EmailSendsTable" value="EmailSends" />
      <env name="ENV_FILE" value="resources/3.11.0/env.production.json" />
      <env name="ENV_MESSAGE_LOG_BUCKET" value="env-message-logs-production" />
      <env name="FORMULAIRE" value="MyResaFormulaire_prd" />
      <env name="FormulairesTable" value="FormulairesTable_prd" />
      <env name="FunctionName" value="sqlDriven-production" />
      <env name="HTML_TO_PDF_LAMBDA" value="arn:aws:lambda:eu-west-1:204480941676:function:transformHtmlToPdf-production" />
      <env name="IS_PROD" value="true" />
      <env name="JWK_BUCKET" value="my-resa-api-production" />
      <env name="JWK_KEY" value="resources/AD_publickey.json" />
      <env name="LOCAL" value="true" />
      <env name="MAPPING_BUCKET_FILE" value="resources/3.11.0/linking.json" />
      <env name="MAPPING_BUCKET_NAME" value="my-resa-api-production" />
      <env name="MD_TABLE" value="MyResaMD_prd" />
      <env name="MOLLIE_TOKEN" value="MyResaAPI/Mollie/PRD" />
      <env name="PANNE_DYNAMODB_TABLE" value="UserPanneSubscription_prd" />
      <env name="PANNE_LOCATION_TABLE" value="PanneLocation_prd" />
      <env name="PASSTHROUGH_LAMBDA_ARN" value="arn:aws:lambda:eu-west-1:204480941676:function:passThrough-production" />
      <env name="PayementTable" value="PayementTable_production" />
      <env name="SHAREPOINT_SECRET" value="MyResaAPI/Sharepoint/PRD" />
      <env name="SharepointUpload_LAMBDA_ARN" value="arn:aws:lambda:eu-west-1:204480941676:function:sharepointUpload-production:35" />
      <env name="SMS_CODES" value="MyResaSMSCodes_prd" />
      <env name="STAGE" value="production" />
      <env name="STAGE_TAG" value="PRD" />
      <env name="USER_ACTIVATION_SQS_URL" value="https://sqs.eu-west-1.amazonaws.com/204480941676/userActivation-prd_queue" />
      <env name="USER_SYNC_LAMBDA_ARN" value="arn:aws:lambda:eu-west-1:204480941676:function:userSynchronisation-prd" />
      <env name="LANG" value="FR" />
      <env name="CACHE_BASE_DIR" value="/tmp/MyResa" />
      <env name="UserEnergyProfiles" value="UserEnergyProfiles_PRD" />
      <env name="SmartConsoAlerts" value="SmartConsoAlerts_PRD" />
      <env name="HistoricAlerts" value="HistoricAlerts_PRD" />
    </envs>
    <option name="SDK_HOME" value="" />
    <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
    <option name="IS_MODULE_SDK" value="true" />
    <option name="ADD_CONTENT_ROOTS" value="true" />
    <option name="ADD_SOURCE_ROOTS" value="true" />
    <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
    <EXTENSION ID="software.aws.toolkits.jetbrains.core.execution.PythonAwsConnectionExtension">
      <option name="credential" />
      <option name="region" />
      <option name="useCurrentConnection" value="false" />
    </EXTENSION>
    <option name="SCRIPT_NAME" value="$PROJECT_DIR$/tools/local_flask/gateway.py" />
    <option name="PARAMETERS" value="" />
    <option name="SHOW_COMMAND_LINE" value="false" />
    <option name="EMULATE_TERMINAL" value="true" />
    <option name="MODULE_MODE" value="false" />
    <option name="REDIRECT_INPUT" value="false" />
    <option name="INPUT_FILE" value="" />
    <method v="2" />
  </configuration>
</component>