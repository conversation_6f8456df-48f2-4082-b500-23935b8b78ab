<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="gateway QLA/stg" type="PythonConfigurationType" factoryName="Python" folderName="Gateway">
    <module name="AWS_API Web" />
    <option name="ENV_FILES" value="" />
    <option name="INTERPRETER_OPTIONS" value="" />
    <option name="PARENT_ENVS" value="true" />
    <envs>
      <env name="AD_SECRET" value="MyResaAPI/ActiveDirectory/QLA" />
      <env name="ADFS_SECRET" value="MyResaAPI/ADFS/QLA" />
      <env name="ADFS_WEB" value="MyResaWeb/ADFS/QLA" />
      <env name="API_REGION" value="eu-west-1" />
      <env name="API_STAGE" value="4.0.5" />
      <env name="API_URL" value="https://127.0.0.1:8011" />
      <env name="API_VERSION" value="4.0.5" />
      <env name="AsynchroneProcessesTable" value="AsynchroneProcesses_qla" />
      <env name="AsyncProcessLambda" value="arn:aws:lambda:eu-west-1:204480941676:function:AsyncProcess-qla" />
      <env name="AWS_DEFAULT_REGION" value="eu-west-1" />
      <env name="AWS_PROFILE" value="resa" />
      <env name="BornesRechargeTable" value="BornesRechargeTable_qla" />
      <env name="BUCKET" value="my-resa-api-qla" />
      <env name="BUCKET_NAME" value="my-resa-api-qla-uploads" />
      <env name="BUCKET_NAME_SHAREPOINT_UPLOADS" value="my-resa-api-qla-sharepoint-uploads" />
      <env name="BUCKET_NAME_UPLOADS" value="my-resa-api-qla-uploads" />
      <env name="CACHE_BASE_DIR" value="/tmp/MyResa" />
      <env name="CURL_CA_BUNDLE" value="" />
      <env name="DIGACERT_STORE_CERT_LAMBDA_ARN" value="arn:aws:lambda:eu-west-1:204480941676:function:digacertStoreCert-qla" />
      <env name="DOMAIN_NAME" value="api-qla.resa.be" />
      <env name="DossierConfActionsTable" value="DossierConfirmationActions_dev" />
      <env name="DYNAMODB" value="MyResaUser_qla" />
      <env name="DYNAMODB_TOKEN_INVALIDATION" value="TokenInvalidation" />
      <env name="EmailSendsTable" value="EmailSends" />
      <env name="ENV_FILE" value="resources/4.0.5/env.qla.json" />
      <env name="ENV_MESSAGE_LOG_BUCKET" value="env-message-logs-qla" />
      <env name="FORMULAIRE" value="MyResaFormulaire_qla" />
      <env name="FormulairesTable" value="FormulairesTable_qla" />
      <env name="FunctionName" value="passThrough-qla" />
      <env name="HistoricAlerts" value="HistoricAlerts_QLA" />
      <env name="HTML_TO_PDF_LAMBDA" value="arn:aws:lambda:eu-west-1:204480941676:function:transformHtmlToPdf-qla" />
      <env name="IS_PROD" value="true" />
      <env name="JWK_BUCKET" value="my-resa-api-qla" />
      <env name="JWK_KEY" value="resources/AD_publickey.json" />
      <env name="LOCAL" value="true" />
      <env name="MAPPING_BUCKET_FILE" value="resources/4.0.5/linking.json" />
      <env name="MAPPING_BUCKET_NAME" value="my-resa-api-qla" />
      <env name="MD_TABLE" value="MyResaMD_qla" />
      <env name="MOLLIE_TOKEN" value="MyResaAPI/Mollie/DEV" />
      <env name="PANNE_DYNAMODB_TABLE" value="UserPanneSubscription_qla" />
      <env name="PANNE_LOCATION_TABLE" value="PanneLocation_qla" />
      <env name="PASSTHROUGH_LAMBDA_ARN" value="arn:aws:lambda:eu-west-1:204480941676:function:passThrough-qla" />
      <env name="PayementTable" value="PayementTable_qla" />
      <env name="PYTHONUNBUFFERED" value="1" />
      <env name="SALESFORCE_QUEUE_URL" value="https://sqs.eu-west-1.amazonaws.com/204480941676/sendToSalesForce-qla_queue" />
      <env name="SHAREPOINT_SECRET" value="MyResaAPI/Sharepoint/QLA" />
      <env name="SharepointUpload_LAMBDA_ARN" value="arn:aws:lambda:eu-west-1:204480941676:function:sharepointUpload-qla" />
      <env name="SmartConsoAlerts" value="SmartConsoAlerts_QLA" />
      <env name="SMS_CODES" value="MyResaSMSCodes_qla" />
      <env name="STAGE" value="qla" />
      <env name="STAGE_TAG" value="QLA" />
      <env name="USER_ACTIVATION_SQS_URL" value="https://sqs.eu-west-1.amazonaws.com/204480941676/userActivation-qla_queue" />
      <env name="USER_SYNC_LAMBDA_ARN" value="arn:aws:lambda:eu-west-1:204480941676:function:userSynchronisation-qla" />
      <env name="UserEnergyProfiles" value="UserEnergyProfiles_QLA" />
      <env name="LANG" value="FR" />
    </envs>
    <option name="SDK_HOME" value="" />
    <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
    <option name="IS_MODULE_SDK" value="true" />
    <option name="ADD_CONTENT_ROOTS" value="true" />
    <option name="ADD_SOURCE_ROOTS" value="true" />
    <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
    <EXTENSION ID="software.aws.toolkits.jetbrains.core.execution.PythonAwsConnectionExtension">
      <option name="credential" />
      <option name="region" />
      <option name="useCurrentConnection" value="false" />
    </EXTENSION>
    <option name="SCRIPT_NAME" value="$PROJECT_DIR$/tools/local_flask/gateway.py" />
    <option name="PARAMETERS" value="" />
    <option name="SHOW_COMMAND_LINE" value="false" />
    <option name="EMULATE_TERMINAL" value="true" />
    <option name="MODULE_MODE" value="false" />
    <option name="REDIRECT_INPUT" value="false" />
    <option name="INPUT_FILE" value="" />
    <method v="2" />
  </configuration>
</component>