import os

import boto3
from boto3.dynamodb.conditions import Attr, Key
from tqdm import tqdm

from utils.aws_utils import scan_all_generator

dynamodb = boto3.resource("dynamodb")
table = dynamodb.Table(os.environ["DYNAMODB"])

if __name__ == "__main__":
    users_gen = scan_all_generator(
        os.environ["DYNAMODB"],
        {
            "ProjectionExpression": "uid",
            "FilterExpression": ~(
                Key("uid").begins_with("ghost_")
                | Key("uid").begins_with("Tes.User.")
                | Key("uid").begins_with("Tes.Test.")
                | Key("uid").eq("DXC.Test1.1RMDk")  # Exclude PPP used by Atrias
                | Key("uid").eq("DXC.Test2.cHmO4")  # Exclude PPP used by Atrias
            )
            & Attr("valide").eq(True)
            & Attr("bp").exists(),
        },
    )

    with tqdm() as progress:
        for user in users_gen:
            table.update_item(
                Key={"uid": user["uid"]},
                UpdateExpression="SET #pref.#racc_pref = :racc_pref_val",
                ExpressionAttributeNames={
                    "#pref": "preferences",
                    "#racc_pref": "com_dossier_racc_mail",
                },
                ExpressionAttributeValues={":racc_pref_val": True},
            )
            progress.update()
