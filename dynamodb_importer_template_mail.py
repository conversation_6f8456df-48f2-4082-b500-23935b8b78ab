import json

import boto3

# Assurez-vous d'avoir configuré vos credentials AWS (par exemple, via aws-cli)
dynamodb = boto3.resource("dynamodb")
table_name = "WS68_TEMPLATE_QTA"
table = dynamodb.Table(table_name)


# Fonction pour vider la table
def empty_table():
    # Scannez tous les items
    items = table.scan()["Items"]

    # Supprimez chaque item
    for item in items:
        table.delete_item(Key={"Id": item["Id"]})


# Fonction pour ajouter des items à la table
def put_items(data):
    for key, value in data.items():
        value["Id"] = key
        table.put_item(Item=value)


if __name__ == "__main__":
    # Vider la table
    empty_table()

    # Charger les données depuis le fichier JSON
    with open("../resources/template_config.json", "r") as file:
        data = json.load(file)

    # Ajouter les données à la table
    put_items(data)
