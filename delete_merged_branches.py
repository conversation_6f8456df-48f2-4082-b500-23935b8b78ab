#!/usr/bin/env python3

import subprocess


def get_current_branch() -> str:
    # Run the git command to get the current branch
    result = subprocess.run(["git", "branch", "--show-current"], capture_output=True, text=True)
    return result.stdout.strip()


def list_merged_branches() -> list[str]:
    current_branch = get_current_branch()

    # Run the git command to list remote and local branches that are merged
    result = subprocess.run(
        ["git", "branch", "-a", "--sort=tree", "--merged", current_branch],
        capture_output=True,
        text=True,
    )
    branches = [branch.strip() for branch in result.stdout.strip().split("\n") if not branch.endswith(current_branch)]

    return branches


def delete_branches(branches: list[str]):
    # Ask for confirmation to delete branches
    print("The following branches will be deleted:")
    for branch in branches:
        print(f"- {branch}")

    confirmation = input("Do you want to delete these branches? (y/n): ").lower()

    if confirmation == "y":
        # Delete branches
        for branch in branches:
            if branch.startswith("remotes/"):
                _, origin, branch_name = branch.split("/", 2)
                subprocess.run(["git", "push", origin, "--delete", branch_name])
            else:
                subprocess.run(["git", "branch", "-d", branch])
        print("Branches deleted successfully.")
    else:
        print("Operation canceled.")


if __name__ == "__main__":
    # Get merged branches
    merged_branches = list_merged_branches()

    # Remove exception branch
    deploy_branch_prefix = [
        "deploy/",
        "remotes/origin/deploy/",
        "remotes/origin_aws/",
    ]
    deploy_branch_suffix = [
        "main",
    ]
    merged_branches = list(
        filter(
            lambda branch_name: not any(branch_name.startswith(prefix) for prefix in deploy_branch_prefix),
            merged_branches,
        )
    )
    merged_branches = list(
        filter(
            lambda branch_name: not any(branch_name.endswith(suffix) for suffix in deploy_branch_suffix),
            merged_branches,
        )
    )

    if not merged_branches:
        print("No merged branches found.")
    else:
        delete_branches(merged_branches)
