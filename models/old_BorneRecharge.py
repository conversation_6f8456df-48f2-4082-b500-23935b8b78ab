from dataclasses import dataclass
from decimal import Decimal
from typing import Optional, Union

from dataclasses_json import dataclass_json, LetterCase

from utils.errors import BadRequestError


@dataclass_json(letter_case=LetterCase.PASCAL)
@dataclass
class Demandeur:
    nom: str
    prenom: str
    email: str
    telephone: str


@dataclass_json(letter_case=LetterCase.PASCAL)
@dataclass
class Installateur:
    nom: Optional[str] = None
    email: Optional[str] = None
    telephone: Optional[str] = None


@dataclass_json(letter_case=LetterCase.PASCAL)
@dataclass
class Entreprise:
    numero: Optional[str] = None
    nom: Optional[str] = None
    acronyme: Optional[str] = None
    forme_juridique: Optional[str] = None


@dataclass_json(letter_case=LetterCase.PASCAL)
@dataclass
class Adresse:
    rue: str
    numero: str
    code_postal: str
    commune: str
    pays: str


@dataclass_json(letter_case=LetterCase.PASCAL)
@dataclass
class Borne:
    adresse: Adresse
    ean: str
    date: str
    marque: str
    modele: str
    utilisation: str
    bidirectionnelle: str
    serial: str
    puissance: Union[str, float, Decimal]
    photo: Optional[str] = None

    def __post_init__(self) -> None:
        self.puissance = Decimal(str(self.puissance).replace(",", "."))
        if not (0 < self.puissance < 1000):
            raise BadRequestError(
                "Invalid value for 'Puissance'. The value should be between 0 and 1000.",
                error_code="BORNE_PUISSANCE_BAD_VALUE",
            )


@dataclass_json(letter_case=LetterCase.PASCAL)
@dataclass
class old_BorneRecharge:
    type_demande: str
    demandeur: Demandeur
    borne: Borne
    ean: Optional[str] = None
    uuid: Optional[str] = None
    installateur: Optional[Installateur] = None
    entreprise: Optional[Entreprise] = None
    date_creation: Optional[str] = None
